# 完整交易字段列表

## 1. 基础价格字段

### 基纳价格
```xml
<price>1000</price>
```
- **说明**: 物品的基础价格，使用基纳货币
- **示例值**: 5, 50, 150, 850, 1000, 1274950 等

## 2. 额外货币字段

### 额外货币物品
```xml
<extra_currency_item>coin_combineskill_01</extra_currency_item>
<extra_currency_item_count>2</extra_currency_item_count>
```
- **说明**: 除了基纳外，还需要额外的货币物品
- **常见货币类型**:
  - `coin_combineskill_01` - 技能合成币
  - `event_chaos_wingwar_coin_02` - 混沌翼战活动币
  - `junk_master_recipe_quest_40a` - 40级任务配方材料
  - `junk_master_recipe_quest_50a` - 50级任务配方材料
  - `black_aion_toll_01` - 黑色永恒币
  - `world_promotion_vip_continue_c` - VIP续费币

## 3. 交易兑换字段

### 完整兑换列表结构
```xml
<trade_in_item_list>
    <data>
        <trade_in_item>black_aion_toll_31</trade_in_item>
        <trade_in_item_count>10000</trade_in_item_count>
    </data>
</trade_in_item_list>
```

### 多物品兑换结构
```xml
<trade_in_item_list>
    <data>
        <trade_in_item>COIN_01</trade_in_item>
        <trade_in_item_count>15</trade_in_item_count>
    </data>
    <data>
        <trade_in_item>COIN_02</trade_in_item>
        <trade_in_item_count>10</trade_in_item_count>
    </data>
    <data>
        <trade_in_item>COIN_03</trade_in_item>
        <trade_in_item_count>5</trade_in_item_count>
    </data>
</trade_in_item_list>
```

### 发现的兑换币类型
- `black_aion_toll_31` - 黑色永恒币31 (数量: 1, 2, 50, 100, 200, 300, 500, 1000, 1500, 2000, 3000, 4000, 5000, 6000, 30000, 4900)
- `black_aion_toll_32` - 黑色永恒币32 (数量: 50, 30000)
- `COIN_01` - 一级币 (数量: 15)
- `COIN_02` - 二级币 (数量: 10)
- `COIN_03` - 三级币 (数量: 5)
- `junk_armor_parts_token_dark` - 黑暗防具部件令牌 (数量: 2)
- `junk_armor_parts_token_light` - 光明防具部件令牌 (数量: 2)
- `world_event_ticket_exchange_05` - 世界事件兑换券05 (数量: 10, 50)

## 4. 交易权限字段

### 交易权限
```xml
<can_exchange>TRUE</can_exchange>
<can_sell_to_npc>TRUE</can_sell_to_npc>
```

### 仓库权限
```xml
<can_deposit_to_character_warehouse>TRUE</can_deposit_to_character_warehouse>
<can_deposit_to_account_warehouse>TRUE</can_deposit_to_account_warehouse>
<can_deposit_to_guild_warehouse>TRUE</can_deposit_to_guild_warehouse>
```

### 掉落权限
```xml
<item_drop_permitted>FALSE</item_drop_permitted>
```

## 5. 物品属性字段

### 基础属性
```xml
<item_type>normal</item_type>
<quality>common</quality>
<level>1</level>
<lore>FALSE</lore>
<cash_item>2</cash_item>
```

### 绑定属性
```xml
<soul_bind>FALSE</soul_bind>
<remove_when_logout>FALSE</remove_when_logout>
```

### 操作权限
```xml
<can_split>TRUE</can_split>
<breakable>TRUE</breakable>
<disassembly_item>0</disassembly_item>
<can_ap_extraction>FALSE</can_ap_extraction>
```

### 种族和性别限制
```xml
<race_permitted>pc_light pc_dark</race_permitted>
<gender_permitted>all</gender_permitted>
```

## 6. 职业限制字段

```xml
<warrior>1</warrior>
<scout>1</scout>
<mage>1</mage>
<cleric>1</cleric>
<engineer>1</engineer>
<artist>1</artist>
<fighter>1</fighter>
<knight>1</knight>
<assassin>1</assassin>
<ranger>1</ranger>
<wizard>1</wizard>
<elementalist>1</elementalist>
<chanter>1</chanter>
<priest>1</priest>
<gunner>1</gunner>
<bard>1</bard>
<rider>1</rider>
```

## 7. 完整物品交易示例

### 示例1: 基础基纳购买
```xml
<item>
    <id>141000001</id>
    <name>stigma_shard</name>
    <price>10</price>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>TRUE</can_sell_to_npc>
    <item_drop_permitted>FALSE</item_drop_permitted>
</item>
```

### 示例2: 混合货币购买
```xml
<item>
    <id>123456789</id>
    <name>skill_combine_item</name>
    <price>1000</price>
    <extra_currency_item>coin_combineskill_01</extra_currency_item>
    <extra_currency_item_count>2</extra_currency_item_count>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>
```

### 示例3: 兑换购买
```xml
<item>
    <id>987654321</id>
    <name>premium_item</name>
    <trade_in_item_list>
        <data>
            <trade_in_item>black_aion_toll_31</trade_in_item>
            <trade_in_item_count>10000</trade_in_item_count>
        </data>
    </trade_in_item_list>
    <can_exchange>FALSE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>
```

### 示例4: 复杂兑换购买
```xml
<item>
    <id>555666777</id>
    <name>multi_currency_item</name>
    <price>500</price>
    <extra_currency_item>black_aion_toll_01</extra_currency_item>
    <extra_currency_item_count>1</extra_currency_item_count>
    <trade_in_item_list>
        <data>
            <trade_in_item>black_aion_toll_31</trade_in_item>
            <trade_in_item_count>100</trade_in_item_count>
        </data>
    </trade_in_item_list>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>
```

## 8. 特殊交易字段

### 激活技能
```xml
<activation_skill>cash_food_l_shape_herlock_phy_01</activation_skill>
```

### 激活模式
```xml
<activation_mode>Both</activation_mode>
```

### 额外库存
```xml
<extra_inventory>1</extra_inventory>
```

### 最大堆叠数量
```xml
<max_stack_count>1</max_stack_count>
```

## 9. 字段值说明

### 布尔值字段
- `TRUE` / `FALSE` - 用于权限控制字段

### 数值字段
- 价格: 整数，表示基纳数量
- 数量: 整数，表示物品数量
- 等级: 整数，表示物品等级

### 文本字段
- 物品类型: `normal`, `quest`, `cash` 等
- 品质: `common`, `rare`, `unique`, `legendary` 等
- 种族: `pc_light`, `pc_dark`, `pc_light pc_dark`
- 性别: `all`, `male`, `female` 