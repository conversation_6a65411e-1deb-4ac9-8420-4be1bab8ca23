using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace ServerConversionTest;

/// <summary>
/// 服务端物品转换服务
/// 负责将客户端物品数据转换为服务端格式并自动分类保存
/// </summary>
public class ServerItemConversionService
{
    private readonly ILogger _logger;
    private readonly XmlService _xmlService;

    public ServerItemConversionService(ILogger logger, XmlService xmlService)
    {
        _logger = logger;
        _xmlService = xmlService;
    }

    /// <summary>
    /// 自动转换并保存客户端物品到服务端格式
    /// </summary>
    public async Task<ConversionResult> ConvertAndSaveToServerAsync(List<ClientItem> clientItems, string serverBasePath)
    {
        try
        {
            _logger.LogInformation("开始转换 {Count} 个客户端物品到服务端格式", clientItems.Count);

            var result = new ConversionResult();

            // 按ID排序
            var sortedItems = clientItems.OrderBy(item => item.Id).ToList();

            // 按类型分类
            var weaponItems = new List<ClientItem>();
            var armorItems = new List<ClientItem>();
            var etcItems = new List<ClientItem>();

            foreach (var item in sortedItems)
            {
                var category = DetermineItemCategory(item);
                switch (category)
                {
                    case ItemCategory.Weapon:
                        weaponItems.Add(item);
                        break;
                    case ItemCategory.Armor:
                        armorItems.Add(item);
                        break;
                    case ItemCategory.Etc:
                        etcItems.Add(item);
                        break;
                }
            }

            // 转换并保存到对应的服务端文件
            if (weaponItems.Count > 0)
            {
                var weaponServerPath = Path.Combine(serverBasePath, "item_weapons.xml");
                await ConvertAndSaveItemCategory(weaponItems, weaponServerPath, "item_weapons", "weapon_templates");
                result.WeaponCount = weaponItems.Count;
                result.WeaponFilePath = weaponServerPath;
            }

            if (armorItems.Count > 0)
            {
                var armorServerPath = Path.Combine(serverBasePath, "item_armors.xml");
                await ConvertAndSaveItemCategory(armorItems, armorServerPath, "item_armors", "armor_templates");
                result.ArmorCount = armorItems.Count;
                result.ArmorFilePath = armorServerPath;
            }

            if (etcItems.Count > 0)
            {
                var etcServerPath = Path.Combine(serverBasePath, "item_etc.xml");
                await ConvertAndSaveItemCategory(etcItems, etcServerPath, "item_etc", "item_templates");
                result.EtcCount = etcItems.Count;
                result.EtcFilePath = etcServerPath;
            }

            result.TotalCount = sortedItems.Count;
            result.Success = true;

            _logger.LogInformation("转换完成: 武器 {WeaponCount}, 防具 {ArmorCount}, 杂货 {EtcCount}", 
                result.WeaponCount, result.ArmorCount, result.EtcCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换客户端物品到服务端格式时出错");
            return new ConversionResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    /// <summary>
    /// 确定物品类别
    /// </summary>
    private ItemCategory DetermineItemCategory(ClientItem item)
    {
        // 根据武器类型判断
        if (!string.IsNullOrEmpty(item.WeaponType))
        {
            return ItemCategory.Weapon;
        }

        // 根据防具类型判断
        if (!string.IsNullOrEmpty(item.ArmorType))
        {
            return ItemCategory.Armor;
        }

        // 根据物品类型判断
        if (!string.IsNullOrEmpty(item.ItemType))
        {
            switch (item.ItemType.ToLower())
            {
                case "weapon":
                    return ItemCategory.Weapon;
                case "armor":
                    return ItemCategory.Armor;
                case "normal":
                case "quest":
                case "cash":
                case "material":
                case "consumable":
                    return ItemCategory.Etc;
            }
        }

        // 根据名称模式判断
        var name = item.Name.ToLower();
        if (name.Contains("sword") || name.Contains("dagger") || name.Contains("bow") || 
            name.Contains("staff") || name.Contains("mace") || name.Contains("gun") ||
            name.Contains("weapon"))
        {
            return ItemCategory.Weapon;
        }

        if (name.Contains("armor") || name.Contains("torso") || name.Contains("leg") || 
            name.Contains("head") || name.Contains("shoulder") || name.Contains("glove") ||
            name.Contains("shoes"))
        {
            return ItemCategory.Armor;
        }

        // 默认为杂货
        return ItemCategory.Etc;
    }

    /// <summary>
    /// 转换并保存特定类别的物品
    /// </summary>
    private async Task ConvertAndSaveItemCategory(List<ClientItem> items, string serverFilePath, 
        string rootElementName, string templateElementName)
    {
        try
        {
            _logger.LogInformation("转换 {Count} 个物品到服务端文件: {FilePath}", items.Count, serverFilePath);

            var xmlDoc = new XmlDocument();

            // 创建XML声明
            var xmlDeclaration = xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmlDoc.AppendChild(xmlDeclaration);

            // 创建根元素
            var rootElement = xmlDoc.CreateElement(rootElementName);
            xmlDoc.AppendChild(rootElement);

            // 创建模板容器元素
            var templatesElement = xmlDoc.CreateElement(templateElementName);
            rootElement.AppendChild(templatesElement);

            // 转换每个物品
            foreach (var clientItem in items.OrderBy(i => i.Id))
            {
                var serverItemElement = ConvertClientItemToServerFormat(xmlDoc, clientItem);
                templatesElement.AppendChild(serverItemElement);
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(serverFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 保存文件
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "\t",
                Encoding = System.Text.Encoding.UTF8
            };

            using var writer = XmlWriter.Create(serverFilePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存服务端文件: {FilePath}", serverFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存服务端文件时出错: {FilePath}", serverFilePath);
            throw;
        }
    }

    /// <summary>
    /// 将客户端物品转换为服务端格式
    /// </summary>
    private XmlElement ConvertClientItemToServerFormat(XmlDocument xmlDoc, ClientItem clientItem)
    {
        var category = DetermineItemCategory(clientItem);
        
        return category switch
        {
            ItemCategory.Weapon => ConvertToWeaponTemplate(xmlDoc, clientItem),
            ItemCategory.Armor => ConvertToArmorTemplate(xmlDoc, clientItem),
            ItemCategory.Etc => ConvertToItemTemplate(xmlDoc, clientItem),
            _ => ConvertToItemTemplate(xmlDoc, clientItem)
        };
    }

    /// <summary>
    /// 转换为武器模板
    /// </summary>
    private XmlElement ConvertToWeaponTemplate(XmlDocument xmlDoc, ClientItem clientItem)
    {
        var weaponElement = xmlDoc.CreateElement("weapon_template");
        
        // 基础属性
        AddServerElement(xmlDoc, weaponElement, "id", clientItem.Id.ToString());
        AddServerElement(xmlDoc, weaponElement, "name", clientItem.Name);
        AddServerElement(xmlDoc, weaponElement, "desc", clientItem.Desc);
        AddServerElement(xmlDoc, weaponElement, "weapon_type", clientItem.WeaponType);
        AddServerElement(xmlDoc, weaponElement, "item_type", clientItem.ItemType);
        AddServerElement(xmlDoc, weaponElement, "material", clientItem.Material);
        AddServerElement(xmlDoc, weaponElement, "price", clientItem.Price.ToString());
        AddServerElement(xmlDoc, weaponElement, "max_stack_count", clientItem.MaxStackCount.ToString());
        
        // 武器专用属性
        if (!string.IsNullOrEmpty(clientItem.EquipmentSlots))
            AddServerElement(xmlDoc, weaponElement, "equipment_slots", clientItem.EquipmentSlots);

        // 添加通用属性
        AddCommonServerAttributes(xmlDoc, weaponElement, clientItem);

        return weaponElement;
    }

    /// <summary>
    /// 转换为防具模板
    /// </summary>
    private XmlElement ConvertToArmorTemplate(XmlDocument xmlDoc, ClientItem clientItem)
    {
        var armorElement = xmlDoc.CreateElement("armor_template");
        
        // 基础属性
        AddServerElement(xmlDoc, armorElement, "id", clientItem.Id.ToString());
        AddServerElement(xmlDoc, armorElement, "name", clientItem.Name);
        AddServerElement(xmlDoc, armorElement, "desc", clientItem.Desc);
        AddServerElement(xmlDoc, armorElement, "armor_type", clientItem.ArmorType);
        AddServerElement(xmlDoc, armorElement, "item_type", clientItem.ItemType);
        AddServerElement(xmlDoc, armorElement, "material", clientItem.Material);
        AddServerElement(xmlDoc, armorElement, "price", clientItem.Price.ToString());
        AddServerElement(xmlDoc, armorElement, "max_stack_count", clientItem.MaxStackCount.ToString());

        // 防具专用属性
        if (clientItem.PhysicalDefend > 0)
            AddServerElement(xmlDoc, armorElement, "physical_defend", clientItem.PhysicalDefend.ToString());
        if (clientItem.MagicalResist > 0)
            AddServerElement(xmlDoc, armorElement, "magical_resist", clientItem.MagicalResist.ToString());
        if (clientItem.Dodge > 0)
            AddServerElement(xmlDoc, armorElement, "dodge", clientItem.Dodge.ToString());

        // 添加通用属性
        AddCommonServerAttributes(xmlDoc, armorElement, clientItem);

        return armorElement;
    }

    /// <summary>
    /// 转换为物品模板
    /// </summary>
    private XmlElement ConvertToItemTemplate(XmlDocument xmlDoc, ClientItem clientItem)
    {
        var itemElement = xmlDoc.CreateElement("item_template");
        
        // 基础属性
        AddServerElement(xmlDoc, itemElement, "id", clientItem.Id.ToString());
        AddServerElement(xmlDoc, itemElement, "name", clientItem.Name);
        AddServerElement(xmlDoc, itemElement, "desc", clientItem.Desc);
        AddServerElement(xmlDoc, itemElement, "item_type", clientItem.ItemType);
        AddServerElement(xmlDoc, itemElement, "material", clientItem.Material);
        AddServerElement(xmlDoc, itemElement, "price", clientItem.Price.ToString());
        AddServerElement(xmlDoc, itemElement, "max_stack_count", clientItem.MaxStackCount.ToString());

        // 添加通用属性
        AddCommonServerAttributes(xmlDoc, itemElement, clientItem);

        return itemElement;
    }

    /// <summary>
    /// 添加通用服务端属性
    /// </summary>
    private void AddCommonServerAttributes(XmlDocument xmlDoc, XmlElement element, ClientItem clientItem)
    {
        // 品质和等级
        AddServerElement(xmlDoc, element, "quality", clientItem.Quality);
        AddServerElement(xmlDoc, element, "level", clientItem.Level.ToString());
        AddServerElement(xmlDoc, element, "lore", clientItem.Lore ? "TRUE" : "FALSE");

        // 交易权限
        AddServerElement(xmlDoc, element, "can_exchange", clientItem.CanExchange ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "can_sell_to_npc", clientItem.CanSellToNpc ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "can_deposit_to_character_warehouse", clientItem.CanDepositToCharacterWarehouse ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "can_deposit_to_account_warehouse", clientItem.CanDepositToAccountWarehouse ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "can_deposit_to_guild_warehouse", clientItem.CanDepositToGuildWarehouse ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "item_drop_permitted", clientItem.ItemDropPermitted ? "TRUE" : "FALSE");

        // 绑定属性
        AddServerElement(xmlDoc, element, "breakable", clientItem.Breakable ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "soul_bind", clientItem.SoulBind ? "TRUE" : "FALSE");
        AddServerElement(xmlDoc, element, "remove_when_logout", clientItem.RemoveWhenLogout ? "TRUE" : "FALSE");

        // 种族和性别限制
        if (!string.IsNullOrEmpty(clientItem.RacePermitted))
            AddServerElement(xmlDoc, element, "race_permitted", clientItem.RacePermitted);
        if (!string.IsNullOrEmpty(clientItem.GenderPermitted))
            AddServerElement(xmlDoc, element, "gender_permitted", clientItem.GenderPermitted);

        // 职业限制
        AddClassRestrictions(xmlDoc, element, clientItem);

        // 属性加成
        AddBonusAttributes(xmlDoc, element, clientItem);

        // 交易相关
        if (!string.IsNullOrEmpty(clientItem.ExtraCurrencyItem))
        {
            AddServerElement(xmlDoc, element, "extra_currency_item", clientItem.ExtraCurrencyItem);
            AddServerElement(xmlDoc, element, "extra_currency_item_count", clientItem.ExtraCurrencyItemCount.ToString());
        }

        // 兑换物品列表
        if (clientItem.TradeInItemList != null && clientItem.TradeInItemList.Count > 0)
        {
            var tradeInListElement = xmlDoc.CreateElement("trade_in_item_list");
            foreach (var tradeInItem in clientItem.TradeInItemList)
            {
                var dataElement = xmlDoc.CreateElement("data");
                AddServerElement(xmlDoc, dataElement, "trade_in_item", tradeInItem.TradeInItem);
                AddServerElement(xmlDoc, dataElement, "trade_in_item_count", tradeInItem.TradeInItemCount.ToString());
                tradeInListElement.AppendChild(dataElement);
            }
            element.AppendChild(tradeInListElement);
        }

        // 其他特殊属性
        if (!string.IsNullOrEmpty(clientItem.ActivationSkill))
            AddServerElement(xmlDoc, element, "activation_skill", clientItem.ActivationSkill);
        if (!string.IsNullOrEmpty(clientItem.ActivationMode))
            AddServerElement(xmlDoc, element, "activation_mode", clientItem.ActivationMode);
        if (clientItem.ExtraInventory > 0)
            AddServerElement(xmlDoc, element, "extra_inventory", clientItem.ExtraInventory.ToString());
        if (clientItem.DisassemblyItem > 0)
            AddServerElement(xmlDoc, element, "disassembly_item", clientItem.DisassemblyItem.ToString());
    }

    /// <summary>
    /// 添加职业限制
    /// </summary>
    private void AddClassRestrictions(XmlDocument xmlDoc, XmlElement element, ClientItem clientItem)
    {
        if (clientItem.Warrior > 0) AddServerElement(xmlDoc, element, "warrior", clientItem.Warrior.ToString());
        if (clientItem.Scout > 0) AddServerElement(xmlDoc, element, "scout", clientItem.Scout.ToString());
        if (clientItem.Mage > 0) AddServerElement(xmlDoc, element, "mage", clientItem.Mage.ToString());
        if (clientItem.Cleric > 0) AddServerElement(xmlDoc, element, "cleric", clientItem.Cleric.ToString());
        if (clientItem.Engineer > 0) AddServerElement(xmlDoc, element, "engineer", clientItem.Engineer.ToString());
        if (clientItem.Artist > 0) AddServerElement(xmlDoc, element, "artist", clientItem.Artist.ToString());
        if (clientItem.Fighter > 0) AddServerElement(xmlDoc, element, "fighter", clientItem.Fighter.ToString());
        if (clientItem.Knight > 0) AddServerElement(xmlDoc, element, "knight", clientItem.Knight.ToString());
        if (clientItem.Assassin > 0) AddServerElement(xmlDoc, element, "assassin", clientItem.Assassin.ToString());
        if (clientItem.Ranger > 0) AddServerElement(xmlDoc, element, "ranger", clientItem.Ranger.ToString());
        if (clientItem.Wizard > 0) AddServerElement(xmlDoc, element, "wizard", clientItem.Wizard.ToString());
        if (clientItem.Elementalist > 0) AddServerElement(xmlDoc, element, "elementalist", clientItem.Elementalist.ToString());
        if (clientItem.Chanter > 0) AddServerElement(xmlDoc, element, "chanter", clientItem.Chanter.ToString());
        if (clientItem.Priest > 0) AddServerElement(xmlDoc, element, "priest", clientItem.Priest.ToString());
        if (clientItem.Gunner > 0) AddServerElement(xmlDoc, element, "gunner", clientItem.Gunner.ToString());
        if (clientItem.Bard > 0) AddServerElement(xmlDoc, element, "bard", clientItem.Bard.ToString());
        if (clientItem.Rider > 0) AddServerElement(xmlDoc, element, "rider", clientItem.Rider.ToString());
    }

    /// <summary>
    /// 添加属性加成
    /// </summary>
    private void AddBonusAttributes(XmlDocument xmlDoc, XmlElement element, ClientItem clientItem)
    {
        if (!string.IsNullOrEmpty(clientItem.BonusAttr1))
            AddServerElement(xmlDoc, element, "bonus_attr1", clientItem.BonusAttr1);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr2))
            AddServerElement(xmlDoc, element, "bonus_attr2", clientItem.BonusAttr2);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr3))
            AddServerElement(xmlDoc, element, "bonus_attr3", clientItem.BonusAttr3);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr4))
            AddServerElement(xmlDoc, element, "bonus_attr4", clientItem.BonusAttr4);
        if (!string.IsNullOrEmpty(clientItem.BonusApply))
            AddServerElement(xmlDoc, element, "bonus_apply", clientItem.BonusApply);
    }

    /// <summary>
    /// 添加服务端XML元素
    /// </summary>
    private void AddServerElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            var element = xmlDoc.CreateElement(elementName);
            element.InnerText = value;
            parentElement.AppendChild(element);
        }
    }
}

/// <summary>
/// 物品类别枚举
/// </summary>
public enum ItemCategory
{
    Weapon,
    Armor,
    Etc
}

/// <summary>
/// 转换结果
/// </summary>
public class ConversionResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public int TotalCount { get; set; }
    public int WeaponCount { get; set; }
    public int ArmorCount { get; set; }
    public int EtcCount { get; set; }
    public string WeaponFilePath { get; set; } = string.Empty;
    public string ArmorFilePath { get; set; } = string.Empty;
    public string EtcFilePath { get; set; } = string.Empty;
}
