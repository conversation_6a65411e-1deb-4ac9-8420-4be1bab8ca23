using AionItemEditor.Models;

namespace AionItemEditor.Services;

/// <summary>
/// XML数据服务接口
/// 定义了XML文件读取、写入和管理的方法
/// </summary>
public interface IXmlDataService
{
    /// <summary>
    /// 加载XML文件中的所有物品
    /// </summary>
    /// <param name="filePath">XML文件路径</param>
    /// <returns>物品列表</returns>
    Task<List<ClientItem>> LoadItemsAsync(string filePath);

    /// <summary>
    /// 保存物品列表到XML文件
    /// </summary>
    /// <param name="filePath">XML文件路径</param>
    /// <param name="items">物品列表</param>
    Task SaveItemsAsync(string filePath, List<ClientItem> items);

    /// <summary>
    /// 验证XML文件格式
    /// </summary>
    /// <param name="filePath">XML文件路径</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateXmlFileAsync(string filePath);

    /// <summary>
    /// 创建XML文件的备份
    /// </summary>
    /// <param name="filePath">原文件路径</param>
    /// <returns>备份文件路径</returns>
    Task<string> CreateBackupAsync(string filePath);

    /// <summary>
    /// 获取支持的XML文件列表
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>XML文件路径列表</returns>
    Task<List<string>> GetXmlFilesAsync(string directoryPath);

    /// <summary>
    /// 根据ID查找物品
    /// </summary>
    /// <param name="items">物品列表</param>
    /// <param name="id">物品ID</param>
    /// <returns>找到的物品，如果不存在则返回null</returns>
    ClientItem? FindItemById(List<ClientItem> items, int id);

    /// <summary>
    /// 根据名称搜索物品
    /// </summary>
    /// <param name="items">物品列表</param>
    /// <param name="name">物品名称（支持部分匹配）</param>
    /// <returns>匹配的物品列表</returns>
    List<ClientItem> SearchItemsByName(List<ClientItem> items, string name);

    /// <summary>
    /// 验证物品数据
    /// </summary>
    /// <param name="item">要验证的物品</param>
    /// <returns>验证结果和错误信息</returns>
    (bool IsValid, List<string> Errors) ValidateItem(ClientItem item);
}
