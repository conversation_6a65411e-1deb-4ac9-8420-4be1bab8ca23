using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor;

/// <summary>
/// 称号管理器窗口
/// </summary>
public partial class TitleManagerWindow : Window
{
    private readonly ILogger<TitleManagerWindow> _logger;
    private readonly TitleXmlService _xmlService;
    private readonly StringService _stringService;
    private ObservableCollection<ClientTitle> _titles;
    private CollectionViewSource _filteredTitles;
    private string? _currentFilePath;

    public TitleManagerWindow(ILogger<TitleManagerWindow> logger, TitleXmlService xmlService, StringService stringService)
    {
        InitializeComponent();
        
        _logger = logger;
        _xmlService = xmlService;
        _stringService = stringService;
        _titles = new ObservableCollection<ClientTitle>();
        _filteredTitles = new CollectionViewSource { Source = _titles };
        
        InitializeUI();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        TitlesDataGrid.ItemsSource = _filteredTitles.View;
        RaceFilterComboBox.SelectedIndex = 0; // 默认选择"全部"
        
        // 设置筛选
        _filteredTitles.Filter += FilterTitles;
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 工具栏按钮事件
        OpenFileButton.Click += OpenFileButton_Click;
        SaveFileButton.Click += SaveFileButton_Click;
        
        // 搜索和筛选事件
        SearchTextBox.TextChanged += (s, e) => _filteredTitles.View.Refresh();
        RaceFilterComboBox.SelectionChanged += (s, e) => _filteredTitles.View.Refresh();
        ClearFilterButton.Click += ClearFilterButton_Click;
        
        // 数据网格事件
        TitlesDataGrid.MouseDoubleClick += TitlesDataGrid_MouseDoubleClick;
        TitlesDataGrid.KeyDown += TitlesDataGrid_KeyDown;
    }

    /// <summary>
    /// 打开文件按钮点击
    /// </summary>
    private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择称号文件",
                Filter = "称号文件 (client_titles.xml)|client_titles.xml|XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadTitlesFile(openFileDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开文件时出错");
            MessageBox.Show($"打开文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 保存文件按钮点击
    /// </summary>
    private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存称号文件",
                    Filter = "称号文件 (client_titles.xml)|client_titles.xml|XML文件 (*.xml)|*.xml",
                    DefaultExt = "xml",
                    FileName = "client_titles.xml"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _currentFilePath = saveFileDialog.FileName;
                }
                else
                {
                    return;
                }
            }

            await SaveTitlesFile(_currentFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件时出错");
            MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 清除筛选按钮点击
    /// </summary>
    private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Text = string.Empty;
        RaceFilterComboBox.SelectedIndex = 0;
        _filteredTitles.View.Refresh();
        UpdateStatus("已清除所有筛选条件");
    }

    /// <summary>
    /// 数据网格双击事件
    /// </summary>
    private void TitlesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (TitlesDataGrid.SelectedItem is ClientTitle selectedTitle)
        {
            OpenTitleEditor(selectedTitle);
        }
    }

    /// <summary>
    /// 数据网格键盘事件
    /// </summary>
    private void TitlesDataGrid_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && TitlesDataGrid.SelectedItem is ClientTitle selectedTitle)
        {
            OpenTitleEditor(selectedTitle);
            e.Handled = true;
        }
    }

    /// <summary>
    /// 加载称号文件
    /// </summary>
    private async Task LoadTitlesFile(string filePath)
    {
        try
        {
            UpdateStatus("正在加载称号文件...");
            
            var titles = await _xmlService.LoadTitlesAsync(filePath);
            
            _titles.Clear();
            foreach (var title in titles)
            {
                _titles.Add(title);
            }
            
            _currentFilePath = filePath;
            UpdateStatistics();
            UpdateStatus($"成功加载 {titles.Count} 个称号");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载称号文件时出错: {FilePath}", filePath);
            UpdateStatus($"加载文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 保存称号文件
    /// </summary>
    private async Task SaveTitlesFile(string filePath)
    {
        try
        {
            UpdateStatus("正在保存称号文件...");
            
            await _xmlService.SaveTitlesAsync(filePath, _titles.ToList());
            
            UpdateStatus($"成功保存 {_titles.Count} 个称号");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存称号文件时出错: {FilePath}", filePath);
            UpdateStatus($"保存文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 打开称号编辑器
    /// </summary>
    private void OpenTitleEditor(ClientTitle title)
    {
        try
        {
            var titleEditor = new TitleEditorWindow(title, _stringService)
            {
                Owner = this
            };

            var result = titleEditor.ShowDialog();
            if (result == true && titleEditor.DataChanged)
            {
                // 更新原始数据
                UpdateTitleData(title, titleEditor.EditedTitle);
                
                // 刷新显示
                _filteredTitles.View.Refresh();
                
                UpdateStatus($"已更新称号: {title.DisplayName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开称号编辑器时出错");
            MessageBox.Show($"打开编辑器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 更新称号数据
    /// </summary>
    private void UpdateTitleData(ClientTitle original, ClientTitle edited)
    {
        original.Name = edited.Name;
        original.Desc = edited.Desc;
        original.TitleDesc = edited.TitleDesc;
        original.TitleRace = edited.TitleRace;
        original.TitleLocation = edited.TitleLocation;
        original.TitlePriority = edited.TitlePriority;
        original.TitleType = edited.TitleType;
        original.BonusAttrs = edited.BonusAttrs.ToList();
    }

    /// <summary>
    /// 筛选称号
    /// </summary>
    private void FilterTitles(object sender, FilterEventArgs e)
    {
        if (e.Item is not ClientTitle title)
        {
            e.Accepted = false;
            return;
        }

        // 搜索文本筛选
        var searchText = SearchTextBox.Text?.Trim().ToLower();
        if (!string.IsNullOrEmpty(searchText))
        {
            var matchesSearch = title.Id.ToString().Contains(searchText) ||
                               title.Name.ToLower().Contains(searchText) ||
                               title.Desc.ToLower().Contains(searchText) ||
                               title.DisplayName.ToLower().Contains(searchText);
            
            if (!matchesSearch)
            {
                e.Accepted = false;
                return;
            }
        }

        // 种族筛选
        if (RaceFilterComboBox.SelectedItem is ComboBoxItem raceItem && 
            int.TryParse(raceItem.Tag?.ToString(), out var raceFilter) && 
            raceFilter >= 0)
        {
            if (title.TitleRace != raceFilter)
            {
                e.Accepted = false;
                return;
            }
        }

        e.Accepted = true;
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        var stats = _xmlService.GetStatistics(_titles.ToList());
        
        TotalCountTextBlock.Text = $"总计: {stats.TotalTitles}";
        LightCountTextBlock.Text = $"天族: {stats.LightTitles}";
        DarkCountTextBlock.Text = $"魔族: {stats.DarkTitles}";
        CommonCountTextBlock.Text = $"通用: {stats.CommonTitles}";
    }

    /// <summary>
    /// 更新状态
    /// </summary>
    private void UpdateStatus(string message)
    {
        StatusTextBlock.Text = message;
        _logger.LogInformation(message);
    }
}
