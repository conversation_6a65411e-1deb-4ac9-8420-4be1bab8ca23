using System.Collections.Generic;
using System.Xml.Serialization;

namespace TitleTest;

/// <summary>
/// 称号数据模型 - 基于真实的client_titles.xml文件结构
/// </summary>
[XmlRoot("client_title")]
public class ClientTitle
{
    /// <summary>
    /// 称号ID
    /// </summary>
    [XmlElement("id")]
    public int Id { get; set; }

    /// <summary>
    /// 称号内部名称
    /// </summary>
    [XmlElement("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 称号显示名称键
    /// </summary>
    [XmlElement("desc")]
    public string Desc { get; set; } = string.Empty;

    /// <summary>
    /// 称号详细描述键
    /// </summary>
    [XmlElement("title_desc")]
    public string TitleDesc { get; set; } = string.Empty;

    /// <summary>
    /// 称号种族限制 (0=天族, 1=魔族, 2=通用)
    /// </summary>
    [XmlElement("title_race")]
    public int TitleRace { get; set; } = 2;

    /// <summary>
    /// 奖励属性列表
    /// </summary>
    [XmlArray("bonus_attrs")]
    [XmlArrayItem("data")]
    public List<TitleBonusData> BonusAttrs { get; set; } = new List<TitleBonusData>();

    /// <summary>
    /// 称号获得地点
    /// </summary>
    [XmlElement("title_location")]
    public string TitleLocation { get; set; } = string.Empty;

    /// <summary>
    /// 称号优先级
    /// </summary>
    [XmlElement("title_priority")]
    public int TitlePriority { get; set; } = 1;

    /// <summary>
    /// 称号类型
    /// </summary>
    [XmlElement("title_type")]
    public int TitleType { get; set; } = 0;

    /// <summary>
    /// 显示名称（用于界面显示）
    /// </summary>
    [XmlIgnore]
    public string DisplayName => !string.IsNullOrEmpty(Desc) ? Desc : Name;

    /// <summary>
    /// 种族显示名称
    /// </summary>
    [XmlIgnore]
    public string RaceDisplayName => TitleRace switch
    {
        0 => "天族",
        1 => "魔族",
        2 => "通用",
        _ => "未知"
    };

    /// <summary>
    /// 获取本地化显示名称
    /// </summary>
    public string GetLocalizedDisplayName(object? stringService = null)
    {
        return DisplayName; // 简化版本，直接返回显示名称
    }

    /// <summary>
    /// 获取本地化描述
    /// </summary>
    public string GetLocalizedDescription(object? stringService = null)
    {
        return TitleDesc; // 简化版本，直接返回描述
    }

    /// <summary>
    /// 获取本地化地点
    /// </summary>
    public string GetLocalizedLocation(object? stringService = null)
    {
        return TitleLocation; // 简化版本，直接返回地点
    }

    /// <summary>
    /// 获取奖励属性摘要
    /// </summary>
    [XmlIgnore]
    public string BonusAttrsSummary
    {
        get
        {
            if (BonusAttrs == null || BonusAttrs.Count == 0)
                return "无奖励属性";

            var attrs = new List<string>();
            foreach (var bonus in BonusAttrs)
            {
                if (!string.IsNullOrEmpty(bonus.BonusAttr))
                    attrs.Add(bonus.BonusAttr);
            }

            return attrs.Count > 0 ? string.Join(", ", attrs) : "无奖励属性";
        }
    }
}

/// <summary>
/// 称号奖励数据
/// </summary>
public class TitleBonusData
{
    /// <summary>
    /// 奖励属性
    /// </summary>
    [XmlElement("bonus_attr")]
    public string BonusAttr { get; set; } = string.Empty;
}

/// <summary>
/// 称号统计信息
/// </summary>
public class TitleStatistics
{
    public int TotalTitles { get; set; }
    public int LightTitles { get; set; }
    public int DarkTitles { get; set; }
    public int CommonTitles { get; set; }
    public Dictionary<string, int> BonusAttrTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> LocationCounts { get; set; } = new Dictionary<string, int>();
}
