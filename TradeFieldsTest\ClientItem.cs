using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using System.Linq;

namespace TradeFieldsTest;

/// <summary>
/// 简化的客户端物品数据模型
/// </summary>
[XmlRoot("client_item")]
public class ClientItem
{
    [XmlElement("id")]
    [Required(ErrorMessage = "物品ID不能为空")]
    public int Id { get; set; }

    [XmlElement("name")]
    [Required(ErrorMessage = "物品名称不能为空")]
    public string Name { get; set; } = string.Empty;

    [XmlElement("desc")]
    public string Desc { get; set; } = string.Empty;

    [XmlElement("armor_type")]
    public string ArmorType { get; set; } = string.Empty;

    [XmlElement("item_type")]
    public string ItemType { get; set; } = "normal";

    [XmlElement("material")]
    public string Material { get; set; } = string.Empty;

    [XmlElement("icon_name")]
    public string IconName { get; set; } = string.Empty;

    [XmlElement("price")]
    [Range(0, int.MaxValue, ErrorMessage = "价格不能为负数")]
    public int Price { get; set; }

    [XmlElement("max_stack_count")]
    [Range(1, 999, ErrorMessage = "堆叠数量必须在1-999之间")]
    public int MaxStackCount { get; set; } = 1;

    [XmlElement("can_sell_to_npc")]
    public bool CanSellToNpc { get; set; } = true;

    [XmlElement("can_deposit_to_character_warehouse")]
    public bool CanDepositToCharacterWarehouse { get; set; } = true;

    [XmlElement("can_deposit_to_account_warehouse")]
    public bool CanDepositToAccountWarehouse { get; set; } = true;

    [XmlElement("can_deposit_to_guild_warehouse")]
    public bool CanDepositToGuildWarehouse { get; set; } = true;

    [XmlElement("equipment_slots")]
    public string EquipmentSlots { get; set; } = string.Empty;

    [XmlElement("dodge")]
    public int Dodge { get; set; } = 0;

    [XmlElement("quality")]
    public string Quality { get; set; } = "common";

    [XmlElement("level")]
    public int Level { get; set; } = 1;

    [XmlElement("magical_resist")]
    public int MagicalResist { get; set; } = 0;

    [XmlElement("physical_defend")]
    public int PhysicalDefend { get; set; } = 0;

    [XmlElement("lore")]
    public bool Lore { get; set; } = false;

    [XmlElement("can_exchange")]
    public bool CanExchange { get; set; } = true;

    [XmlElement("breakable")]
    public bool Breakable { get; set; } = true;

    [XmlElement("soul_bind")]
    public bool SoulBind { get; set; } = false;

    [XmlElement("remove_when_logout")]
    public bool RemoveWhenLogout { get; set; } = false;

    [XmlElement("gender_permitted")]
    public string GenderPermitted { get; set; } = "all";





    // 职业限制
    [XmlElement("warrior")]
    public int Warrior { get; set; } = 1;

    [XmlElement("scout")]
    public int Scout { get; set; } = 1;

    [XmlElement("mage")]
    public int Mage { get; set; } = 1;

    [XmlElement("cleric")]
    public int Cleric { get; set; } = 1;

    [XmlElement("engineer")]
    public int Engineer { get; set; } = 1;

    [XmlElement("artist")]
    public int Artist { get; set; } = 1;

    [XmlElement("fighter")]
    public int Fighter { get; set; } = 1;

    [XmlElement("knight")]
    public int Knight { get; set; } = 1;

    [XmlElement("assassin")]
    public int Assassin { get; set; } = 1;

    [XmlElement("ranger")]
    public int Ranger { get; set; } = 1;

    [XmlElement("wizard")]
    public int Wizard { get; set; } = 1;

    [XmlElement("elementalist")]
    public int Elementalist { get; set; } = 1;

    [XmlElement("chanter")]
    public int Chanter { get; set; } = 1;

    [XmlElement("priest")]
    public int Priest { get; set; } = 1;

    [XmlElement("gunner")]
    public int Gunner { get; set; } = 1;

    [XmlElement("bard")]
    public int Bard { get; set; } = 1;

    [XmlElement("rider")]
    public int Rider { get; set; } = 1;

    // 强化属性
    [XmlElement("option_slot_value")]
    public int OptionSlotValue { get; set; } = 0;

    [XmlElement("special_slot_value")]
    public int SpecialSlotValue { get; set; } = 0;

    [XmlElement("option_slot_bonus")]
    public int OptionSlotBonus { get; set; } = 0;

    [XmlElement("bonus_apply")]
    public string BonusApply { get; set; } = "equip";

    [XmlElement("no_enchant")]
    public bool NoEnchant { get; set; } = false;

    [XmlElement("max_enchant_value")]
    public int MaxEnchantValue { get; set; } = 15;

    [XmlElement("can_proc_enchant")]
    public bool CanProcEnchant { get; set; } = true;

    [XmlElement("can_composite_weapon")]
    public bool CanCompositeWeapon { get; set; } = true;

    // 外观和音效
    [XmlElement("cannot_changeskin")]
    public int CannotChangeskin { get; set; } = 0;

    [XmlElement("ui_sound_type")]
    public string UiSoundType { get; set; } = string.Empty;

    [XmlElement("mesh")]
    public string Mesh { get; set; } = string.Empty;

    [XmlElement("mesh_change")]
    public int MeshChange { get; set; } = 0;

    [XmlElement("can_dye")]
    public int CanDye { get; set; } = 0;

    // 特殊属性
    [XmlElement("cash_item")]
    public int CashItem { get; set; } = 0;

    [XmlElement("can_split")]
    public bool CanSplit { get; set; } = false;

    [XmlElement("item_drop_permitted")]
    public bool ItemDropPermitted { get; set; } = false;

    [XmlElement("bm_restrict_category")]
    public int BmRestrictCategory { get; set; } = 1;

    [XmlElement("can_ap_extraction")]
    public bool CanApExtraction { get; set; } = false;

    [XmlElement("can_polish")]
    public bool CanPolish { get; set; } = true;

    [XmlElement("polish_burn_on_attack")]
    public int PolishBurnOnAttack { get; set; } = 29;

    [XmlElement("polish_burn_on_defend")]
    public int PolishBurnOnDefend { get; set; } = 12;

    [XmlElement("race_permitted")]
    public string RacePermitted { get; set; } = "pc_light pc_dark";

    // 武器装备属性
    [XmlElement("bonus_attr1")]
    public string BonusAttr1 { get; set; } = string.Empty;

    [XmlElement("bonus_attr2")]
    public string BonusAttr2 { get; set; } = string.Empty;

    [XmlElement("bonus_attr3")]
    public string BonusAttr3 { get; set; } = string.Empty;

    [XmlElement("bonus_attr4")]
    public string BonusAttr4 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a1")]
    public string BonusAttrA1 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a2")]
    public string BonusAttrA2 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a3")]
    public string BonusAttrA3 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a4")]
    public string BonusAttrA4 { get; set; } = string.Empty;

    [XmlElement("burn_on_attack")]
    public int BurnOnAttack { get; set; } = 0;

    [XmlElement("burn_on_defend")]
    public int BurnOnDefend { get; set; } = 0;

    [XmlElement("disposable_trade_item")]
    public string DisposableTradeItem { get; set; } = string.Empty;

    [XmlElement("disposable_trade_item_count")]
    public int DisposableTradeItemCount { get; set; } = 0;

    [XmlElement("can_pack_count")]
    public int CanPackCount { get; set; } = 0;

    [XmlElement("visual_slot")]
    public string VisualSlot { get; set; } = string.Empty;

    [XmlElement("default_color_m")]
    public string DefaultColorM { get; set; } = "255,255,255";

    [XmlElement("default_color_f")]
    public string DefaultColorF { get; set; } = "255,255,255";

    // 武器专用属性
    [XmlElement("weapon_type")]
    public string WeaponType { get; set; } = string.Empty;

    [XmlElement("dmg_decal")]
    public int DmgDecal { get; set; } = 0;

    [XmlElement("equip_bone")]
    public string EquipBone { get; set; } = string.Empty;

    [XmlElement("combat_equip_bone")]
    public string CombatEquipBone { get; set; } = string.Empty;



    // 描述和说明
    [XmlElement("desc_long")]
    public string DescLong { get; set; } = string.Empty;

    // 分解属性
    [XmlElement("disassembly_item")]
    public int DisassemblyItem { get; set; } = 0;



    // 额外的奖励属性 (从XML中发现的更多bonus_attr字段)
    [XmlElement("bonus_attr5")]
    public string BonusAttr5 { get; set; } = string.Empty;

    [XmlElement("bonus_attr6")]
    public string BonusAttr6 { get; set; } = string.Empty;

    [XmlElement("bonus_attr7")]
    public string BonusAttr7 { get; set; } = string.Empty;

    [XmlElement("bonus_attr8")]
    public string BonusAttr8 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a5")]
    public string BonusAttrA5 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a6")]
    public string BonusAttrA6 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a7")]
    public string BonusAttrA7 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_a8")]
    public string BonusAttrA8 { get; set; } = string.Empty;

    // 特殊效果属性
    [XmlElement("bonus_attr_b1")]
    public string BonusAttrB1 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_b2")]
    public string BonusAttrB2 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_b3")]
    public string BonusAttrB3 { get; set; } = string.Empty;

    [XmlElement("bonus_attr_b4")]
    public string BonusAttrB4 { get; set; } = string.Empty;

    /// <summary>
    /// 获取物品的显示名称
    /// </summary>
    public string DisplayName => $"[{Id}] {Name}";

    /// <summary>
    /// 获取本地化的显示名称
    /// </summary>
    public string GetLocalizedDisplayName(StringService? stringService)
    {
        if (stringService == null)
            return DisplayName;

        var localizedName = stringService.GetString(Desc);
        if (!string.IsNullOrEmpty(localizedName) && localizedName != Desc)
        {
            return $"[{Id}] {localizedName}";
        }

        return DisplayName;
    }

    /// <summary>
    /// 获取本地化的描述文本
    /// </summary>
    public string GetLocalizedDescription(StringService? stringService)
    {
        if (stringService == null || string.IsNullOrEmpty(Desc))
            return Desc;

        return stringService.GetString(Desc);
    }

    // ==================== 额外交易字段 ====================

    /// <summary>
    /// 额外货币物品
    /// </summary>
    [XmlElement("extra_currency_item")]
    public string ExtraCurrencyItem { get; set; } = string.Empty;

    /// <summary>
    /// 额外货币物品数量
    /// </summary>
    [XmlElement("extra_currency_item_count")]
    public int ExtraCurrencyItemCount { get; set; } = 0;

    /// <summary>
    /// 兑换物品列表
    /// </summary>
    [XmlArray("trade_in_item_list")]
    [XmlArrayItem("data")]
    public List<TradeInItemData> TradeInItemList { get; set; } = new List<TradeInItemData>();

    /// <summary>
    /// 激活技能
    /// </summary>
    [XmlElement("activation_skill")]
    public string ActivationSkill { get; set; } = string.Empty;

    /// <summary>
    /// 激活模式
    /// </summary>
    [XmlElement("activation_mode")]
    public string ActivationMode { get; set; } = string.Empty;

    /// <summary>
    /// 额外库存
    /// </summary>
    [XmlElement("extra_inventory")]
    public int ExtraInventory { get; set; } = 0;

    /// <summary>
    /// 获取交易成本摘要
    /// </summary>
    [XmlIgnore]
    public string TradeCostSummary
    {
        get
        {
            var costs = new List<string>();

            if (Price > 0)
                costs.Add($"{Price:N0} 基纳");

            if (!string.IsNullOrEmpty(ExtraCurrencyItem) && ExtraCurrencyItemCount > 0)
                costs.Add($"{ExtraCurrencyItemCount} {ExtraCurrencyItem}");

            if (TradeInItemList != null && TradeInItemList.Count > 0)
            {
                foreach (var item in TradeInItemList)
                {
                    costs.Add($"{item.TradeInItemCount} {item.TradeInItem}");
                }
            }

            return costs.Count > 0 ? string.Join(" + ", costs) : "免费";
        }
    }

    /// <summary>
    /// 获取职业限制摘要
    /// </summary>
    [XmlIgnore]
    public string ClassRestrictionSummary
    {
        get
        {
            var allowedClasses = new List<string>();

            if (Warrior == 1) allowedClasses.Add("战士");
            if (Scout == 1) allowedClasses.Add("侦察");
            if (Mage == 1) allowedClasses.Add("法师");
            if (Cleric == 1) allowedClasses.Add("牧师");
            if (Engineer == 1) allowedClasses.Add("工程师");
            if (Artist == 1) allowedClasses.Add("艺术家");
            if (Fighter == 1) allowedClasses.Add("格斗家");
            if (Knight == 1) allowedClasses.Add("守护星");
            if (Assassin == 1) allowedClasses.Add("杀星");
            if (Ranger == 1) allowedClasses.Add("游侠");
            if (Wizard == 1) allowedClasses.Add("魔道星");
            if (Elementalist == 1) allowedClasses.Add("精灵星");
            if (Chanter == 1) allowedClasses.Add("护法星");
            if (Priest == 1) allowedClasses.Add("治愈星");
            if (Gunner == 1) allowedClasses.Add("枪手");
            if (Bard == 1) allowedClasses.Add("吟游诗人");
            if (Rider == 1) allowedClasses.Add("骑士");

            return allowedClasses.Count == 17 ? "全职业" :
                   allowedClasses.Count == 0 ? "无职业限制" :
                   string.Join(", ", allowedClasses);
        }
    }

    /// <summary>
    /// 获取权限摘要
    /// </summary>
    [XmlIgnore]
    public string PermissionSummary
    {
        get
        {
            var permissions = new List<string>();

            if (CanExchange) permissions.Add("可交易");
            if (CanSellToNpc) permissions.Add("可卖NPC");
            if (CanDepositToCharacterWarehouse) permissions.Add("角色仓库");
            if (CanDepositToAccountWarehouse) permissions.Add("账户仓库");
            if (CanDepositToGuildWarehouse) permissions.Add("军团仓库");
            if (ItemDropPermitted) permissions.Add("可掉落");

            return permissions.Count > 0 ? string.Join(", ", permissions) : "无权限";
        }
    }

    /// <summary>
    /// 检查物品是否有效
    /// </summary>
    public bool IsValid()
    {
        return Id > 0 && !string.IsNullOrWhiteSpace(Name);
    }
}

/// <summary>
/// 兑换物品数据
/// </summary>
public class TradeInItemData
{
    /// <summary>
    /// 兑换物品ID
    /// </summary>
    [XmlElement("trade_in_item")]
    public string TradeInItem { get; set; } = string.Empty;

    /// <summary>
    /// 兑换物品数量
    /// </summary>
    [XmlElement("trade_in_item_count")]
    public int TradeInItemCount { get; set; } = 1;
}
