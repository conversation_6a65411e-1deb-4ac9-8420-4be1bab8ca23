using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SimpleWpfEditor;

/// <summary>
/// NPC商店编辑器窗口
/// </summary>
public partial class NpcShopEditorWindow : Window
{
    private readonly StringService? _stringService;
    private ClientNpcGoodsList _originalShop;
    private ClientNpcGoodsList _editingShop;
    private ObservableCollection<GoodsData> _goodsList;

    /// <summary>
    /// 数据是否已更改
    /// </summary>
    public bool DataChanged { get; private set; }

    /// <summary>
    /// 编辑后的商店数据
    /// </summary>
    public ClientNpcGoodsList EditedShop => _editingShop;

    public NpcShopEditorWindow(ClientNpcGoodsList shop, StringService? stringService = null)
    {
        InitializeComponent();
        
        _stringService = stringService;
        _originalShop = shop;
        _editingShop = CloneShop(shop);
        _goodsList = new ObservableCollection<GoodsData>(_editingShop.GoodsList);
        
        InitializeUI();
        LoadShopData();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        GoodsListBox.ItemsSource = _goodsList;
    }

    /// <summary>
    /// 加载商店数据
    /// </summary>
    private void LoadShopData()
    {
        if (_editingShop == null) return;

        // 基础信息
        IdTextBlock.Text = _editingShop.Id.ToString();
        ShopIdTextBlock.Text = $"ID: {_editingShop.Id}";
        NameTextBox.Text = _editingShop.Name;
        DescTextBox.Text = _editingShop.Desc;
        LocalizedNameTextBlock.Text = _editingShop.GetLocalizedDisplayName(_stringService);

        // 商店分类
        ShopTypeTextBlock.Text = _editingShop.ShopType;
        ProfessionTypeTextBlock.Text = _editingShop.ProfessionType;
        LevelTypeTextBlock.Text = _editingShop.LevelType;
        ItemCountTextBlock.Text = _editingShop.ItemCount.ToString();

        // 商品统计
        UpdateItemStatistics();

        // 更新窗口标题
        UpdateWindowTitle();
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 基础信息变更事件
        NameTextBox.TextChanged += (s, e) => { 
            _editingShop.Name = NameTextBox.Text; 
            DataChanged = true; 
            UpdateShopClassification();
        };
        DescTextBox.TextChanged += (s, e) => { 
            _editingShop.Desc = DescTextBox.Text; 
            DataChanged = true; 
            LocalizedNameTextBlock.Text = _editingShop.GetLocalizedDisplayName(_stringService);
        };

        // 商品列表按钮事件
        AddItemButton.Click += AddItemButton_Click;
        RemoveItemButton.Click += RemoveItemButton_Click;

        // 底部按钮事件
        ResetButton.Click += ResetButton_Click;
        CancelButton.Click += CancelButton_Click;
        SaveButton.Click += SaveButton_Click;

        // 商品列表变更事件
        _goodsList.CollectionChanged += (s, e) => { 
            _editingShop.GoodsList = _goodsList.ToList(); 
            DataChanged = true; 
            UpdateItemStatistics();
            UpdateShopClassification();
        };
    }

    /// <summary>
    /// 更新商店分类信息
    /// </summary>
    private void UpdateShopClassification()
    {
        ShopTypeTextBlock.Text = _editingShop.ShopType;
        ProfessionTypeTextBlock.Text = _editingShop.ProfessionType;
        LevelTypeTextBlock.Text = _editingShop.LevelType;
        ItemCountTextBlock.Text = _editingShop.ItemCount.ToString();
        UpdateWindowTitle();
    }

    /// <summary>
    /// 更新商品统计
    /// </summary>
    private void UpdateItemStatistics()
    {
        var stats = _editingShop.GetItemTypeStatistics();
        var statsList = stats.Select(kvp => new { Key = kvp.Key, Value = kvp.Value }).ToList();
        ItemStatsListBox.ItemsSource = statsList;
    }

    /// <summary>
    /// 更新窗口标题
    /// </summary>
    private void UpdateWindowTitle()
    {
        if (_editingShop == null) return;

        var titleParts = new[] { "NPC商店编辑器" };
        
        // 添加商店类型信息
        var shopInfo = $"{_editingShop.ShopType} - {_editingShop.ProfessionType}";
        titleParts = titleParts.Append(shopInfo).ToArray();

        Title = string.Join(" - ", titleParts);
        ShopInfoTextBlock.Text = $"编辑 {shopInfo} ({_editingShop.ItemCount} 件商品)";
    }

    /// <summary>
    /// 添加商品
    /// </summary>
    private void AddItemButton_Click(object sender, RoutedEventArgs e)
    {
        _goodsList.Add(new GoodsData { Item = "new_item_id" });
        StatusTextBlock.Text = "已添加新商品";
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
    {
        if (GoodsListBox.SelectedItem is GoodsData selectedGoods)
        {
            _goodsList.Remove(selectedGoods);
            StatusTextBlock.Text = "已删除选中的商品";
        }
        else
        {
            StatusTextBlock.Text = "请先选择要删除的商品";
        }
    }

    /// <summary>
    /// 重置按钮点击
    /// </summary>
    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("确定要重置所有更改吗？", "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            _editingShop = CloneShop(_originalShop);
            _goodsList.Clear();
            foreach (var goods in _editingShop.GoodsList)
            {
                _goodsList.Add(goods);
            }
            LoadShopData();
            DataChanged = false;
            StatusTextBlock.Text = "已重置所有更改";
        }
    }

    /// <summary>
    /// 取消按钮点击
    /// </summary>
    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataChanged)
        {
            var result = MessageBox.Show("有未保存的更改，确定要取消吗？", "确认取消", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.No)
                return;
        }

        DialogResult = false;
        Close();
    }

    /// <summary>
    /// 保存按钮点击
    /// </summary>
    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 验证数据
            if (!ValidateData())
                return;

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存时出错: {ex.Message}", "保存错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 验证数据
    /// </summary>
    private bool ValidateData()
    {
        if (_editingShop.Id <= 0)
        {
            MessageBox.Show("商店ID必须大于0", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingShop.Name))
        {
            MessageBox.Show("商店内部名称不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            NameTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingShop.Desc))
        {
            MessageBox.Show("商店描述不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            DescTextBox.Focus();
            return false;
        }

        // 验证商品列表中是否有空的物品ID
        var emptyItems = _goodsList.Where(g => string.IsNullOrWhiteSpace(g.Item)).ToList();
        if (emptyItems.Any())
        {
            var result = MessageBox.Show($"发现 {emptyItems.Count} 个空的物品ID，是否自动删除？", "数据验证", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                foreach (var emptyItem in emptyItems)
                {
                    _goodsList.Remove(emptyItem);
                }
            }
            else
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 克隆商店对象
    /// </summary>
    private ClientNpcGoodsList CloneShop(ClientNpcGoodsList original)
    {
        return new ClientNpcGoodsList
        {
            Id = original.Id,
            Name = original.Name,
            Desc = original.Desc,
            GoodsList = original.GoodsList.Select(g => new GoodsData { Item = g.Item }).ToList()
        };
    }
}
