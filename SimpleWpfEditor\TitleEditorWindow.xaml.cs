using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SimpleWpfEditor;

/// <summary>
/// 称号编辑器窗口
/// </summary>
public partial class TitleEditorWindow : Window
{
    private readonly StringService? _stringService;
    private ClientTitle _originalTitle;
    private ClientTitle _editingTitle;
    private ObservableCollection<TitleBonusData> _bonusAttrs;

    /// <summary>
    /// 数据是否已更改
    /// </summary>
    public bool DataChanged { get; private set; }

    /// <summary>
    /// 编辑后的称号数据
    /// </summary>
    public ClientTitle EditedTitle => _editingTitle;

    public TitleEditorWindow(ClientTitle title, StringService? stringService = null)
    {
        InitializeComponent();
        
        _stringService = stringService;
        _originalTitle = title;
        _editingTitle = CloneTitle(title);
        _bonusAttrs = new ObservableCollection<TitleBonusData>(_editingTitle.BonusAttrs);
        
        InitializeUI();
        LoadTitleData();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        BonusAttrsListBox.ItemsSource = _bonusAttrs;
        
        // 设置种族下拉框默认选中项
        TitleRaceComboBox.SelectedIndex = 2; // 默认选中"通用"
    }

    /// <summary>
    /// 加载称号数据
    /// </summary>
    private void LoadTitleData()
    {
        if (_editingTitle == null) return;

        // 基础信息
        IdTextBlock.Text = _editingTitle.Id.ToString();
        TitleIdTextBlock.Text = $"ID: {_editingTitle.Id}";
        NameTextBox.Text = _editingTitle.Name;
        DescTextBox.Text = _editingTitle.Desc;
        TitleDescTextBox.Text = _editingTitle.TitleDesc;
        LocalizedNameTextBlock.Text = _editingTitle.GetLocalizedDisplayName(_stringService);
        LocalizedDescTextBlock.Text = _editingTitle.GetLocalizedDescription(_stringService);

        // 称号属性
        SetComboBoxValue(TitleRaceComboBox, _editingTitle.TitleRace);
        TitleTypeTextBox.Text = _editingTitle.TitleType.ToString();
        TitlePriorityTextBox.Text = _editingTitle.TitlePriority.ToString();
        TitleLocationTextBox.Text = _editingTitle.TitleLocation;
        LocalizedLocationTextBlock.Text = _editingTitle.GetLocalizedLocation(_stringService);

        // 更新窗口标题
        UpdateWindowTitle();
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 基础信息变更事件
        NameTextBox.TextChanged += (s, e) => { _editingTitle.Name = NameTextBox.Text; DataChanged = true; };
        DescTextBox.TextChanged += (s, e) => { 
            _editingTitle.Desc = DescTextBox.Text; 
            DataChanged = true; 
            LocalizedNameTextBlock.Text = _editingTitle.GetLocalizedDisplayName(_stringService);
        };
        TitleDescTextBox.TextChanged += (s, e) => { 
            _editingTitle.TitleDesc = TitleDescTextBox.Text; 
            DataChanged = true; 
            LocalizedDescTextBlock.Text = _editingTitle.GetLocalizedDescription(_stringService);
        };

        // 称号属性变更事件
        TitleRaceComboBox.SelectionChanged += (s, e) => { 
            _editingTitle.TitleRace = GetComboBoxValue(TitleRaceComboBox); 
            DataChanged = true; 
            UpdateWindowTitle();
        };
        TitleTypeTextBox.TextChanged += (s, e) => { 
            if (int.TryParse(TitleTypeTextBox.Text, out var type)) { 
                _editingTitle.TitleType = type; 
                DataChanged = true; 
            } 
        };
        TitlePriorityTextBox.TextChanged += (s, e) => { 
            if (int.TryParse(TitlePriorityTextBox.Text, out var priority)) { 
                _editingTitle.TitlePriority = priority; 
                DataChanged = true; 
            } 
        };
        TitleLocationTextBox.TextChanged += (s, e) => { 
            _editingTitle.TitleLocation = TitleLocationTextBox.Text; 
            DataChanged = true; 
            LocalizedLocationTextBlock.Text = _editingTitle.GetLocalizedLocation(_stringService);
        };

        // 奖励属性按钮事件
        AddBonusButton.Click += AddBonusButton_Click;
        RemoveBonusButton.Click += RemoveBonusButton_Click;

        // 底部按钮事件
        ResetButton.Click += ResetButton_Click;
        CancelButton.Click += CancelButton_Click;
        SaveButton.Click += SaveButton_Click;

        // 奖励属性列表变更事件
        _bonusAttrs.CollectionChanged += (s, e) => { 
            _editingTitle.BonusAttrs = _bonusAttrs.ToList(); 
            DataChanged = true; 
        };
    }

    /// <summary>
    /// 设置下拉框值
    /// </summary>
    private void SetComboBoxValue(ComboBox comboBox, int value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Tag?.ToString() == value.ToString())
            {
                comboBox.SelectedItem = item;
                break;
            }
        }
    }

    /// <summary>
    /// 获取下拉框值
    /// </summary>
    private int GetComboBoxValue(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
        {
            if (int.TryParse(item.Tag.ToString(), out var value))
                return value;
        }
        return 0;
    }

    /// <summary>
    /// 更新窗口标题
    /// </summary>
    private void UpdateWindowTitle()
    {
        if (_editingTitle == null) return;

        var titleParts = new[] { "称号编辑器" };
        
        // 添加种族信息
        var raceInfo = _editingTitle.RaceDisplayName;
        titleParts = titleParts.Append($"种族: {raceInfo}").ToArray();

        Title = string.Join(" - ", titleParts);
        TitleInfoTextBlock.Text = $"编辑 {raceInfo} 称号属性和奖励";
    }

    /// <summary>
    /// 添加奖励属性
    /// </summary>
    private void AddBonusButton_Click(object sender, RoutedEventArgs e)
    {
        _bonusAttrs.Add(new TitleBonusData { BonusAttr = "maxHp 50" });
        StatusTextBlock.Text = "已添加新的奖励属性";
    }

    /// <summary>
    /// 删除奖励属性
    /// </summary>
    private void RemoveBonusButton_Click(object sender, RoutedEventArgs e)
    {
        if (BonusAttrsListBox.SelectedItem is TitleBonusData selectedBonus)
        {
            _bonusAttrs.Remove(selectedBonus);
            StatusTextBlock.Text = "已删除选中的奖励属性";
        }
        else
        {
            StatusTextBlock.Text = "请先选择要删除的奖励属性";
        }
    }

    /// <summary>
    /// 重置按钮点击
    /// </summary>
    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("确定要重置所有更改吗？", "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            _editingTitle = CloneTitle(_originalTitle);
            _bonusAttrs.Clear();
            foreach (var bonus in _editingTitle.BonusAttrs)
            {
                _bonusAttrs.Add(bonus);
            }
            LoadTitleData();
            DataChanged = false;
            StatusTextBlock.Text = "已重置所有更改";
        }
    }

    /// <summary>
    /// 取消按钮点击
    /// </summary>
    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataChanged)
        {
            var result = MessageBox.Show("有未保存的更改，确定要取消吗？", "确认取消", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.No)
                return;
        }

        DialogResult = false;
        Close();
    }

    /// <summary>
    /// 保存按钮点击
    /// </summary>
    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 验证数据
            if (!ValidateData())
                return;

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存时出错: {ex.Message}", "保存错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 验证数据
    /// </summary>
    private bool ValidateData()
    {
        if (_editingTitle.Id <= 0)
        {
            MessageBox.Show("称号ID必须大于0", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingTitle.Name))
        {
            MessageBox.Show("称号内部名称不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            NameTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingTitle.Desc))
        {
            MessageBox.Show("称号显示名称不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            DescTextBox.Focus();
            return false;
        }

        return true;
    }

    /// <summary>
    /// 克隆称号对象
    /// </summary>
    private ClientTitle CloneTitle(ClientTitle original)
    {
        return new ClientTitle
        {
            Id = original.Id,
            Name = original.Name,
            Desc = original.Desc,
            TitleDesc = original.TitleDesc,
            TitleRace = original.TitleRace,
            TitleLocation = original.TitleLocation,
            TitlePriority = original.TitlePriority,
            TitleType = original.TitleType,
            BonusAttrs = original.BonusAttrs.Select(b => new TitleBonusData { BonusAttr = b.BonusAttr }).ToList()
        };
    }
}
