using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Windows.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AionItemEditor.Models;
using AionItemEditor.Services;
using Microsoft.Extensions.Logging;

namespace AionItemEditor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly IXmlDataService _xmlDataService;
    private readonly IDialogService _dialogService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<ClientItem> items = new();

    [ObservableProperty]
    private ICollectionView? filteredItems;

    [ObservableProperty]
    private ClientItem? selectedItem;

    [ObservableProperty]
    private string searchText = string.Empty;

    [ObservableProperty]
    private string currentFileName = "未打开文件";

    [ObservableProperty]
    private string statusMessage = "就绪";

    [ObservableProperty]
    private bool isFileModified = false;

    [ObservableProperty]
    private bool isFileSaved = true;

    [ObservableProperty]
    private DateTime? lastSavedTime;

    [ObservableProperty]
    private ItemEditViewModel? itemEditViewModel;

    private string? _currentFilePath;

    public MainWindowViewModel(
        IXmlDataService xmlDataService,
        IDialogService dialogService,
        ILogger<MainWindowViewModel> logger,
        ItemEditViewModel itemEditViewModel)
    {
        _xmlDataService = xmlDataService;
        _dialogService = dialogService;
        _logger = logger;

        // 初始化过滤视图
        FilteredItems = CollectionViewSource.GetDefaultView(Items);
        FilteredItems.Filter = FilterItems;

        // 监听搜索文本变化
        PropertyChanged += OnPropertyChanged;

        // 设置编辑ViewModel
        ItemEditViewModel = itemEditViewModel;
    }

    /// <summary>
    /// 物品数量
    /// </summary>
    public int ItemCount => Items.Count;

    #region 命令

    /// <summary>
    /// 打开文件命令
    /// </summary>
    [RelayCommand]
    private async Task OpenFile()
    {
        try
        {
            var filePath = await _dialogService.ShowOpenFileDialogAsync(
                "XML文件|*.xml|所有文件|*.*",
                "选择要打开的XML文件");

            if (string.IsNullOrEmpty(filePath))
                return;

            StatusMessage = "正在加载文件...";

            var loadedItems = await _xmlDataService.LoadItemsAsync(filePath);
            
            Items.Clear();
            foreach (var item in loadedItems)
            {
                Items.Add(item);
            }

            _currentFilePath = filePath;
            CurrentFileName = Path.GetFileName(filePath);
            IsFileModified = false;
            IsFileSaved = true;
            LastSavedTime = File.GetLastWriteTime(filePath);

            StatusMessage = $"成功加载 {Items.Count} 个物品";
            OnPropertyChanged(nameof(ItemCount));

            _logger.LogInformation("成功打开文件: {FilePath}, 物品数量: {Count}", filePath, Items.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开文件时出错");
            await _dialogService.ShowErrorAsync($"打开文件时出错: {ex.Message}");
            StatusMessage = "打开文件失败";
        }
    }

    /// <summary>
    /// 保存文件命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSaveFile))]
    private async Task SaveFile()
    {
        try
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                await SaveFileAs();
                return;
            }

            StatusMessage = "正在保存文件...";

            await _xmlDataService.SaveItemsAsync(_currentFilePath, Items.ToList());

            IsFileModified = false;
            IsFileSaved = true;
            LastSavedTime = DateTime.Now;

            StatusMessage = "文件保存成功";

            _logger.LogInformation("成功保存文件: {FilePath}", _currentFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件时出错");
            await _dialogService.ShowErrorAsync($"保存文件时出错: {ex.Message}");
            StatusMessage = "保存文件失败";
        }
    }

    /// <summary>
    /// 另存为命令
    /// </summary>
    [RelayCommand]
    private async Task SaveFileAs()
    {
        try
        {
            var filePath = await _dialogService.ShowSaveFileDialogAsync(
                "XML文件|*.xml|所有文件|*.*",
                "保存XML文件",
                CurrentFileName);

            if (string.IsNullOrEmpty(filePath))
                return;

            StatusMessage = "正在保存文件...";

            await _xmlDataService.SaveItemsAsync(filePath, Items.ToList());

            _currentFilePath = filePath;
            CurrentFileName = Path.GetFileName(filePath);
            IsFileModified = false;
            IsFileSaved = true;
            LastSavedTime = DateTime.Now;

            StatusMessage = "文件保存成功";

            _logger.LogInformation("成功另存为文件: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "另存为文件时出错");
            await _dialogService.ShowErrorAsync($"另存为文件时出错: {ex.Message}");
            StatusMessage = "保存文件失败";
        }
    }

    /// <summary>
    /// 新增物品命令
    /// </summary>
    [RelayCommand]
    private async Task AddItem()
    {
        try
        {
            var newId = Items.Count > 0 ? Items.Max(i => i.Id) + 1 : 1;
            var newItem = new ClientItem
            {
                Id = newId,
                Name = $"new_item_{newId}",
                Desc = $"STR_NEW_ITEM_{newId}",
                ItemType = "normal",
                ArmorType = "clothes",
                MaxStackCount = 1,
                Price = 100
            };

            Items.Add(newItem);
            SelectedItem = newItem;
            MarkAsModified();

            StatusMessage = $"新增物品: {newItem.DisplayName}";
            OnPropertyChanged(nameof(ItemCount));

            _logger.LogInformation("新增物品: {ItemId} - {ItemName}", newItem.Id, newItem.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "新增物品时出错");
            await _dialogService.ShowErrorAsync($"新增物品时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 编辑物品命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedItem))]
    private void EditItem()
    {
        if (SelectedItem != null && ItemEditViewModel != null)
        {
            ItemEditViewModel.SetCurrentItem(SelectedItem);
            StatusMessage = $"正在编辑: {SelectedItem.DisplayName}";
        }
    }

    /// <summary>
    /// 复制物品命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedItem))]
    private async Task CopyItem()
    {
        if (SelectedItem == null) return;

        try
        {
            var newId = Items.Max(i => i.Id) + 1;
            var copiedItem = new ClientItem
            {
                // 复制所有属性，但使用新的ID
                Id = newId,
                Name = $"{SelectedItem.Name}_copy",
                Desc = SelectedItem.Desc,
                ArmorType = SelectedItem.ArmorType,
                ItemType = SelectedItem.ItemType,
                Material = SelectedItem.Material,
                IconName = SelectedItem.IconName,
                Price = SelectedItem.Price,
                MaxStackCount = SelectedItem.MaxStackCount,
                // ... 复制其他属性
            };

            Items.Add(copiedItem);
            SelectedItem = copiedItem;
            MarkAsModified();

            StatusMessage = $"复制物品: {copiedItem.DisplayName}";
            OnPropertyChanged(nameof(ItemCount));

            _logger.LogInformation("复制物品: {OriginalId} -> {NewId}", SelectedItem.Id, newId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制物品时出错");
            await _dialogService.ShowErrorAsync($"复制物品时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除物品命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedItem))]
    private async Task DeleteItem()
    {
        if (SelectedItem == null) return;

        try
        {
            var confirmed = await _dialogService.ShowConfirmAsync(
                $"确定要删除物品 '{SelectedItem.DisplayName}' 吗？\n此操作无法撤销。",
                "确认删除");

            if (!confirmed) return;

            var deletedItem = SelectedItem;
            Items.Remove(SelectedItem);
            SelectedItem = null;
            MarkAsModified();

            StatusMessage = $"删除物品: {deletedItem.DisplayName}";
            OnPropertyChanged(nameof(ItemCount));

            _logger.LogInformation("删除物品: {ItemId} - {ItemName}", deletedItem.Id, deletedItem.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除物品时出错");
            await _dialogService.ShowErrorAsync($"删除物品时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 搜索命令
    /// </summary>
    [RelayCommand]
    private void Search()
    {
        FilteredItems?.Refresh();
        StatusMessage = string.IsNullOrWhiteSpace(SearchText)
            ? "显示所有物品"
            : $"搜索结果: {FilteredItems?.Cast<object>().Count() ?? 0} 个物品";
    }

    /// <summary>
    /// 重置物品命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedItem))]
    private async Task ResetItem()
    {
        if (SelectedItem == null || ItemEditViewModel == null) return;

        var confirmed = await _dialogService.ShowConfirmAsync(
            "确定要重置当前物品的所有更改吗？",
            "确认重置");

        if (confirmed)
        {
            ItemEditViewModel.ResetChanges();
            StatusMessage = "已重置物品更改";
        }
    }

    /// <summary>
    /// 保存物品更改命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedItem))]
    private async Task SaveItem()
    {
        if (SelectedItem == null || ItemEditViewModel == null) return;

        try
        {
            var (isValid, errors) = _xmlDataService.ValidateItem(SelectedItem);

            if (!isValid)
            {
                var errorMessage = string.Join("\n", errors);
                await _dialogService.ShowErrorAsync($"物品数据验证失败:\n{errorMessage}");
                return;
            }

            ItemEditViewModel.ApplyChanges();
            MarkAsModified();

            StatusMessage = $"保存物品更改: {SelectedItem.DisplayName}";

            _logger.LogInformation("保存物品更改: {ItemId} - {ItemName}", SelectedItem.Id, SelectedItem.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存物品更改时出错");
            await _dialogService.ShowErrorAsync($"保存物品更改时出错: {ex.Message}");
        }
    }

    #endregion

    #region 命令条件检查

    private bool CanSaveFile() => Items.Count > 0 && IsFileModified;
    private bool HasSelectedItem() => SelectedItem != null;

    #endregion

    #region 私有方法

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(SearchText):
                Search();
                break;
            case nameof(SelectedItem):
                EditItem();
                break;
        }
    }

    /// <summary>
    /// 过滤物品
    /// </summary>
    private bool FilterItems(object obj)
    {
        if (obj is not ClientItem item) return false;
        if (string.IsNullOrWhiteSpace(SearchText)) return true;

        var searchLower = SearchText.ToLowerInvariant();
        return item.Id.ToString().Contains(searchLower) ||
               item.Name.ToLowerInvariant().Contains(searchLower) ||
               item.Desc.ToLowerInvariant().Contains(searchLower) ||
               item.ArmorType.ToLowerInvariant().Contains(searchLower);
    }

    /// <summary>
    /// 标记文件为已修改
    /// </summary>
    private void MarkAsModified()
    {
        IsFileModified = true;
        IsFileSaved = false;
    }

    #endregion
}
