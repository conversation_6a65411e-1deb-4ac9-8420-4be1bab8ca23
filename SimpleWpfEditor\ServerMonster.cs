using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 服务端怪物数据模型（包含掉落系统）
    /// </summary>
    [XmlRoot("npc")]
    public class ServerMonster
    {
        // 基础信息
        [XmlElement("id")]
        public int Id { get; set; }

        [XmlElement("name")]
        public string Name { get; set; } = string.Empty;

        [XmlElement("desc")]
        public string Desc { get; set; } = string.Empty;

        [XmlElement("__spawn_zonename__")]
        public string SpawnZoneName { get; set; } = string.Empty;

        [XmlElement("npc_title")]
        public string NpcTitle { get; set; } = string.Empty;

        [XmlElement("disk_type")]
        public string DiskType { get; set; } = string.Empty;

        // 外观系统
        [XmlElement("appearance")]
        public MonsterAppearance Appearance { get; set; } = new MonsterAppearance();

        [XmlElement("visible_equipments")]
        public MonsterVisibleEquipments VisibleEquipments { get; set; } = new MonsterVisibleEquipments();

        [XmlElement("bound_radius")]
        public MonsterBoundRadius BoundRadius { get; set; } = new MonsterBoundRadius();

        [XmlElement("scale")]
        public int Scale { get; set; } = 100;

        [XmlElement("weapon_scale")]
        public int WeaponScale { get; set; } = 100;

        // 移动属性
        [XmlElement("move_speed_normal_walk")]
        public float MoveSpeedNormalWalk { get; set; } = 1.457f;

        [XmlElement("move_speed_normal_run")]
        public float MoveSpeedNormalRun { get; set; } = 6.0f;

        [XmlElement("move_speed_combat_run")]
        public float MoveSpeedCombatRun { get; set; } = 7.0f;

        [XmlElement("pushed_range")]
        public float PushedRange { get; set; } = 2.0f;

        [XmlElement("__design_reference__")]
        public string DesignReference { get; set; } = string.Empty;

        // 基础属性
        [XmlElement("level")]
        public int Level { get; set; } = 1;

        [XmlElement("exp")]
        public int Exp { get; set; } = 0;

        [XmlElement("dp")]
        public int Dp { get; set; } = 100;

        [XmlElement("max_hp")]
        public int MaxHp { get; set; } = 100;

        [XmlElement("hpgauge_level")]
        public int HpgaugeLevel { get; set; } = 1;

        [XmlElement("hp_regen")]
        public int HpRegen { get; set; } = 0;

        // 攻击属性
        [XmlElement("physical_attack")]
        public int PhysicalAttack { get; set; } = 0;

        [XmlElement("physical_defend")]
        public int PhysicalDefend { get; set; } = 0;

        [XmlElement("magical_attack")]
        public int MagicalAttack { get; set; } = 0;

        [XmlElement("magical_defend")]
        public int MagicalDefend { get; set; } = 0;

        [XmlElement("magical_resist")]
        public int MagicalResist { get; set; } = 0;

        [XmlElement("min_damage")]
        public int MinDamage { get; set; } = 0;

        [XmlElement("max_damage")]
        public int MaxDamage { get; set; } = 0;

        [XmlElement("magical_skill_boost")]
        public int MagicalSkillBoost { get; set; } = 0;

        [XmlElement("magical_skill_boost_resist")]
        public int MagicalSkillBoostResist { get; set; } = 0;

        [XmlElement("hit_count")]
        public int HitCount { get; set; } = 1;

        [XmlElement("attack_delay")]
        public int AttackDelay { get; set; } = 2000;

        [XmlElement("hit_accuracy")]
        public int HitAccuracy { get; set; } = 0;

        [XmlElement("magical_hit_accuracy")]
        public int MagicalHitAccuracy { get; set; } = 0;

        // 暴击属性
        [XmlElement("critical")]
        public int Critical { get; set; } = 0;

        [XmlElement("physical_critical_reduce_rate")]
        public int PhysicalCriticalReduceRate { get; set; } = 0;

        [XmlElement("physical_critical_damage_reduce")]
        public int PhysicalCriticalDamageReduce { get; set; } = 0;

        [XmlElement("magical_critical")]
        public int MagicalCritical { get; set; } = 0;

        [XmlElement("magical_critical_reduce_rate")]
        public int MagicalCriticalReduceRate { get; set; } = 0;

        [XmlElement("magical_critical_damage_reduce")]
        public int MagicalCriticalDamageReduce { get; set; } = 0;

        // 防御属性
        [XmlElement("concentration")]
        public int Concentration { get; set; } = 0;

        [XmlElement("dodge")]
        public int Dodge { get; set; } = 0;

        [XmlElement("parry")]
        public int Parry { get; set; } = 0;

        [XmlElement("block")]
        public int Block { get; set; } = 0;

        [XmlElement("dp_reduce")]
        public int DpReduce { get; set; } = 0;

        // 深渊属性
        [XmlElement("abyss_point")]
        public int AbyssPoint { get; set; } = 0;

        [XmlElement("abyss_rank")]
        public int AbyssRank { get; set; } = 1;

        [XmlElement("cancel_level")]
        public int CancelLevel { get; set; } = 50;

        // 元素防御
        [XmlElement("first_level_attr")]
        public MonsterFirstLevelAttr FirstLevelAttr { get; set; } = new MonsterFirstLevelAttr();

        // 异常状态抗性
        [XmlArray("abnormal_resists")]
        [XmlArrayItem("data")]
        public List<MonsterAbnormalResist> AbnormalResists { get; set; } = new List<MonsterAbnormalResist>();

        // 异常状态穿透
        [XmlArray("abnormal_penetrations")]
        [XmlArrayItem("data")]
        public List<MonsterAbnormalPenetration> AbnormalPenetrations { get; set; } = new List<MonsterAbnormalPenetration>();

        // 伤害修正
        [XmlElement("physical_damage_trim")]
        public int PhysicalDamageTrim { get; set; } = 0;

        [XmlElement("magical_damage_trim")]
        public int MagicalDamageTrim { get; set; } = 0;

        [XmlElement("noreducespell_immune")]
        public int NoreducespellImmune { get; set; } = 0;

        [XmlElement("first_attack_split_rate")]
        public string FirstAttackSplitRate { get; set; } = "Split_Default";

        // AI和行为
        [XmlElement("game_lang")]
        public string GameLang { get; set; } = string.Empty;

        [XmlElement("no_looting_despawn_time")]
        public int NoLootingDespawnTime { get; set; } = 5000;

        [XmlElement("ai_name")]
        public string AiName { get; set; } = string.Empty;

        [XmlElement("quest_ai_name")]
        public string QuestAiName { get; set; } = string.Empty;

        [XmlElement("idle_name")]
        public string IdleName { get; set; } = "NoMove";

        [XmlElement("tribe")]
        public string Tribe { get; set; } = string.Empty;

        [XmlElement("race_type")]
        public string RaceType { get; set; } = string.Empty;

        [XmlElement("pet_ai_name")]
        public string PetAiName { get; set; } = "Pet";

        // 感知和追击
        [XmlElement("max_enemy_count")]
        public int MaxEnemyCount { get; set; } = 10;

        [XmlElement("sensory_range")]
        public float SensoryRange { get; set; } = 8.0f;

        [XmlElement("sensory_range_short")]
        public float SensoryRangeShort { get; set; } = 4.0f;

        [XmlElement("sensory_angle")]
        public int SensoryAngle { get; set; } = 240;

        [XmlElement("max_chase_time")]
        public int MaxChaseTime { get; set; } = 8;

        [XmlElement("chase_flying")]
        public int ChaseFlying { get; set; } = 0;

        [XmlElement("react_to_pathfind_fail")]
        public string ReactToPathfindFail { get; set; } = "return_to_sp";

        // 移动行为
        [XmlElement("move_speed_flee")]
        public int MoveSpeedFlee { get; set; } = 70;

        [XmlElement("move_type_return")]
        public string MoveTypeReturn { get; set; } = "run";

        [XmlElement("move_speed_return")]
        public int MoveSpeedReturn { get; set; } = 200;

        [XmlElement("decrease_sensory_range_return")]
        public int DecreaseSensoryRangeReturn { get; set; } = 40;

        // 仇恨系统
        [XmlElement("hate_decrease_a")]
        public int HateDecreaseA { get; set; } = 364;

        // 特殊能力
        [XmlElement("can_see_invisible")]
        public int CanSeeInvisible { get; set; } = 0;

        [XmlElement("can_see_polymorph")]
        public string CanSeePolymorph { get; set; } = "FALSE";

        // 攻击和技能
        [XmlArray("attacks")]
        [XmlArrayItem("data")]
        public List<MonsterAttack> Attacks { get; set; } = new List<MonsterAttack>();

        [XmlArray("skills")]
        [XmlArrayItem("data")]
        public List<MonsterSkill> Skills { get; set; } = new List<MonsterSkill>();

        // 掉落系统 - 这是重点！
        [XmlElement("items_info")]
        public MonsterItemsInfo ItemsInfo { get; set; } = new MonsterItemsInfo();
    }

    // 外观相关类
    public class MonsterAppearance
    {
        [XmlElement("pc_type")]
        public string PcType { get; set; } = string.Empty;

        [XmlElement("face_type")]
        public int FaceType { get; set; } = 0;

        [XmlElement("hair_type")]
        public int HairType { get; set; } = 0;
    }

    public class MonsterVisibleEquipments
    {
        [XmlElement("head")]
        public string Head { get; set; } = string.Empty;

        [XmlElement("torso")]
        public string Torso { get; set; } = string.Empty;

        [XmlElement("leg")]
        public string Leg { get; set; } = string.Empty;

        [XmlElement("foot")]
        public string Foot { get; set; } = string.Empty;

        [XmlElement("shoulder")]
        public string Shoulder { get; set; } = string.Empty;

        [XmlElement("glove")]
        public string Glove { get; set; } = string.Empty;

        [XmlElement("main")]
        public string Main { get; set; } = string.Empty;

        [XmlElement("sub")]
        public string Sub { get; set; } = string.Empty;
    }

    public class MonsterBoundRadius
    {
        [XmlElement("front")]
        public float Front { get; set; } = 0.5f;

        [XmlElement("side")]
        public float Side { get; set; } = 0.7f;

        [XmlElement("upper")]
        public float Upper { get; set; } = 2.0f;
    }

    public class MonsterFirstLevelAttr
    {
        [XmlElement("elemental_defend_light")]
        public int ElementalDefendLight { get; set; } = 0;

        [XmlElement("elemental_defend_dark")]
        public int ElementalDefendDark { get; set; } = 0;

        [XmlElement("elemental_defend_earth")]
        public int ElementalDefendEarth { get; set; } = 0;

        [XmlElement("elemental_defend_air")]
        public int ElementalDefendAir { get; set; } = 0;

        [XmlElement("elemental_defend_water")]
        public int ElementalDefendWater { get; set; } = 0;

        [XmlElement("elemental_defend_fire")]
        public int ElementalDefendFire { get; set; } = 0;
    }

    public class MonsterAbnormalResist
    {
        [XmlElement("abnormal_resist_name")]
        public string AbnormalResistName { get; set; } = string.Empty;

        [XmlElement("abnormal_resist_value")]
        public int AbnormalResistValue { get; set; } = 0;
    }

    public class MonsterAbnormalPenetration
    {
        [XmlElement("abnormal_penetration_value")]
        public int AbnormalPenetrationValue { get; set; } = 0;
    }

    public class MonsterAttack
    {
        [XmlElement("attack_mod")]
        public int AttackMod { get; set; } = 100;

        [XmlElement("attack_range")]
        public float AttackRange { get; set; } = 2.0f;

        [XmlElement("attack_rate")]
        public int AttackRate { get; set; } = 1000;

        [XmlElement("attack_type")]
        public string AttackType { get; set; } = "physical";
    }

    public class MonsterSkill
    {
        [XmlElement("skill_name")]
        public string SkillName { get; set; } = string.Empty;

        [XmlElement("skill_level")]
        public int SkillLevel { get; set; } = 1;

        [XmlElement("skill_rate")]
        public int SkillRate { get; set; } = 0;
    }

    // 掉落系统 - 重点部分！
    public class MonsterItemsInfo
    {
        [XmlElement("min_cash_amount")]
        public int MinCashAmount { get; set; } = 0;

        [XmlElement("max_cash_amount")]
        public int MaxCashAmount { get; set; } = 0;

        [XmlElement("cash_drop_prob")]
        public int CashDropProb { get; set; } = 0;

        [XmlArray("drop_groups")]
        [XmlArrayItem("data")]
        public List<MonsterDropGroup> DropGroups { get; set; } = new List<MonsterDropGroup>();

        [XmlArray("items")]
        [XmlArrayItem("data")]
        public List<MonsterDropItem> Items { get; set; } = new List<MonsterDropItem>();

        [XmlArray("common_drops")]
        [XmlArrayItem("data")]
        public List<MonsterCommonDrop> CommonDrops { get; set; } = new List<MonsterCommonDrop>();
    }

    public class MonsterDropGroup
    {
        [XmlElement("drop_group_min")]
        public int DropGroupMin { get; set; } = 0;

        [XmlElement("drop_group_max")]
        public int DropGroupMax { get; set; } = 1;
    }

    public class MonsterDropItem
    {
        [XmlElement("prob")]
        public int Prob { get; set; } = -1;

        [XmlElement("item")]
        public string Item { get; set; } = string.Empty;

        [XmlElement("item_id")]
        public int ItemId { get; set; } = 0;

        [XmlElement("item_count")]
        public int ItemCount { get; set; } = 1;

        [XmlElement("min_count")]
        public int MinCount { get; set; } = 1;

        [XmlElement("max_count")]
        public int MaxCount { get; set; } = 1;

        [XmlElement("drop_attribute")]
        public string DropAttribute { get; set; } = "common";

        [XmlElement("drop_group_idx")]
        public int DropGroupIdx { get; set; } = 0;

        // 显示用的属性
        public string DisplayName => !string.IsNullOrEmpty(Item) ? Item : ItemId.ToString();
        public int DisplayCount => ItemCount > 0 ? ItemCount : (MinCount > 0 ? MinCount : 1);
    }

    public class MonsterCommonDrop
    {
        [XmlElement("common_drop")]
        public string CommonDrop { get; set; } = string.Empty;

        [XmlElement("common_drop_adjustment")]
        public int CommonDropAdjustment { get; set; } = -1;

        [XmlElement("common_drop_group_idx")]
        public int CommonDropGroupIdx { get; set; } = 0;

        // 显示用的属性
        public string DisplayName => !string.IsNullOrEmpty(CommonDrop) ? CommonDrop : "通用掉落";
    }
}
