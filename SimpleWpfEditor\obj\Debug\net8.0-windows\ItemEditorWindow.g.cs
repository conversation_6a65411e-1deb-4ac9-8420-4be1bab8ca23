﻿#pragma checksum "..\..\..\ItemEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "66A28D65BA98A3C4B276A1E2B57105625D9F5AC7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// ItemEditorWindow
    /// </summary>
    public partial class ItemEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 35 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtitleText;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemIdText;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemTypeText;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IdTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedNameText;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ArmorTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ItemTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QualityComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EquipmentSlotsComboBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxStackCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DodgeTextBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhysicalDefendTextBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagicalResistTextBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EquipTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpireTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsableRankMinTextBox;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchableRankMinTextBox;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScaleTextBox;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExtractSkinTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TradeModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid BasicPriceGrid;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TradePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ExtraCurrencyPanel;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExtraCurrencyItemComboBox;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExtraCurrencyItemCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TradeInItemPanel;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox TradeInItemListBox;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTradeInItemButton;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCommonTradeItemButton;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TradeModeExampleBorder;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TradeModeExampleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TradeCostSummaryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanExchangeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanSellToNpcCheckBox;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ItemDropPermittedCheckBox;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanSplitCheckBox;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanApExtractionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BreakableCheckBox;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanDepositToCharacterWarehouseCheckBox;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanDepositToAccountWarehouseCheckBox;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanDepositToGuildWarehouseCheckBox;
        
        #line default
        #line hidden
        
        
        #line 463 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SoulBindCheckBox;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RemoveWhenLogoutCheckBox;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LoreCheckBox;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WarriorCheckBox;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ScoutCheckBox;
        
        #line default
        #line hidden
        
        
        #line 496 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MageCheckBox;
        
        #line default
        #line hidden
        
        
        #line 497 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ClericCheckBox;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EngineerCheckBox;
        
        #line default
        #line hidden
        
        
        #line 499 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ArtistCheckBox;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox FighterCheckBox;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KnightCheckBox;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AssassinCheckBox;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RangerCheckBox;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WizardCheckBox;
        
        #line default
        #line hidden
        
        
        #line 508 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ElementalistCheckBox;
        
        #line default
        #line hidden
        
        
        #line 509 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChanterCheckBox;
        
        #line default
        #line hidden
        
        
        #line 510 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PriestCheckBox;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox GunnerCheckBox;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BardCheckBox;
        
        #line default
        #line hidden
        
        
        #line 513 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RiderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectAllClassesButton;
        
        #line default
        #line hidden
        
        
        #line 519 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAllClassesButton;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RacePermittedComboBox;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox GenderPermittedComboBox;
        
        #line default
        #line hidden
        
        
        #line 572 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ActivationSkillTextBox;
        
        #line default
        #line hidden
        
        
        #line 581 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ActivationModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 595 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExtraInventoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 604 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DisassemblyItemTextBox;
        
        #line default
        #line hidden
        
        
        #line 622 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetailedTradeCostTextBlock;
        
        #line default
        #line hidden
        
        
        #line 630 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PermissionSummaryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 638 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClassRestrictionSummaryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 645 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WeaponSpecificBorder;
        
        #line default
        #line hidden
        
        
        #line 661 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WeaponTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 681 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DmgDecalTextBox;
        
        #line default
        #line hidden
        
        
        #line 690 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EquipBoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 699 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CombatEquipBoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 713 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinDamageTextBox;
        
        #line default
        #line hidden
        
        
        #line 715 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxDamageTextBox;
        
        #line default
        #line hidden
        
        
        #line 726 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StrTextBox;
        
        #line default
        #line hidden
        
        
        #line 728 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AgiTextBox;
        
        #line default
        #line hidden
        
        
        #line 739 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox KnoTextBox;
        
        #line default
        #line hidden
        
        
        #line 741 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HitAccuracyTextBox;
        
        #line default
        #line hidden
        
        
        #line 752 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CriticalTextBox;
        
        #line default
        #line hidden
        
        
        #line 754 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ParryTextBox;
        
        #line default
        #line hidden
        
        
        #line 765 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagicalSkillBoostTextBox;
        
        #line default
        #line hidden
        
        
        #line 767 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagicalHitAccuracyTextBox;
        
        #line default
        #line hidden
        
        
        #line 778 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AttackTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 786 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttackDelayTextBox;
        
        #line default
        #line hidden
        
        
        #line 797 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttackRangeTextBox;
        
        #line default
        #line hidden
        
        
        #line 799 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RobotNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 805 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BasicBonusBorder;
        
        #line default
        #line hidden
        
        
        #line 828 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr1TextBox;
        
        #line default
        #line hidden
        
        
        #line 837 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr2TextBox;
        
        #line default
        #line hidden
        
        
        #line 846 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr3TextBox;
        
        #line default
        #line hidden
        
        
        #line 855 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr4TextBox;
        
        #line default
        #line hidden
        
        
        #line 868 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr5TextBox;
        
        #line default
        #line hidden
        
        
        #line 877 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr6TextBox;
        
        #line default
        #line hidden
        
        
        #line 886 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr7TextBox;
        
        #line default
        #line hidden
        
        
        #line 895 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttr8TextBox;
        
        #line default
        #line hidden
        
        
        #line 903 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AdvancedBonusBorder;
        
        #line default
        #line hidden
        
        
        #line 926 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA1TextBox;
        
        #line default
        #line hidden
        
        
        #line 935 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA2TextBox;
        
        #line default
        #line hidden
        
        
        #line 944 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA3TextBox;
        
        #line default
        #line hidden
        
        
        #line 953 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA4TextBox;
        
        #line default
        #line hidden
        
        
        #line 966 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA5TextBox;
        
        #line default
        #line hidden
        
        
        #line 975 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA6TextBox;
        
        #line default
        #line hidden
        
        
        #line 984 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA7TextBox;
        
        #line default
        #line hidden
        
        
        #line 993 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrA8TextBox;
        
        #line default
        #line hidden
        
        
        #line 1001 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SpecialEffectBorder;
        
        #line default
        #line hidden
        
        
        #line 1024 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrB1TextBox;
        
        #line default
        #line hidden
        
        
        #line 1033 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrB2TextBox;
        
        #line default
        #line hidden
        
        
        #line 1046 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrB3TextBox;
        
        #line default
        #line hidden
        
        
        #line 1055 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BonusAttrB4TextBox;
        
        #line default
        #line hidden
        
        
        #line 1063 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CombatEnhanceBorder;
        
        #line default
        #line hidden
        
        
        #line 1081 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BurnOnAttackTextBox;
        
        #line default
        #line hidden
        
        
        #line 1083 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BurnOnDefendTextBox;
        
        #line default
        #line hidden
        
        
        #line 1096 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PolishBurnOnAttackTextBox;
        
        #line default
        #line hidden
        
        
        #line 1098 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PolishBurnOnDefendTextBox;
        
        #line default
        #line hidden
        
        
        #line 1113 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BmRestrictCategoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1115 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DisassemblyItemTextBox2;
        
        #line default
        #line hidden
        
        
        #line 1137 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescLongTextBox;
        
        #line default
        #line hidden
        
        
        #line 1152 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaterialTextBox;
        
        #line default
        #line hidden
        
        
        #line 1154 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IconNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 1165 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MeshTextBox;
        
        #line default
        #line hidden
        
        
        #line 1167 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VisualSlotTextBox;
        
        #line default
        #line hidden
        
        
        #line 1188 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultColorMTextBox;
        
        #line default
        #line hidden
        
        
        #line 1190 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultColorFTextBox;
        
        #line default
        #line hidden
        
        
        #line 1205 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DisposableTradeItemTextBox;
        
        #line default
        #line hidden
        
        
        #line 1207 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DisposableTradeItemCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 1216 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CanPackCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 1226 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 1231 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 1237 "..\..\..\ItemEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/itemeditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ItemEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ItemIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ItemTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.IdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.LocalizedNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ArmorTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.ItemTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.QualityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.EquipmentSlotsComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.PriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.LevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.MaxStackCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.DodgeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.PhysicalDefendTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.MagicalResistTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.EquipTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.ExpireTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.UsableRankMinTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.PurchableRankMinTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.ScaleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.ExtractSkinTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.TradeModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 285 "..\..\..\ItemEditorWindow.xaml"
            this.TradeModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TradeModeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 26:
            this.BasicPriceGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 27:
            this.TradePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.ExtraCurrencyPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 29:
            this.ExtraCurrencyItemComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 30:
            this.ExtraCurrencyItemCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.TradeInItemPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 32:
            this.TradeInItemListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 34:
            this.AddTradeInItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 359 "..\..\..\ItemEditorWindow.xaml"
            this.AddTradeInItemButton.Click += new System.Windows.RoutedEventHandler(this.AddTradeInItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.AddCommonTradeItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 360 "..\..\..\ItemEditorWindow.xaml"
            this.AddCommonTradeItemButton.Click += new System.Windows.RoutedEventHandler(this.AddCommonTradeItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.TradeModeExampleBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 37:
            this.TradeModeExampleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.TradeCostSummaryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.CanExchangeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 40:
            this.CanSellToNpcCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.ItemDropPermittedCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.CanSplitCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.CanApExtractionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.BreakableCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 45:
            this.CanDepositToCharacterWarehouseCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 46:
            this.CanDepositToAccountWarehouseCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 47:
            this.CanDepositToGuildWarehouseCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 48:
            this.SoulBindCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 49:
            this.RemoveWhenLogoutCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 50:
            this.LoreCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 51:
            this.WarriorCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 52:
            this.ScoutCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 53:
            this.MageCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 54:
            this.ClericCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 55:
            this.EngineerCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 56:
            this.ArtistCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 57:
            this.FighterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 58:
            this.KnightCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 59:
            this.AssassinCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 60:
            this.RangerCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 61:
            this.WizardCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 62:
            this.ElementalistCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 63:
            this.ChanterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 64:
            this.PriestCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 65:
            this.GunnerCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 66:
            this.BardCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 67:
            this.RiderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 68:
            this.SelectAllClassesButton = ((System.Windows.Controls.Button)(target));
            
            #line 518 "..\..\..\ItemEditorWindow.xaml"
            this.SelectAllClassesButton.Click += new System.Windows.RoutedEventHandler(this.SelectAllClassesButton_Click);
            
            #line default
            #line hidden
            return;
            case 69:
            this.ClearAllClassesButton = ((System.Windows.Controls.Button)(target));
            
            #line 519 "..\..\..\ItemEditorWindow.xaml"
            this.ClearAllClassesButton.Click += new System.Windows.RoutedEventHandler(this.ClearAllClassesButton_Click);
            
            #line default
            #line hidden
            return;
            case 70:
            this.RacePermittedComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 71:
            this.GenderPermittedComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 72:
            this.ActivationSkillTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 73:
            this.ActivationModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 74:
            this.ExtraInventoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 75:
            this.DisassemblyItemTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 76:
            this.DetailedTradeCostTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 77:
            this.PermissionSummaryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 78:
            this.ClassRestrictionSummaryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 79:
            this.WeaponSpecificBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 80:
            this.WeaponTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 81:
            this.DmgDecalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 82:
            this.EquipBoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 83:
            this.CombatEquipBoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 84:
            this.MinDamageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 85:
            this.MaxDamageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 86:
            this.StrTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 87:
            this.AgiTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 88:
            this.KnoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 89:
            this.HitAccuracyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 90:
            this.CriticalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 91:
            this.ParryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 92:
            this.MagicalSkillBoostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 93:
            this.MagicalHitAccuracyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 94:
            this.AttackTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 95:
            this.AttackDelayTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 96:
            this.AttackRangeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 97:
            this.RobotNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 98:
            this.BasicBonusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 99:
            this.BonusAttr1TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 100:
            this.BonusAttr2TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 101:
            this.BonusAttr3TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 102:
            this.BonusAttr4TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 103:
            this.BonusAttr5TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 104:
            this.BonusAttr6TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 105:
            this.BonusAttr7TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 106:
            this.BonusAttr8TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 107:
            this.AdvancedBonusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 108:
            this.BonusAttrA1TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 109:
            this.BonusAttrA2TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 110:
            this.BonusAttrA3TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 111:
            this.BonusAttrA4TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 112:
            this.BonusAttrA5TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 113:
            this.BonusAttrA6TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 114:
            this.BonusAttrA7TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 115:
            this.BonusAttrA8TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 116:
            this.SpecialEffectBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 117:
            this.BonusAttrB1TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 118:
            this.BonusAttrB2TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 119:
            this.BonusAttrB3TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 120:
            this.BonusAttrB4TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 121:
            this.CombatEnhanceBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 122:
            this.BurnOnAttackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 123:
            this.BurnOnDefendTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 124:
            this.PolishBurnOnAttackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 125:
            this.PolishBurnOnDefendTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 126:
            this.BmRestrictCategoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 127:
            this.DisassemblyItemTextBox2 = ((System.Windows.Controls.TextBox)(target));
            return;
            case 128:
            this.DescLongTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 129:
            this.MaterialTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 130:
            this.IconNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 131:
            this.MeshTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 132:
            this.VisualSlotTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 133:
            this.DefaultColorMTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 134:
            this.DefaultColorFTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 135:
            this.DisposableTradeItemTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 136:
            this.DisposableTradeItemCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 137:
            this.CanPackCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 138:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 1229 "..\..\..\ItemEditorWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 139:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 1235 "..\..\..\ItemEditorWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 140:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 1240 "..\..\..\ItemEditorWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 33:
            
            #line 351 "..\..\..\ItemEditorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveTradeInItemButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

