using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using CommunityToolkit.Mvvm.ComponentModel;

namespace AionItemEditor.Models;

/// <summary>
/// 客户端物品数据模型
/// 对应XML中的client_item元素
/// </summary>
[XmlRoot("client_item")]
public partial class ClientItem : ObservableValidator
{
    #region 基础信息 - Basic Information

    [ObservableProperty]
    [XmlElement("id")]
    [Display(Name = "物品ID", Description = "物品的唯一标识符")]
    [Required(ErrorMessage = "物品ID不能为空")]
    private int id;

    [ObservableProperty]
    [XmlElement("name")]
    [Display(Name = "物品名称", Description = "物品的内部名称")]
    [Required(ErrorMessage = "物品名称不能为空")]
    private string name = string.Empty;

    [ObservableProperty]
    [XmlElement("desc")]
    [Display(Name = "描述键", Description = "物品描述的本地化键")]
    private string desc = string.Empty;

    [ObservableProperty]
    [XmlElement("armor_type")]
    [Display(Name = "护甲类型", Description = "护甲的类型（如clothes, leather, chain, plate等）")]
    private string armorType = string.Empty;

    [ObservableProperty]
    [XmlElement("item_type")]
    [Display(Name = "物品类型", Description = "物品的基础类型（如normal, quest, abyss等）")]
    private string itemType = "normal";

    [ObservableProperty]
    [XmlElement("material")]
    [Display(Name = "材质", Description = "物品的材质类型")]
    private string material = string.Empty;

    [ObservableProperty]
    [XmlElement("icon_name")]
    [Display(Name = "图标名称", Description = "物品图标的文件名")]
    private string iconName = string.Empty;

    #endregion

    #region 经济属性 - Economic Properties

    [ObservableProperty]
    [XmlElement("price")]
    [Display(Name = "价格", Description = "物品的基础价格")]
    [Range(0, int.MaxValue, ErrorMessage = "价格不能为负数")]
    private int price;

    [ObservableProperty]
    [XmlElement("max_stack_count")]
    [Display(Name = "最大堆叠数量", Description = "物品可以堆叠的最大数量")]
    [Range(1, 999, ErrorMessage = "堆叠数量必须在1-999之间")]
    private int maxStackCount = 1;

    [ObservableProperty]
    [XmlElement("can_sell_to_npc")]
    [Display(Name = "可售给NPC", Description = "是否可以出售给NPC商人")]
    private bool canSellToNpc = true;

    #endregion

    #region 存储权限 - Storage Permissions

    [ObservableProperty]
    [XmlElement("can_deposit_to_character_warehouse")]
    [Display(Name = "可存入角色仓库", Description = "是否可以存入角色仓库")]
    private bool canDepositToCharacterWarehouse = true;

    [ObservableProperty]
    [XmlElement("can_deposit_to_account_warehouse")]
    [Display(Name = "可存入账户仓库", Description = "是否可以存入账户仓库")]
    private bool canDepositToAccountWarehouse = true;

    [ObservableProperty]
    [XmlElement("can_deposit_to_guild_warehouse")]
    [Display(Name = "可存入军团仓库", Description = "是否可以存入军团仓库")]
    private bool canDepositToGuildWarehouse = true;

    #endregion

    #region 物品属性 - Item Properties

    [ObservableProperty]
    [XmlElement("equipment_slots")]
    [Display(Name = "装备部位", Description = "物品装备的身体部位")]
    private string equipmentSlots = string.Empty;

    [ObservableProperty]
    [XmlElement("breakable")]
    [Display(Name = "可损坏", Description = "物品是否会损坏")]
    private bool breakable = true;

    [ObservableProperty]
    [XmlElement("soul_bind")]
    [Display(Name = "灵魂绑定", Description = "物品是否灵魂绑定")]
    private bool soulBind = false;

    [ObservableProperty]
    [XmlElement("remove_when_logout")]
    [Display(Name = "登出时移除", Description = "登出时是否移除物品")]
    private bool removeWhenLogout = false;

    [ObservableProperty]
    [XmlElement("gender_permitted")]
    [Display(Name = "性别限制", Description = "允许使用的性别（all, male, female）")]
    private string genderPermitted = "all";

    #endregion

    #region 职业限制 - Class Restrictions

    [ObservableProperty]
    [XmlElement("warrior")]
    [Display(Name = "战士", Description = "战士职业是否可使用（1=可用，0=不可用）")]
    private int warrior = 1;

    [ObservableProperty]
    [XmlElement("scout")]
    [Display(Name = "侦察兵", Description = "侦察兵职业是否可使用")]
    private int scout = 1;

    [ObservableProperty]
    [XmlElement("mage")]
    [Display(Name = "法师", Description = "法师职业是否可使用")]
    private int mage = 1;

    [ObservableProperty]
    [XmlElement("cleric")]
    [Display(Name = "牧师", Description = "牧师职业是否可使用")]
    private int cleric = 1;

    [ObservableProperty]
    [XmlElement("engineer")]
    [Display(Name = "工程师", Description = "工程师职业是否可使用")]
    private int engineer = 1;

    [ObservableProperty]
    [XmlElement("artist")]
    [Display(Name = "艺术家", Description = "艺术家职业是否可使用")]
    private int artist = 1;

    [ObservableProperty]
    [XmlElement("fighter")]
    [Display(Name = "格斗家", Description = "格斗家职业是否可使用")]
    private int fighter = 1;

    [ObservableProperty]
    [XmlElement("knight")]
    [Display(Name = "骑士", Description = "骑士职业是否可使用")]
    private int knight = 1;

    [ObservableProperty]
    [XmlElement("assassin")]
    [Display(Name = "刺客", Description = "刺客职业是否可使用")]
    private int assassin = 1;

    [ObservableProperty]
    [XmlElement("ranger")]
    [Display(Name = "游侠", Description = "游侠职业是否可使用")]
    private int ranger = 1;

    [ObservableProperty]
    [XmlElement("wizard")]
    [Display(Name = "魔导星", Description = "魔导星职业是否可使用")]
    private int wizard = 1;

    [ObservableProperty]
    [XmlElement("elementalist")]
    [Display(Name = "精灵星", Description = "精灵星职业是否可使用")]
    private int elementalist = 1;

    [ObservableProperty]
    [XmlElement("chanter")]
    [Display(Name = "护法星", Description = "护法星职业是否可使用")]
    private int chanter = 1;

    [ObservableProperty]
    [XmlElement("priest")]
    [Display(Name = "治愈星", Description = "治愈星职业是否可使用")]
    private int priest = 1;

    [ObservableProperty]
    [XmlElement("gunner")]
    [Display(Name = "枪炮兵", Description = "枪炮兵职业是否可使用")]
    private int gunner = 1;

    [ObservableProperty]
    [XmlElement("bard")]
    [Display(Name = "吟游诗人", Description = "吟游诗人职业是否可使用")]
    private int bard = 1;

    [ObservableProperty]
    [XmlElement("rider")]
    [Display(Name = "驭灵师", Description = "驭灵师职业是否可使用")]
    private int rider = 1;

    #endregion

    #region 强化属性 - Enhancement Properties

    [ObservableProperty]
    [XmlElement("option_slot_value")]
    [Display(Name = "选项槽数值", Description = "物品的选项槽数值")]
    [Range(0, 10, ErrorMessage = "选项槽数值必须在0-10之间")]
    private int optionSlotValue = 0;

    [ObservableProperty]
    [XmlElement("special_slot_value")]
    [Display(Name = "特殊槽数值", Description = "物品的特殊槽数值")]
    [Range(0, 10, ErrorMessage = "特殊槽数值必须在0-10之间")]
    private int specialSlotValue = 0;

    [ObservableProperty]
    [XmlElement("option_slot_bonus")]
    [Display(Name = "选项槽奖励", Description = "选项槽的奖励值")]
    private int optionSlotBonus = 0;

    [ObservableProperty]
    [XmlElement("bonus_apply")]
    [Display(Name = "奖励应用方式", Description = "奖励的应用方式（equip=装备时）")]
    private string bonusApply = "equip";

    [ObservableProperty]
    [XmlElement("no_enchant")]
    [Display(Name = "禁止强化", Description = "是否禁止强化")]
    private bool noEnchant = false;

    [ObservableProperty]
    [XmlElement("max_enchant_value")]
    [Display(Name = "最大强化值", Description = "物品可强化的最大等级")]
    [Range(0, 20, ErrorMessage = "最大强化值必须在0-20之间")]
    private int maxEnchantValue = 15;

    [ObservableProperty]
    [XmlElement("can_proc_enchant")]
    [Display(Name = "可触发强化", Description = "是否可以触发强化")]
    private bool canProcEnchant = true;

    [ObservableProperty]
    [XmlElement("can_composite_weapon")]
    [Display(Name = "可合成武器", Description = "是否可以合成武器")]
    private bool canCompositeWeapon = true;

    #endregion

    #region 外观和音效 - Appearance and Sound

    [ObservableProperty]
    [XmlElement("cannot_changeskin")]
    [Display(Name = "禁止换肤", Description = "是否禁止更换外观")]
    private int cannotChangeskin = 0;

    [ObservableProperty]
    [XmlElement("ui_sound_type")]
    [Display(Name = "UI音效类型", Description = "物品的UI音效文件路径")]
    private string uiSoundType = string.Empty;

    [ObservableProperty]
    [XmlElement("mesh")]
    [Display(Name = "模型", Description = "物品的3D模型名称")]
    private string mesh = string.Empty;

    [ObservableProperty]
    [XmlElement("mesh_change")]
    [Display(Name = "模型变化", Description = "模型变化标识")]
    private int meshChange = 0;

    [ObservableProperty]
    [XmlElement("can_dye")]
    [Display(Name = "可染色", Description = "是否可以染色")]
    private int canDye = 0;

    #endregion

    #region 特殊属性 - Special Properties

    [ObservableProperty]
    [XmlElement("cash_item")]
    [Display(Name = "商城物品", Description = "是否为商城物品")]
    private int cashItem = 0;

    [ObservableProperty]
    [XmlElement("can_split")]
    [Display(Name = "可分割", Description = "是否可以分割")]
    private bool canSplit = false;

    [ObservableProperty]
    [XmlElement("item_drop_permitted")]
    [Display(Name = "允许掉落", Description = "是否允许掉落")]
    private bool itemDropPermitted = false;

    [ObservableProperty]
    [XmlElement("bm_restrict_category")]
    [Display(Name = "交易限制类别", Description = "交易限制的类别")]
    private int bmRestrictCategory = 1;

    [ObservableProperty]
    [XmlElement("can_ap_extraction")]
    [Display(Name = "可提取AP", Description = "是否可以提取深渊点数")]
    private bool canApExtraction = false;

    [ObservableProperty]
    [XmlElement("can_polish")]
    [Display(Name = "可抛光", Description = "是否可以抛光")]
    private bool canPolish = true;

    [ObservableProperty]
    [XmlElement("polish_burn_on_attack")]
    [Display(Name = "攻击时抛光消耗", Description = "攻击时抛光的消耗值")]
    private int polishBurnOnAttack = 29;

    [ObservableProperty]
    [XmlElement("polish_burn_on_defend")]
    [Display(Name = "防御时抛光消耗", Description = "防御时抛光的消耗值")]
    private int polishBurnOnDefend = 12;

    [ObservableProperty]
    [XmlElement("race_permitted")]
    [Display(Name = "种族限制", Description = "允许使用的种族（pc_light pc_dark）")]
    private string racePermitted = "pc_light pc_dark";

    #endregion

    #region 辅助方法 - Helper Methods

    /// <summary>
    /// 获取所有职业权限的字典
    /// </summary>
    public Dictionary<string, int> GetClassPermissions()
    {
        return new Dictionary<string, int>
        {
            ["战士"] = Warrior,
            ["侦察兵"] = Scout,
            ["法师"] = Mage,
            ["牧师"] = Cleric,
            ["工程师"] = Engineer,
            ["艺术家"] = Artist,
            ["格斗家"] = Fighter,
            ["骑士"] = Knight,
            ["刺客"] = Assassin,
            ["游侠"] = Ranger,
            ["魔导星"] = Wizard,
            ["精灵星"] = Elementalist,
            ["护法星"] = Chanter,
            ["治愈星"] = Priest,
            ["枪炮兵"] = Gunner,
            ["吟游诗人"] = Bard,
            ["驭灵师"] = Rider
        };
    }

    /// <summary>
    /// 设置所有职业权限
    /// </summary>
    public void SetAllClassPermissions(int value)
    {
        Warrior = Scout = Mage = Cleric = Engineer = Artist =
        Fighter = Knight = Assassin = Ranger = Wizard = Elementalist =
        Chanter = Priest = Gunner = Bard = Rider = value;
    }

    /// <summary>
    /// 检查物品是否有效
    /// </summary>
    public bool IsValid()
    {
        return Id > 0 && !string.IsNullOrWhiteSpace(Name);
    }

    /// <summary>
    /// 获取物品的显示名称
    /// </summary>
    public string DisplayName => $"[{Id}] {Name}";

    #endregion
}
