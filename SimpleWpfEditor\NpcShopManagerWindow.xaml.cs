using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor;

/// <summary>
/// NPC商店管理器窗口
/// </summary>
public partial class NpcShopManagerWindow : Window
{
    private readonly ILogger<NpcShopManagerWindow> _logger;
    private readonly NpcGoodsListXmlService _xmlService;
    private readonly StringService _stringService;
    private ObservableCollection<ClientNpcGoodsList> _shops;
    private CollectionViewSource _filteredShops;
    private string? _currentFilePath;
    private NpcGoodsListStatistics? _statistics;

    public NpcShopManagerWindow(ILogger<NpcShopManagerWindow> logger, NpcGoodsListXmlService xmlService, StringService stringService)
    {
        InitializeComponent();
        
        _logger = logger;
        _xmlService = xmlService;
        _stringService = stringService;
        _shops = new ObservableCollection<ClientNpcGoodsList>();
        _filteredShops = new CollectionViewSource { Source = _shops };
        
        InitializeUI();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        ShopsDataGrid.ItemsSource = _filteredShops.View;
        
        // 设置筛选
        _filteredShops.Filter += FilterShops;
        
        // 初始化筛选下拉框
        InitializeFilterComboBoxes();
    }

    /// <summary>
    /// 初始化筛选下拉框
    /// </summary>
    private void InitializeFilterComboBoxes()
    {
        // 商店类型筛选
        ShopTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 职业筛选
        ProfessionFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 等级筛选
        LevelFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 默认选择"全部"
        ShopTypeFilterComboBox.SelectedIndex = 0;
        ProfessionFilterComboBox.SelectedIndex = 0;
        LevelFilterComboBox.SelectedIndex = 0;
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 工具栏按钮事件
        OpenFileButton.Click += OpenFileButton_Click;
        SaveFileButton.Click += SaveFileButton_Click;
        SaveWithServerConversionButton.Click += SaveWithServerConversionButton_Click;
        
        // 搜索和筛选事件
        SearchTextBox.TextChanged += (s, e) => _filteredShops.View.Refresh();
        ShopTypeFilterComboBox.SelectionChanged += (s, e) => _filteredShops.View.Refresh();
        ProfessionFilterComboBox.SelectionChanged += (s, e) => _filteredShops.View.Refresh();
        LevelFilterComboBox.SelectionChanged += (s, e) => _filteredShops.View.Refresh();
        ClearFilterButton.Click += ClearFilterButton_Click;
        
        // 数据网格事件
        ShopsDataGrid.MouseDoubleClick += ShopsDataGrid_MouseDoubleClick;
        ShopsDataGrid.KeyDown += ShopsDataGrid_KeyDown;
    }

    /// <summary>
    /// 打开文件按钮点击
    /// </summary>
    private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择NPC商店文件",
                Filter = "NPC商店文件 (client_npc_goodslist.xml)|client_npc_goodslist.xml|XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadShopsFile(openFileDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开文件时出错");
            MessageBox.Show($"打开文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 保存文件按钮点击
    /// </summary>
    private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存NPC商店文件",
                    Filter = "NPC商店文件 (client_npc_goodslist.xml)|client_npc_goodslist.xml|XML文件 (*.xml)|*.xml",
                    DefaultExt = "xml",
                    FileName = "client_npc_goodslist.xml"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _currentFilePath = saveFileDialog.FileName;
                }
                else
                {
                    return;
                }
            }

            await SaveShopsFile(_currentFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件时出错");
            MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 保存并转换服务端按钮点击
    /// </summary>
    private async void SaveWithServerConversionButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_shops.Count == 0)
            {
                MessageBox.Show("没有商店数据需要转换", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 选择服务端文件保存目录
            var folderDialog = new OpenFileDialog
            {
                Title = "选择服务端文件保存目录",
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "选择文件夹",
                Filter = "文件夹|*.folder"
            };

            if (folderDialog.ShowDialog() == true)
            {
                var serverBasePath = Path.GetDirectoryName(folderDialog.FileName) ?? "";

                UpdateStatus("正在转换商店数据到服务端格式...");

                var success = await _xmlService.SaveWithServerConversionAsync(_currentFilePath ?? "", _shops.ToList(), serverBasePath);

                if (success)
                {
                    var message = $"商店数据转换完成！\n\n" +
                                  $"商店总数: {_shops.Count}\n" +
                                  $"文件保存位置: {Path.Combine(serverBasePath, "npc_goods.xml")}\n\n" +
                                  $"使用服务端根标签: <npc_goods>";

                    MessageBox.Show(message, "转换成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    UpdateStatus("服务端转换完成");
                }
                else
                {
                    MessageBox.Show("转换失败，请检查日志", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    UpdateStatus("服务端转换失败");
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"转换过程中出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("服务端转换失败");
        }
    }

    /// <summary>
    /// 清除筛选按钮点击
    /// </summary>
    private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Text = string.Empty;
        ShopTypeFilterComboBox.SelectedIndex = 0;
        ProfessionFilterComboBox.SelectedIndex = 0;
        LevelFilterComboBox.SelectedIndex = 0;
        _filteredShops.View.Refresh();
        UpdateStatus("已清除所有筛选条件");
    }

    /// <summary>
    /// 数据网格双击事件
    /// </summary>
    private void ShopsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (ShopsDataGrid.SelectedItem is ClientNpcGoodsList selectedShop)
        {
            OpenShopEditor(selectedShop);
        }
    }

    /// <summary>
    /// 数据网格键盘事件
    /// </summary>
    private void ShopsDataGrid_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && ShopsDataGrid.SelectedItem is ClientNpcGoodsList selectedShop)
        {
            OpenShopEditor(selectedShop);
            e.Handled = true;
        }
    }

    /// <summary>
    /// 加载商店文件
    /// </summary>
    private async Task LoadShopsFile(string filePath)
    {
        try
        {
            UpdateStatus("正在加载NPC商店文件...");
            
            var shops = await _xmlService.LoadNpcGoodsListsAsync(filePath);
            
            _shops.Clear();
            foreach (var shop in shops)
            {
                _shops.Add(shop);
            }
            
            _currentFilePath = filePath;
            _statistics = _xmlService.GetStatistics(shops);
            
            UpdateFilterComboBoxes();
            UpdateStatistics();
            UpdateStatus($"成功加载 {shops.Count} 个NPC商店");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载NPC商店文件时出错: {FilePath}", filePath);
            UpdateStatus($"加载文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 保存商店文件
    /// </summary>
    private async Task SaveShopsFile(string filePath)
    {
        try
        {
            UpdateStatus("正在保存NPC商店文件...");
            
            await _xmlService.SaveNpcGoodsListsAsync(filePath, _shops.ToList());
            
            UpdateStatus($"成功保存 {_shops.Count} 个NPC商店");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存NPC商店文件时出错: {FilePath}", filePath);
            UpdateStatus($"保存文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 打开商店编辑器
    /// </summary>
    private void OpenShopEditor(ClientNpcGoodsList shop)
    {
        try
        {
            var shopEditor = new NpcShopEditorWindow(shop, _stringService)
            {
                Owner = this
            };

            var result = shopEditor.ShowDialog();
            if (result == true && shopEditor.DataChanged)
            {
                // 更新原始数据
                UpdateShopData(shop, shopEditor.EditedShop);
                
                // 刷新显示
                _filteredShops.View.Refresh();
                
                UpdateStatus($"已更新商店: {shop.DisplayName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开商店编辑器时出错");
            MessageBox.Show($"打开编辑器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 更新商店数据
    /// </summary>
    private void UpdateShopData(ClientNpcGoodsList original, ClientNpcGoodsList edited)
    {
        original.Name = edited.Name;
        original.Desc = edited.Desc;
        original.GoodsList = edited.GoodsList.ToList();
    }

    /// <summary>
    /// 更新筛选下拉框
    /// </summary>
    private void UpdateFilterComboBoxes()
    {
        if (_statistics == null) return;

        // 更新商店类型筛选
        var currentShopType = GetComboBoxValue(ShopTypeFilterComboBox);
        ShopTypeFilterComboBox.Items.Clear();
        ShopTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var shopType in _statistics.ShopTypes.Keys.OrderBy(x => x))
        {
            ShopTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{shopType} ({_statistics.ShopTypes[shopType]})", Tag = shopType });
        }
        SetComboBoxValue(ShopTypeFilterComboBox, currentShopType);

        // 更新职业筛选
        var currentProfession = GetComboBoxValue(ProfessionFilterComboBox);
        ProfessionFilterComboBox.Items.Clear();
        ProfessionFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var profession in _statistics.ProfessionTypes.Keys.OrderBy(x => x))
        {
            ProfessionFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{profession} ({_statistics.ProfessionTypes[profession]})", Tag = profession });
        }
        SetComboBoxValue(ProfessionFilterComboBox, currentProfession);

        // 更新等级筛选
        var currentLevel = GetComboBoxValue(LevelFilterComboBox);
        LevelFilterComboBox.Items.Clear();
        LevelFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var level in _statistics.LevelTypes.Keys.OrderBy(x => x))
        {
            LevelFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{level} ({_statistics.LevelTypes[level]})", Tag = level });
        }
        SetComboBoxValue(LevelFilterComboBox, currentLevel);
    }

    /// <summary>
    /// 筛选商店
    /// </summary>
    private void FilterShops(object sender, FilterEventArgs e)
    {
        if (e.Item is not ClientNpcGoodsList shop)
        {
            e.Accepted = false;
            return;
        }

        // 搜索文本筛选
        var searchText = SearchTextBox.Text?.Trim().ToLower();
        if (!string.IsNullOrEmpty(searchText))
        {
            var matchesSearch = shop.Id.ToString().Contains(searchText) ||
                               shop.Name.ToLower().Contains(searchText) ||
                               shop.Desc.ToLower().Contains(searchText) ||
                               shop.DisplayName.ToLower().Contains(searchText);
            
            if (!matchesSearch)
            {
                e.Accepted = false;
                return;
            }
        }

        // 商店类型筛选
        var shopTypeFilter = GetComboBoxValue(ShopTypeFilterComboBox);
        if (!string.IsNullOrEmpty(shopTypeFilter) && shop.ShopType != shopTypeFilter)
        {
            e.Accepted = false;
            return;
        }

        // 职业筛选
        var professionFilter = GetComboBoxValue(ProfessionFilterComboBox);
        if (!string.IsNullOrEmpty(professionFilter) && shop.ProfessionType != professionFilter)
        {
            e.Accepted = false;
            return;
        }

        // 等级筛选
        var levelFilter = GetComboBoxValue(LevelFilterComboBox);
        if (!string.IsNullOrEmpty(levelFilter) && shop.LevelType != levelFilter)
        {
            e.Accepted = false;
            return;
        }

        e.Accepted = true;
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        if (_statistics == null) return;
        
        TotalShopsTextBlock.Text = $"总商店: {_statistics.TotalShops}";
        TotalItemsTextBlock.Text = $"总商品: {_statistics.TotalItems}";
        AvgItemsTextBlock.Text = $"平均: {_statistics.AverageItemsPerShop:F1}";
        
        // 更新筛选结果数量
        var filteredCount = _filteredShops.View.Cast<object>().Count();
        FilteredCountTextBlock.Text = $"筛选结果: {filteredCount}";
    }

    /// <summary>
    /// 获取下拉框值
    /// </summary>
    private string GetComboBoxValue(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
        {
            return item.Tag.ToString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 设置下拉框值
    /// </summary>
    private void SetComboBoxValue(ComboBox comboBox, string value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Tag?.ToString() == value)
            {
                comboBox.SelectedItem = item;
                return;
            }
        }
        comboBox.SelectedIndex = 0; // 默认选择"全部"
    }

    /// <summary>
    /// 更新状态
    /// </summary>
    private void UpdateStatus(string message)
    {
        StatusTextBlock.Text = message;
        _logger.LogInformation(message);
        
        // 更新筛选结果数量
        if (_filteredShops?.View != null)
        {
            var filteredCount = _filteredShops.View.Cast<object>().Count();
            FilteredCountTextBlock.Text = $"筛选结果: {filteredCount}";
        }
    }
}
