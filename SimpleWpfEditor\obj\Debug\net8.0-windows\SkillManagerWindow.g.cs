﻿#pragma checksum "..\..\..\SkillManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8EBDC471D5A70EFEA6183588E8FF164C56DD4F6B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// SkillManagerWindow
    /// </summary>
    public partial class SkillManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveFileButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveWithServerConversionButton;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LevelFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSkillButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteSkillButton;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SkillsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem EditSkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem CopySkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem DeleteSkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SkillEditPanel;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillDescTextBox;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillLevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillMaxLevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillMpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillHpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillDpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillCastTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillCoolTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillDurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillTargetTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillTargetRangeTextBox;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillTargetMaxCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillEffectTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillEffectValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillAnimationTextBox;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillIconTextBox;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSkillButton;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelEditButton;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSkillsText;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\SkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilteredSkillsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/skillmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SkillManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            return;
            case 2:
            this.SaveFileButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.SaveWithServerConversionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.SkillTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.LevelFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.ClearFilterButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.AddSkillButton = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.DeleteSkillButton = ((System.Windows.Controls.Button)(target));
            return;
            case 10:
            this.SkillsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 113 "..\..\..\SkillManagerWindow.xaml"
            this.SkillsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SkillsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.EditSkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 124 "..\..\..\SkillManagerWindow.xaml"
            this.EditSkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.EditSkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CopySkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 125 "..\..\..\SkillManagerWindow.xaml"
            this.CopySkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.CopySkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DeleteSkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 127 "..\..\..\SkillManagerWindow.xaml"
            this.DeleteSkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.DeleteSkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.SkillEditPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.SkillIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.SkillNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.SkillDescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SkillTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.SkillLevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.SkillMaxLevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.SkillMpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.SkillHpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.SkillDpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.SkillCastTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.SkillCoolTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.SkillDurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.SkillTargetTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.SkillTargetRangeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.SkillTargetMaxCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.SkillEffectTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.SkillEffectValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.SkillAnimationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.SkillIconTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.SaveSkillButton = ((System.Windows.Controls.Button)(target));
            
            #line 304 "..\..\..\SkillManagerWindow.xaml"
            this.SaveSkillButton.Click += new System.Windows.RoutedEventHandler(this.SaveSkillButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.CancelEditButton = ((System.Windows.Controls.Button)(target));
            
            #line 306 "..\..\..\SkillManagerWindow.xaml"
            this.CancelEditButton.Click += new System.Windows.RoutedEventHandler(this.CancelEditButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.TotalSkillsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.FilteredSkillsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

