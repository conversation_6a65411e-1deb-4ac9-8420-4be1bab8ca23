﻿#pragma checksum "..\..\..\ProfessionalSkillManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6E89C395E85ACEFBF7AC9A8698B3D9C2B285D363"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// ProfessionalSkillManagerWindow
    /// </summary>
    public partial class ProfessionalSkillManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveFileButton;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSkillButton;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ActivationFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SkillsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem EditSkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem CopySkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem DeleteSkillMenuItem;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SkillEditPanel;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillDescTextBox;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillDescLongTextBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillSubTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillActivationComboBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillIconNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CastingDelayTextBox;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CancelRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ObstacleTextBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MoveCastingComboBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AutoAttackComboBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CostParameterComboBox;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostEndTextBox;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostChargeWeaponTextBox;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostChargeArmorTextBox;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PolishChargeWeaponTextBox;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DispelCategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetSlotComboBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox HostileTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DelayTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetMaxcountTextBox;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FirstTargetComboBox;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetRangeComboBox;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetSpeciesRestrictionComboBox;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetRelationRestrictionComboBox;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MotionNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MotionPlaySpeedTextBox;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox InstantSkillCheckBox;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PeaceSkillCheckBox;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowWeaponCheckBox;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1TypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Effect1TargetTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Remain1TextBox;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Remain2TextBox;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1EffectIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Reserved2TextBox;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Reserved4TextBox;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Reserved13TextBox;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Effect1Reserved14TextBox;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillCategoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DelayIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 452 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ChainCategoryLevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SkillGroupNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescAbnormalTextBox;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescPrechainCategoryNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSkillButton;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelEditButton;
        
        #line default
        #line hidden
        
        
        #line 487 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 490 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSkillsText;
        
        #line default
        #line hidden
        
        
        #line 492 "..\..\..\ProfessionalSkillManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilteredSkillsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/professionalskillmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SaveFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.SaveFileButton.Click += new System.Windows.RoutedEventHandler(this.SaveFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddSkillButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.AddSkillButton.Click += new System.Windows.RoutedEventHandler(this.AddSkillButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 58 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 62 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ActivationFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 71 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.ActivationFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ActivationFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ClearFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.ClearFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SkillsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 103 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.SkillsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SkillsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.EditSkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 114 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.EditSkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.EditSkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CopySkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 115 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.CopySkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.CopySkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DeleteSkillMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 117 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.DeleteSkillMenuItem.Click += new System.Windows.RoutedEventHandler(this.DeleteSkillMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SkillEditPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.SkillIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.SkillNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SkillDescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.SkillDescLongTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.SkillTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.SkillSubTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.SkillActivationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.SkillIconNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.CastingDelayTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.CancelRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.ObstacleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.MoveCastingComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.AutoAttackComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            this.CostParameterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.CostEndTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.CostChargeWeaponTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.CostChargeArmorTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.PolishChargeWeaponTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.DispelCategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 32:
            this.TargetSlotComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 33:
            this.HostileTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.DelayTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.TargetMaxcountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.FirstTargetComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 37:
            this.TargetRangeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 38:
            this.TargetSpeciesRestrictionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 39:
            this.TargetRelationRestrictionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 40:
            this.MotionNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 41:
            this.MotionPlaySpeedTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 42:
            this.InstantSkillCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.PeaceSkillCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.ShowWeaponCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 45:
            this.Effect1TypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.Effect1TargetTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 47:
            this.Effect1Remain1TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 48:
            this.Effect1Remain2TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 49:
            this.Effect1EffectIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 50:
            this.Effect1Reserved2TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 51:
            this.Effect1Reserved4TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 52:
            this.Effect1Reserved13TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 53:
            this.Effect1Reserved14TextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 54:
            this.SkillCategoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 55:
            this.DelayIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 56:
            this.ChainCategoryLevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 57:
            this.SkillGroupNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 58:
            this.DescAbnormalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 59:
            this.DescPrechainCategoryNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 60:
            this.SaveSkillButton = ((System.Windows.Controls.Button)(target));
            
            #line 470 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.SaveSkillButton.Click += new System.Windows.RoutedEventHandler(this.SaveSkillButton_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.CancelEditButton = ((System.Windows.Controls.Button)(target));
            
            #line 472 "..\..\..\ProfessionalSkillManagerWindow.xaml"
            this.CancelEditButton.Click += new System.Windows.RoutedEventHandler(this.CancelEditButton_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 63:
            this.TotalSkillsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 64:
            this.FilteredSkillsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

