using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor;

/// <summary>
/// 技能基础数据测试类
/// </summary>
public class SkillBaseClientTest
{
    private readonly SkillBaseXmlService _xmlService;

    public SkillBaseClientTest()
    {
        // 创建简单的空日志记录器
        var loggerFactory = LoggerFactory.Create(builder => { });
        var logger = loggerFactory.CreateLogger<SkillBaseXmlService>();
        _xmlService = new SkillBaseXmlService(logger);
    }

    /// <summary>
    /// 测试创建和保存技能基础数据
    /// </summary>
    public async Task TestCreateAndSaveSkillBaseClient()
    {
        try
        {
            Console.WriteLine("=== 技能基础数据测试开始 ===");

            // 创建测试技能数据
            var testSkill = new SkillBaseClient
            {
                Id = 21368,
                Name = "IU_Damage",
                Desc = "STR_IU_Heal",
                Type = "Physical",
                SubType = "Attack",
                SkillCategory = "SKILLCTG_NONE",
                SkillIconName = "icon_skill_idevent_Areaattack",
                ActivationAttribute = "Active",
                
                // 消耗相关
                CostParameter = "MP",
                CostEnd = 0,
                PenaltyNoCastingTimeSucc = 0,
                
                // 目标相关
                TargetSlot = "none",
                HostileType = "none",
                DelayType = 0,
                DelayTime = 0,
                TargetMaxcount = 4,
                FirstTarget = "Me",
                FirstTargetValidDistance = 6,
                TargetRange = "",
                TargetRangeAreaType = "FireBall",
                TargetRangeOpt1 = 8,
                TargetRangeOpt3 = 4,
                TargetSpeciesRestriction = "All",
                TargetRelationRestriction = "Enemy",
                
                // 行为控制
                CancelRate = 0,
                Obstacle = 4,
                CastingDelay = 0,
                MoveCasting = 1,
                AutoAttack = "stop",
                
                // 动作和动画
                MotionName = "",
                MotionPlaySpeed = 50,
                
                // 效果1
                Effect1Type = "NoReduceSpellATK_Instant",
                Effect1NoResist = 1,
                Effect1TargetType = "Target_Only",
                Effect1Reserved1 = 0,
                Effect1Reserved2 = 12000,
                Effect1Reserved3 = 0,
                Effect1Reserved4 = 0,
                Effect1Reserved6 = 0,
                Effect1Reserved10 = "earth",
                Effect1Reserved11 = 255,
                Effect1CriticalProbMod1 = 100,
                
                // 效果2
                Effect2Type = "DispelBuff",
                Effect2NoResist = 1,
                Effect2TargetType = "Target_Only",
                Effect2CondPreeffect = "e1",
                Effect2Reserved1 = 0,
                Effect2Reserved2 = 3,
                Effect2Reserved10 = "earth",
                Effect2Reserved11 = 100,
                Effect2Reserved12 = 120,
                Effect2Reserved15 = 0,
                Effect2Reserved16 = 10,
                Effect2Reserved17 = 0,
                Effect2Reserved18 = 10,
                Effect2CriticalProbMod2 = 100
            };

            Console.WriteLine($"创建测试技能: ID={testSkill.Id}, Name={testSkill.Name}");

            // 创建技能列表
            var skills = new List<SkillBaseClient> { testSkill };

            // 保存到临时文件
            var tempFile = Path.Combine(Path.GetTempPath(), "test_skill_base_client.xml");
            Console.WriteLine($"保存到临时文件: {tempFile}");

            await _xmlService.SaveSkillsToXmlAsync(tempFile, skills);
            Console.WriteLine("保存成功！");

            // 读取并验证
            Console.WriteLine("读取并验证文件...");
            var loadedSkills = await _xmlService.LoadSkillsFromXmlAsync(tempFile);

            if (loadedSkills.Count == 1)
            {
                var loadedSkill = loadedSkills[0];
                Console.WriteLine($"验证成功: ID={loadedSkill.Id}, Name={loadedSkill.Name}");
                Console.WriteLine($"效果1类型: {loadedSkill.Effect1Type}");
                Console.WriteLine($"效果2类型: {loadedSkill.Effect2Type}");
                Console.WriteLine($"目标关系限制: {loadedSkill.TargetRelationRestriction}");
            }
            else
            {
                Console.WriteLine($"错误: 期望1个技能，实际加载了{loadedSkills.Count}个");
            }

            // 清理临时文件
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
                Console.WriteLine("清理临时文件完成");
            }

            Console.WriteLine("=== 技能基础数据测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static async Task<string> RunAllTests()
    {
        try
        {
            var test = new SkillBaseClientTest();
            await test.TestCreateAndSaveSkillBaseClient();
            return "技能基础数据测试完成！所有测试通过。";
        }
        catch (Exception ex)
        {
            return $"测试失败: {ex.Message}";
        }
    }
}
