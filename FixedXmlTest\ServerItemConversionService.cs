using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace FixedXmlTest;

/// <summary>
/// 服务端物品转换服务
/// 负责将客户端物品数据转换为服务端格式并自动分类保存
/// </summary>
public class ServerItemConversionService
{
    private readonly ILogger _logger;
    private readonly XmlService _xmlService;

    public ServerItemConversionService(ILogger logger, XmlService xmlService)
    {
        _logger = logger;
        _xmlService = xmlService;
    }

    /// <summary>
    /// 自动转换并保存客户端物品到服务端格式
    /// </summary>
    public async Task<ConversionResult> ConvertAndSaveToServerAsync(List<ClientItem> clientItems, string serverBasePath)
    {
        try
        {
            _logger.LogInformation("开始转换 {Count} 个客户端物品到服务端格式", clientItems.Count);

            var result = new ConversionResult();

            // 按ID排序
            var sortedItems = clientItems.OrderBy(item => item.Id).ToList();

            // 按类型分类
            var weaponItems = new List<ClientItem>();
            var armorItems = new List<ClientItem>();
            var etcItems = new List<ClientItem>();

            foreach (var item in sortedItems)
            {
                var category = DetermineItemCategory(item);
                switch (category)
                {
                    case ItemCategory.Weapon:
                        weaponItems.Add(item);
                        break;
                    case ItemCategory.Armor:
                        armorItems.Add(item);
                        break;
                    case ItemCategory.Etc:
                        etcItems.Add(item);
                        break;
                }
            }

            // 转换并保存到对应的服务端文件
            if (weaponItems.Count > 0)
            {
                var weaponServerPath = Path.Combine(serverBasePath, "item_weapons.xml");
                await ConvertAndSaveItemCategory(weaponItems, weaponServerPath, "item_weapons", "weapon_templates");
                result.WeaponCount = weaponItems.Count;
                result.WeaponFilePath = weaponServerPath;
            }

            if (armorItems.Count > 0)
            {
                var armorServerPath = Path.Combine(serverBasePath, "item_armors.xml");
                await ConvertAndSaveItemCategory(armorItems, armorServerPath, "item_armors", "armor_templates");
                result.ArmorCount = armorItems.Count;
                result.ArmorFilePath = armorServerPath;
            }

            if (etcItems.Count > 0)
            {
                var etcServerPath = Path.Combine(serverBasePath, "item_etc.xml");
                await ConvertAndSaveItemCategory(etcItems, etcServerPath, "item_etc", "item_templates");
                result.EtcCount = etcItems.Count;
                result.EtcFilePath = etcServerPath;
            }

            result.TotalCount = sortedItems.Count;
            result.Success = true;

            _logger.LogInformation("转换完成: 武器 {WeaponCount}, 防具 {ArmorCount}, 杂货 {EtcCount}", 
                result.WeaponCount, result.ArmorCount, result.EtcCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换客户端物品到服务端格式时出错");
            return new ConversionResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    /// <summary>
    /// 确定物品类别
    /// </summary>
    private ItemCategory DetermineItemCategory(ClientItem item)
    {
        // 根据武器类型判断
        if (!string.IsNullOrEmpty(item.WeaponType))
        {
            return ItemCategory.Weapon;
        }

        // 根据防具类型判断
        if (!string.IsNullOrEmpty(item.ArmorType))
        {
            return ItemCategory.Armor;
        }

        // 根据物品类型判断
        if (!string.IsNullOrEmpty(item.ItemType))
        {
            switch (item.ItemType.ToLower())
            {
                case "weapon":
                    return ItemCategory.Weapon;
                case "armor":
                    return ItemCategory.Armor;
                case "normal":
                case "quest":
                case "cash":
                case "material":
                case "consumable":
                    return ItemCategory.Etc;
            }
        }

        // 根据名称模式判断
        var name = item.Name.ToLower();
        if (name.Contains("sword") || name.Contains("dagger") || name.Contains("bow") || 
            name.Contains("staff") || name.Contains("mace") || name.Contains("gun") ||
            name.Contains("weapon"))
        {
            return ItemCategory.Weapon;
        }

        if (name.Contains("armor") || name.Contains("torso") || name.Contains("leg") || 
            name.Contains("head") || name.Contains("shoulder") || name.Contains("glove") ||
            name.Contains("shoes"))
        {
            return ItemCategory.Armor;
        }

        // 默认为杂货
        return ItemCategory.Etc;
    }

    /// <summary>
    /// 转换并保存特定类别的物品
    /// </summary>
    private async Task ConvertAndSaveItemCategory(List<ClientItem> items, string serverFilePath,
        string rootElementName, string templateElementName)
    {
        try
        {
            _logger.LogInformation("转换 {Count} 个物品到服务端文件: {FilePath}", items.Count, serverFilePath);

            // 确保目录存在
            var directory = Path.GetDirectoryName(serverFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 使用StringBuilder构建XML内容，确保格式正确
            var xmlContent = new System.Text.StringBuilder();
            xmlContent.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xmlContent.AppendLine($"<{rootElementName}>");
            xmlContent.AppendLine($"\t<{templateElementName}>");

            // 转换每个物品
            foreach (var clientItem in items.OrderBy(i => i.Id))
            {
                var itemXml = ConvertClientItemToServerXml(clientItem);
                xmlContent.AppendLine(itemXml);
            }

            xmlContent.AppendLine($"\t</{templateElementName}>");
            xmlContent.AppendLine($"</{rootElementName}>");

            // 保存文件
            await File.WriteAllTextAsync(serverFilePath, xmlContent.ToString(), System.Text.Encoding.UTF8);

            _logger.LogInformation("成功保存服务端文件: {FilePath}", serverFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存服务端文件时出错: {FilePath}", serverFilePath);
            throw;
        }
    }

    /// <summary>
    /// 将客户端物品转换为服务端XML字符串
    /// </summary>
    private string ConvertClientItemToServerXml(ClientItem clientItem)
    {
        var category = DetermineItemCategory(clientItem);

        return category switch
        {
            ItemCategory.Weapon => ConvertToWeaponTemplateXml(clientItem),
            ItemCategory.Armor => ConvertToArmorTemplateXml(clientItem),
            ItemCategory.Etc => ConvertToItemTemplateXml(clientItem),
            _ => ConvertToItemTemplateXml(clientItem)
        };
    }

    /// <summary>
    /// 转换为武器模板XML
    /// </summary>
    private string ConvertToWeaponTemplateXml(ClientItem clientItem)
    {
        var xml = new System.Text.StringBuilder();
        xml.AppendLine("\t\t<weapon_template>");

        // 基础属性
        AddXmlElement(xml, "id", clientItem.Id.ToString());
        AddXmlElement(xml, "name", clientItem.Name);
        AddXmlElement(xml, "desc", clientItem.Desc);
        AddXmlElement(xml, "weapon_type", clientItem.WeaponType);
        AddXmlElement(xml, "item_type", clientItem.ItemType);
        AddXmlElement(xml, "material", clientItem.Material);
        AddXmlElement(xml, "price", clientItem.Price.ToString());
        AddXmlElement(xml, "max_stack_count", clientItem.MaxStackCount.ToString());

        // 武器专用属性
        if (!string.IsNullOrEmpty(clientItem.EquipmentSlots))
            AddXmlElement(xml, "equipment_slots", clientItem.EquipmentSlots);

        // 添加通用属性
        AddCommonServerAttributesXml(xml, clientItem);

        xml.AppendLine("\t\t</weapon_template>");
        return xml.ToString();
    }

    /// <summary>
    /// 转换为防具模板XML
    /// </summary>
    private string ConvertToArmorTemplateXml(ClientItem clientItem)
    {
        var xml = new System.Text.StringBuilder();
        xml.AppendLine("\t\t<armor_template>");

        // 基础属性
        AddXmlElement(xml, "id", clientItem.Id.ToString());
        AddXmlElement(xml, "name", clientItem.Name);
        AddXmlElement(xml, "desc", clientItem.Desc);
        AddXmlElement(xml, "armor_type", clientItem.ArmorType);
        AddXmlElement(xml, "item_type", clientItem.ItemType);
        AddXmlElement(xml, "material", clientItem.Material);
        AddXmlElement(xml, "price", clientItem.Price.ToString());
        AddXmlElement(xml, "max_stack_count", clientItem.MaxStackCount.ToString());

        // 防具专用属性
        if (clientItem.PhysicalDefend > 0)
            AddXmlElement(xml, "physical_defend", clientItem.PhysicalDefend.ToString());
        if (clientItem.MagicalResist > 0)
            AddXmlElement(xml, "magical_resist", clientItem.MagicalResist.ToString());
        if (clientItem.Dodge > 0)
            AddXmlElement(xml, "dodge", clientItem.Dodge.ToString());

        // 添加通用属性
        AddCommonServerAttributesXml(xml, clientItem);

        xml.AppendLine("\t\t</armor_template>");
        return xml.ToString();
    }

    /// <summary>
    /// 转换为物品模板XML
    /// </summary>
    private string ConvertToItemTemplateXml(ClientItem clientItem)
    {
        var xml = new System.Text.StringBuilder();
        xml.AppendLine("\t\t<item_template>");

        // 基础属性
        AddXmlElement(xml, "id", clientItem.Id.ToString());
        AddXmlElement(xml, "name", clientItem.Name);
        AddXmlElement(xml, "desc", clientItem.Desc);
        AddXmlElement(xml, "item_type", clientItem.ItemType);
        AddXmlElement(xml, "material", clientItem.Material);
        AddXmlElement(xml, "price", clientItem.Price.ToString());
        AddXmlElement(xml, "max_stack_count", clientItem.MaxStackCount.ToString());

        // 添加通用属性
        AddCommonServerAttributesXml(xml, clientItem);

        xml.AppendLine("\t\t</item_template>");
        return xml.ToString();
    }

    /// <summary>
    /// 添加通用服务端属性XML
    /// </summary>
    private void AddCommonServerAttributesXml(System.Text.StringBuilder xml, ClientItem clientItem)
    {
        // 品质和等级
        AddXmlElement(xml, "quality", clientItem.Quality);
        AddXmlElement(xml, "level", clientItem.Level.ToString());
        AddXmlElement(xml, "lore", clientItem.Lore ? "TRUE" : "FALSE");

        // 交易权限
        AddXmlElement(xml, "can_exchange", clientItem.CanExchange ? "TRUE" : "FALSE");
        AddXmlElement(xml, "can_sell_to_npc", clientItem.CanSellToNpc ? "TRUE" : "FALSE");
        AddXmlElement(xml, "can_deposit_to_character_warehouse", clientItem.CanDepositToCharacterWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xml, "can_deposit_to_account_warehouse", clientItem.CanDepositToAccountWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xml, "can_deposit_to_guild_warehouse", clientItem.CanDepositToGuildWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xml, "item_drop_permitted", clientItem.ItemDropPermitted ? "TRUE" : "FALSE");

        // 绑定属性
        AddXmlElement(xml, "breakable", clientItem.Breakable ? "TRUE" : "FALSE");
        AddXmlElement(xml, "soul_bind", clientItem.SoulBind ? "TRUE" : "FALSE");
        AddXmlElement(xml, "remove_when_logout", clientItem.RemoveWhenLogout ? "TRUE" : "FALSE");

        // 种族和性别限制
        if (!string.IsNullOrEmpty(clientItem.RacePermitted))
            AddXmlElement(xml, "race_permitted", clientItem.RacePermitted);
        if (!string.IsNullOrEmpty(clientItem.GenderPermitted))
            AddXmlElement(xml, "gender_permitted", clientItem.GenderPermitted);

        // 职业限制
        AddClassRestrictionsXml(xml, clientItem);

        // 属性加成
        AddBonusAttributesXml(xml, clientItem);

        // 交易相关
        if (!string.IsNullOrEmpty(clientItem.ExtraCurrencyItem))
        {
            AddXmlElement(xml, "extra_currency_item", clientItem.ExtraCurrencyItem);
            AddXmlElement(xml, "extra_currency_item_count", clientItem.ExtraCurrencyItemCount.ToString());
        }

        // 兑换物品列表
        if (clientItem.TradeInItemList != null && clientItem.TradeInItemList.Count > 0)
        {
            xml.AppendLine("\t\t\t<trade_in_item_list>");
            foreach (var tradeInItem in clientItem.TradeInItemList)
            {
                xml.AppendLine("\t\t\t\t<data>");
                AddXmlElement(xml, "trade_in_item", tradeInItem.TradeInItem, "\t\t\t\t\t");
                AddXmlElement(xml, "trade_in_item_count", tradeInItem.TradeInItemCount.ToString(), "\t\t\t\t\t");
                xml.AppendLine("\t\t\t\t</data>");
            }
            xml.AppendLine("\t\t\t</trade_in_item_list>");
        }

        // 其他特殊属性
        if (!string.IsNullOrEmpty(clientItem.ActivationSkill))
            AddXmlElement(xml, "activation_skill", clientItem.ActivationSkill);
        if (!string.IsNullOrEmpty(clientItem.ActivationMode))
            AddXmlElement(xml, "activation_mode", clientItem.ActivationMode);
        if (clientItem.ExtraInventory > 0)
            AddXmlElement(xml, "extra_inventory", clientItem.ExtraInventory.ToString());
        if (clientItem.DisassemblyItem > 0)
            AddXmlElement(xml, "disassembly_item", clientItem.DisassemblyItem.ToString());
    }

    /// <summary>
    /// 添加职业限制XML
    /// </summary>
    private void AddClassRestrictionsXml(System.Text.StringBuilder xml, ClientItem clientItem)
    {
        if (clientItem.Warrior > 0) AddXmlElement(xml, "warrior", clientItem.Warrior.ToString());
        if (clientItem.Scout > 0) AddXmlElement(xml, "scout", clientItem.Scout.ToString());
        if (clientItem.Mage > 0) AddXmlElement(xml, "mage", clientItem.Mage.ToString());
        if (clientItem.Cleric > 0) AddXmlElement(xml, "cleric", clientItem.Cleric.ToString());
        if (clientItem.Engineer > 0) AddXmlElement(xml, "engineer", clientItem.Engineer.ToString());
        if (clientItem.Artist > 0) AddXmlElement(xml, "artist", clientItem.Artist.ToString());
        if (clientItem.Fighter > 0) AddXmlElement(xml, "fighter", clientItem.Fighter.ToString());
        if (clientItem.Knight > 0) AddXmlElement(xml, "knight", clientItem.Knight.ToString());
        if (clientItem.Assassin > 0) AddXmlElement(xml, "assassin", clientItem.Assassin.ToString());
        if (clientItem.Ranger > 0) AddXmlElement(xml, "ranger", clientItem.Ranger.ToString());
        if (clientItem.Wizard > 0) AddXmlElement(xml, "wizard", clientItem.Wizard.ToString());
        if (clientItem.Elementalist > 0) AddXmlElement(xml, "elementalist", clientItem.Elementalist.ToString());
        if (clientItem.Chanter > 0) AddXmlElement(xml, "chanter", clientItem.Chanter.ToString());
        if (clientItem.Priest > 0) AddXmlElement(xml, "priest", clientItem.Priest.ToString());
        if (clientItem.Gunner > 0) AddXmlElement(xml, "gunner", clientItem.Gunner.ToString());
        if (clientItem.Bard > 0) AddXmlElement(xml, "bard", clientItem.Bard.ToString());
        if (clientItem.Rider > 0) AddXmlElement(xml, "rider", clientItem.Rider.ToString());
    }

    /// <summary>
    /// 添加属性加成XML
    /// </summary>
    private void AddBonusAttributesXml(System.Text.StringBuilder xml, ClientItem clientItem)
    {
        if (!string.IsNullOrEmpty(clientItem.BonusAttr1))
            AddXmlElement(xml, "bonus_attr1", clientItem.BonusAttr1);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr2))
            AddXmlElement(xml, "bonus_attr2", clientItem.BonusAttr2);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr3))
            AddXmlElement(xml, "bonus_attr3", clientItem.BonusAttr3);
        if (!string.IsNullOrEmpty(clientItem.BonusAttr4))
            AddXmlElement(xml, "bonus_attr4", clientItem.BonusAttr4);
        if (!string.IsNullOrEmpty(clientItem.BonusApply))
            AddXmlElement(xml, "bonus_apply", clientItem.BonusApply);
    }

    /// <summary>
    /// 添加XML元素
    /// </summary>
    private void AddXmlElement(System.Text.StringBuilder xml, string elementName, string value, string indent = "\t\t\t")
    {
        if (!string.IsNullOrEmpty(value))
        {
            xml.AppendLine($"{indent}<{elementName}>{System.Security.SecurityElement.Escape(value)}</{elementName}>");
        }
    }
}

/// <summary>
/// 物品类别枚举
/// </summary>
public enum ItemCategory
{
    Weapon,
    Armor,
    Etc
}

/// <summary>
/// 转换结果
/// </summary>
public class ConversionResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public int TotalCount { get; set; }
    public int WeaponCount { get; set; }
    public int ArmorCount { get; set; }
    public int EtcCount { get; set; }
    public string WeaponFilePath { get; set; } = string.Empty;
    public string ArmorFilePath { get; set; } = string.Empty;
    public string EtcFilePath { get; set; } = string.Empty;
}
