using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor;

/// <summary>
/// NPC商店XML文件服务
/// </summary>
public class NpcGoodsListXmlService
{
    private readonly ILogger<NpcGoodsListXmlService> _logger;

    public NpcGoodsListXmlService(ILogger<NpcGoodsListXmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加载NPC商店文件
    /// </summary>
    public async Task<List<ClientNpcGoodsList>> LoadNpcGoodsListsAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载NPC商店文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"NPC商店文件不存在: {filePath}");
            }

            var goodsLists = new List<ClientNpcGoodsList>();
            var xmlDoc = new XmlDocument();
            
            // 读取XML文件
            var xmlContent = await File.ReadAllTextAsync(filePath);
            xmlDoc.LoadXml(xmlContent);

            var goodsListNodes = xmlDoc.SelectNodes("//client_npc_goodslist");
            if (goodsListNodes == null)
            {
                _logger.LogWarning("未找到NPC商店节点");
                return goodsLists;
            }

            foreach (XmlNode goodsListNode in goodsListNodes)
            {
                try
                {
                    var goodsList = ParseClientNpcGoodsList(goodsListNode);
                    goodsLists.Add(goodsList);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析NPC商店时出错: {GoodsListNode}", goodsListNode?.OuterXml);
                }
            }

            _logger.LogInformation("成功加载 {Count} 个NPC商店", goodsLists.Count);
            return goodsLists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载NPC商店文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 解析NPC商店节点
    /// </summary>
    private ClientNpcGoodsList ParseClientNpcGoodsList(XmlNode goodsListNode)
    {
        var goodsList = new ClientNpcGoodsList
        {
            Id = GetIntValue(goodsListNode, "id", 0),
            Name = GetStringValue(goodsListNode, "name"),
            Desc = GetStringValue(goodsListNode, "desc")
        };

        // 解析商品列表
        var goodsListXmlNode = goodsListNode.SelectSingleNode("goods_list");
        if (goodsListXmlNode != null)
        {
            var dataNodes = goodsListXmlNode.SelectNodes("data");
            if (dataNodes != null)
            {
                foreach (XmlNode dataNode in dataNodes)
                {
                    var itemNode = dataNode.SelectSingleNode("item");
                    if (itemNode != null && !string.IsNullOrEmpty(itemNode.InnerText))
                    {
                        goodsList.GoodsList.Add(new GoodsData
                        {
                            Item = itemNode.InnerText.Trim()
                        });
                    }
                }
            }
        }

        return goodsList;
    }

    /// <summary>
    /// 获取字符串值
    /// </summary>
    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText?.Trim() ?? defaultValue;
    }

    /// <summary>
    /// 获取整数值
    /// </summary>
    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText?.Trim(), out var value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 保存NPC商店到XML文件
    /// </summary>
    public async Task SaveNpcGoodsListsAsync(string filePath, List<ClientNpcGoodsList> goodsLists)
    {
        try
        {
            _logger.LogInformation("开始保存NPC商店文件: {FilePath}", filePath);

            var xmlDoc = new XmlDocument();
            
            // 创建根节点
            var rootElement = xmlDoc.CreateElement("client_npc_goodslists");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有商店
            foreach (var goodsList in goodsLists)
            {
                var goodsListElement = CreateGoodsListXmlElement(xmlDoc, goodsList);
                rootElement.AppendChild(goodsListElement);
            }

            // 保存到文件
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = System.Text.Encoding.Unicode // 使用UTF-16编码，与原文件一致
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存 {Count} 个NPC商店到文件", goodsLists.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存NPC商店文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 创建商店的XML元素
    /// </summary>
    private XmlElement CreateGoodsListXmlElement(XmlDocument xmlDoc, ClientNpcGoodsList goodsList)
    {
        var goodsListElement = xmlDoc.CreateElement("client_npc_goodslist");

        // 添加基础属性
        AddXmlElement(xmlDoc, goodsListElement, "id", goodsList.Id.ToString());
        AddXmlElement(xmlDoc, goodsListElement, "name", goodsList.Name);
        AddXmlElement(xmlDoc, goodsListElement, "desc", goodsList.Desc);

        // 添加商品列表
        if (goodsList.GoodsList != null && goodsList.GoodsList.Count > 0)
        {
            var goodsListXmlElement = xmlDoc.CreateElement("goods_list");
            
            foreach (var goods in goodsList.GoodsList)
            {
                if (!string.IsNullOrEmpty(goods.Item))
                {
                    var dataElement = xmlDoc.CreateElement("data");
                    AddXmlElement(xmlDoc, dataElement, "item", goods.Item);
                    goodsListXmlElement.AppendChild(dataElement);
                }
            }
            
            goodsListElement.AppendChild(goodsListXmlElement);
        }

        return goodsListElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AddXmlElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value ?? string.Empty;
        parentElement.AppendChild(element);
    }

    /// <summary>
    /// 获取NPC商店统计信息
    /// </summary>
    public NpcGoodsListStatistics GetStatistics(List<ClientNpcGoodsList> goodsLists)
    {
        var stats = new NpcGoodsListStatistics
        {
            TotalShops = goodsLists.Count
        };

        // 统计商店类型
        foreach (var shop in goodsLists)
        {
            var shopType = shop.ShopType;
            if (stats.ShopTypes.ContainsKey(shopType))
                stats.ShopTypes[shopType]++;
            else
                stats.ShopTypes[shopType] = 1;

            var professionType = shop.ProfessionType;
            if (stats.ProfessionTypes.ContainsKey(professionType))
                stats.ProfessionTypes[professionType]++;
            else
                stats.ProfessionTypes[professionType] = 1;

            var levelType = shop.LevelType;
            if (stats.LevelTypes.ContainsKey(levelType))
                stats.LevelTypes[levelType]++;
            else
                stats.LevelTypes[levelType] = 1;
        }

        // 统计商品信息
        var allItemCounts = goodsLists.Select(s => s.ItemCount).ToList();
        stats.TotalItems = allItemCounts.Sum();
        stats.AverageItemsPerShop = allItemCounts.Count > 0 ? allItemCounts.Average() : 0;
        stats.MaxItemsInShop = allItemCounts.Count > 0 ? allItemCounts.Max() : 0;
        stats.MinItemsInShop = allItemCounts.Count > 0 ? allItemCounts.Min() : 0;

        // 统计物品类型
        foreach (var shop in goodsLists)
        {
            var itemTypeStats = shop.GetItemTypeStatistics();
            foreach (var itemType in itemTypeStats)
            {
                if (stats.ItemTypes.ContainsKey(itemType.Key))
                    stats.ItemTypes[itemType.Key] += itemType.Value;
                else
                    stats.ItemTypes[itemType.Key] = itemType.Value;
            }
        }

        return stats;
    }

    /// <summary>
    /// 筛选商店
    /// </summary>
    public List<ClientNpcGoodsList> FilterShops(List<ClientNpcGoodsList> goodsLists, ShopFilterCriteria criteria)
    {
        var filtered = goodsLists.AsEnumerable();

        if (!string.IsNullOrEmpty(criteria.SearchText))
        {
            var searchText = criteria.SearchText.ToLower();
            filtered = filtered.Where(s => 
                s.Id.ToString().Contains(searchText) ||
                s.Name.ToLower().Contains(searchText) ||
                s.Desc.ToLower().Contains(searchText) ||
                s.DisplayName.ToLower().Contains(searchText));
        }

        if (!string.IsNullOrEmpty(criteria.ShopType))
        {
            filtered = filtered.Where(s => s.ShopType == criteria.ShopType);
        }

        if (!string.IsNullOrEmpty(criteria.ProfessionType))
        {
            filtered = filtered.Where(s => s.ProfessionType == criteria.ProfessionType);
        }

        if (!string.IsNullOrEmpty(criteria.LevelType))
        {
            filtered = filtered.Where(s => s.LevelType == criteria.LevelType);
        }

        if (criteria.MinItems.HasValue)
        {
            filtered = filtered.Where(s => s.ItemCount >= criteria.MinItems.Value);
        }

        if (criteria.MaxItems.HasValue)
        {
            filtered = filtered.Where(s => s.ItemCount <= criteria.MaxItems.Value);
        }

        return filtered.ToList();
    }

    /// <summary>
    /// 保存商店数据并转换为服务端格式
    /// </summary>
    public async Task<bool> SaveWithServerConversionAsync(string clientFilePath, List<ClientNpcGoodsList> goodsLists, string serverBasePath)
    {
        try
        {
            _logger.LogInformation($"开始转换 {goodsLists.Count} 个商店到服务端格式");

            // 确保服务端目录存在
            if (!Directory.Exists(serverBasePath))
            {
                Directory.CreateDirectory(serverBasePath);
            }

            // 创建服务端XML文档
            var xmlDoc = new XmlDocument();
            xmlDoc.AppendChild(xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null));

            // 使用服务端根标签
            var rootElement = xmlDoc.CreateElement("npc_goods");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有商店（按ID排序）
            var sortedGoodsLists = goodsLists.OrderBy(g => g.Id).ToList();
            foreach (var goodsList in sortedGoodsLists)
            {
                var goodsListElement = CreateGoodsListXmlElement(xmlDoc, goodsList);
                rootElement.AppendChild(goodsListElement);
            }

            // 保存服务端文件
            var serverFilePath = Path.Combine(serverBasePath, "npc_goods.xml");
            xmlDoc.Save(serverFilePath);

            _logger.LogInformation($"服务端商店文件已保存: {serverFilePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换商店到服务端格式时出错");
            return false;
        }
    }
}
