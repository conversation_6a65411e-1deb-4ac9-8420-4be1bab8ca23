using Microsoft.Extensions.Logging;
using LinkTest;

Console.WriteLine("=================================");
Console.WriteLine("    智能关联服务测试程序");
Console.WriteLine("=================================");
Console.WriteLine();

// 创建日志记录器
using var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));

var stringLogger = loggerFactory.CreateLogger<StringService>();
var xmlLogger = loggerFactory.CreateLogger<XmlService>();
var linkLogger = loggerFactory.CreateLogger<ItemStringLinkService>();

// 创建服务
var stringService = new StringService(stringLogger);
var xmlService = new XmlService(xmlLogger);
var linkService = new ItemStringLinkService(stringService, xmlService, linkLogger);

try
{
    Console.WriteLine("✓ 第一步：加载字符串文件...");
    await stringService.LoadStringsAsync();
    
    var stringStats = stringService.GetStatistics();
    Console.WriteLine($"  字符串加载完成 - 总计: {stringStats.TotalStrings} 个");
    Console.WriteLine();

    Console.WriteLine("✓ 第二步：加载所有物品文件并建立智能关联...");
    await linkService.LoadAllItemsAndBuildLinksAsync();
    
    var linkStats = linkService.GetStatistics();
    Console.WriteLine($"  关联建立完成:");
    Console.WriteLine($"  - 总物品数量: {linkStats.TotalItems}");
    Console.WriteLine($"  - 成功关联: {linkStats.LinkedItems}");
    Console.WriteLine($"  - 未关联: {linkStats.UnlinkedItems}");
    Console.WriteLine($"  - 关联成功率: {(double)linkStats.LinkedItems / linkStats.TotalItems * 100:F1}%");
    Console.WriteLine();

    Console.WriteLine("✓ 第三步：测试智能关联功能...");
    
    // 测试通过ID查找本地化名称
    var testIds = new[] { 110000001, 110000002, 110000003, 100001001, 100002001 };
    
    Console.WriteLine("  通过物品ID查找本地化名称:");
    foreach (var id in testIds)
    {
        var localizedName = linkService.GetLocalizedNameById(id);
        var descKey = linkService.GetDescKeyById(id);
        var item = linkService.GetItemById(id);
        
        if (item != null)
        {
            Console.WriteLine($"    ID {id}:");
            Console.WriteLine($"      内部名称: {item.Name}");
            Console.WriteLine($"      描述键: {descKey ?? "无"}");
            Console.WriteLine($"      本地化名称: {localizedName ?? "未找到"}");
            Console.WriteLine();
        }
        else
        {
            Console.WriteLine($"    ID {id}: 物品不存在");
        }
    }

    Console.WriteLine("✓ 第四步：测试搜索功能...");
    
    // 测试各种搜索方式
    var searchTests = new[]
    {
        ("110000001", "ID搜索"),
        ("npc_cl_torso", "内部名称搜索"),
        ("衣裳", "本地化名称搜索"),
        ("武器", "本地化名称搜索"),
        ("护甲", "本地化名称搜索")
    };

    foreach (var (searchTerm, searchType) in searchTests)
    {
        Console.WriteLine($"  {searchType} - 搜索 '{searchTerm}':");
        var results = linkService.SearchItems(searchTerm, 5);
        
        if (results.Count > 0)
        {
            Console.WriteLine($"    找到 {results.Count} 个结果:");
            foreach (var result in results.Take(3))
            {
                Console.WriteLine($"      [{result.Item.Id}] {result.Item.Name} -> {result.LocalizedName} ({result.MatchType}匹配)");
            }
            if (results.Count > 3)
            {
                Console.WriteLine($"      ... 还有 {results.Count - 3} 个结果");
            }
        }
        else
        {
            Console.WriteLine($"    未找到匹配结果");
        }
        Console.WriteLine();
    }

    Console.WriteLine("✓ 第五步：分析未关联的物品...");
    var unlinkedItems = linkService.GetUnlinkedItems();
    Console.WriteLine($"  未关联物品数量: {unlinkedItems.Count}");
    
    if (unlinkedItems.Count > 0)
    {
        Console.WriteLine("  前10个未关联物品:");
        foreach (var item in unlinkedItems.Take(10))
        {
            Console.WriteLine($"    [{item.Id}] {item.Name} - 描述键: {item.Desc}");
        }
        
        if (unlinkedItems.Count > 10)
        {
            Console.WriteLine($"    ... 还有 {unlinkedItems.Count - 10} 个未关联物品");
        }
    }
    Console.WriteLine();

    Console.WriteLine("=================================");
    Console.WriteLine("✓ 智能关联服务测试完成！");
    Console.WriteLine("=================================");
    Console.WriteLine();
    Console.WriteLine("功能验证:");
    Console.WriteLine("  ✓ 自动加载所有客户端XML文件");
    Console.WriteLine("  ✓ 智能建立物品与字符串的关联");
    Console.WriteLine("  ✓ 通过ID直接获取本地化名称");
    Console.WriteLine("  ✓ 通过内部名称获取本地化名称");
    Console.WriteLine("  ✓ 多维度搜索功能");
    Console.WriteLine("  ✓ 高效的数据索引和查询");
    Console.WriteLine();
    Console.WriteLine("现在用户可以:");
    Console.WriteLine("  • 点击任意物品ID立即看到中文名称");
    Console.WriteLine("  • 通过中文名称搜索物品");
    Console.WriteLine("  • 自动关联所有XML文件的物品");
    Console.WriteLine("  • 实时验证字符串关联状态");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 测试过程中出现错误: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

Console.WriteLine();
Console.WriteLine("按任意键退出...");
Console.ReadKey();
