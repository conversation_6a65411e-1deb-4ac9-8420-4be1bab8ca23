using System.Collections.Concurrent;
using System.IO;
using System.Text;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace TradeModeTest;

/// <summary>
/// 字符串本地化服务
/// 用于加载和管理strings文件中的本地化文本
/// </summary>
public class StringService
{
    private readonly ILogger<StringService> _logger;
    private readonly ConcurrentDictionary<string, string> _strings;
    private readonly ConcurrentDictionary<int, string> _stringsByIndex;

    public StringService(ILogger<StringService> logger)
    {
        _logger = logger;
        _strings = new ConcurrentDictionary<string, string>();
        _stringsByIndex = new ConcurrentDictionary<int, string>();
    }

    /// <summary>
    /// 加载strings目录中的所有字符串文件
    /// </summary>
    public async Task LoadStringsAsync(string stringsDirectory = "strings")
    {
        try
        {
            if (!Directory.Exists(stringsDirectory))
            {
                _logger.LogWarning("Strings目录不存在: {Directory}", stringsDirectory);
                return;
            }

            var stringFiles = Directory.GetFiles(stringsDirectory, "client_strings_*.xml");
            _logger.LogInformation("找到 {Count} 个字符串文件", stringFiles.Length);

            foreach (var file in stringFiles)
            {
                await LoadStringFileAsync(file);
            }

            _logger.LogInformation("成功加载 {Count} 个字符串", _strings.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载字符串文件时出错");
        }
    }

    /// <summary>
    /// 加载单个字符串文件
    /// </summary>
    private async Task LoadStringFileAsync(string filePath)
    {
        try
        {
            _logger.LogDebug("加载字符串文件: {FilePath}", filePath);

            // 读取UTF-16编码的XML文件
            var content = await File.ReadAllTextAsync(filePath, Encoding.Unicode);
            
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);

            var stringNodes = xmlDoc.SelectNodes("//string");
            if (stringNodes == null) return;

            var count = 0;
            foreach (XmlNode stringNode in stringNodes)
            {
                var idNode = stringNode.SelectSingleNode("id");
                var nameNode = stringNode.SelectSingleNode("name");
                var bodyNode = stringNode.SelectSingleNode("body");

                if (idNode != null && nameNode != null && bodyNode != null)
                {
                    var id = idNode.InnerText;
                    var name = nameNode.InnerText;
                    var body = bodyNode.InnerText;

                    // 按名称存储
                    _strings.TryAdd(name, body);
                    
                    // 按ID存储
                    if (int.TryParse(id, out var numericId))
                    {
                        _stringsByIndex.TryAdd(numericId, body);
                    }

                    count++;
                }
            }

            _logger.LogDebug("从 {FilePath} 加载了 {Count} 个字符串", Path.GetFileName(filePath), count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载字符串文件时出错: {FilePath}", filePath);
        }
    }

    /// <summary>
    /// 根据字符串键获取本地化文本
    /// </summary>
    public string GetString(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            return string.Empty;

        return _strings.TryGetValue(key, out var value) ? value : key;
    }

    /// <summary>
    /// 根据字符串ID获取本地化文本
    /// </summary>
    public string GetString(int id)
    {
        return _stringsByIndex.TryGetValue(id, out var value) ? value : id.ToString();
    }

    /// <summary>
    /// 检查是否包含指定的字符串键
    /// </summary>
    public bool ContainsKey(string key)
    {
        return _strings.ContainsKey(key);
    }

    /// <summary>
    /// 获取所有字符串键
    /// </summary>
    public IEnumerable<string> GetAllKeys()
    {
        return _strings.Keys;
    }

    /// <summary>
    /// 搜索包含指定文本的字符串
    /// </summary>
    public Dictionary<string, string> SearchStrings(string searchText, int maxResults = 100)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return new Dictionary<string, string>();

        var results = new Dictionary<string, string>();
        var searchLower = searchText.ToLowerInvariant();

        foreach (var kvp in _strings)
        {
            if (results.Count >= maxResults)
                break;

            if (kvp.Key.ToLowerInvariant().Contains(searchLower) ||
                kvp.Value.ToLowerInvariant().Contains(searchLower))
            {
                results[kvp.Key] = kvp.Value;
            }
        }

        return results;
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public (int TotalStrings, int StringsByName, int StringsById) GetStatistics()
    {
        return (_strings.Count + _stringsByIndex.Count, _strings.Count, _stringsByIndex.Count);
    }

    /// <summary>
    /// 清空所有字符串
    /// </summary>
    public void Clear()
    {
        _strings.Clear();
        _stringsByIndex.Clear();
    }
}
