<Window x:Class="AionItemEditor.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:AionItemEditor.ViewModels"
        xmlns:views="clr-namespace:AionItemEditor.Views"
        Title="Aion物品编辑器" 
        Height="800" 
        Width="1400"
        MinHeight="600"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewModels:MainWindowViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧按钮组 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="{StaticResource PrimaryBrush}"
                            Command="{Binding OpenFileCommand}"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderOpen" Margin="0,0,8,0"/>
                            <TextBlock Text="打开文件"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="{StaticResource PrimaryBrush}"
                            Command="{Binding SaveFileCommand}"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                            <TextBlock Text="保存文件"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="{StaticResource PrimaryBrush}"
                            Command="{Binding AddItemCommand}"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                            <TextBlock Text="新增物品"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- 中间标题 -->
                <TextBlock Grid.Column="1" 
                          Text="{Binding CurrentFileName, StringFormat='当前文件: {0}'}"
                          Foreground="White"
                          FontSize="16"
                          FontWeight="Medium"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>

                <!-- 右侧状态 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="{Binding ItemCount, StringFormat='物品数量: {0}'}"
                              Foreground="White"
                              FontSize="14"
                              VerticalAlignment="Center"
                              Margin="0,0,16,0"/>
                    
                    <materialDesign:PackIcon Kind="CheckCircle" 
                                           Foreground="{StaticResource SuccessBrush}"
                                           Visibility="{Binding IsFileSaved, Converter={StaticResource BooleanToVisibilityConverter}}"
                                           VerticalAlignment="Center"/>
                    
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Foreground="{StaticResource WarningBrush}"
                                           Visibility="{Binding IsFileModified, Converter={StaticResource BooleanToVisibilityConverter}}"
                                           VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400" MinWidth="300"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*" MinWidth="400"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧物品列表 -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 搜索栏 -->
                    <TextBlock Grid.Row="0" Text="物品列表" Style="{StaticResource GroupHeaderStyle}"/>
                    
                    <TextBox Grid.Row="1" 
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            materialDesign:HintAssist.Hint="搜索物品名称或ID..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,8">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- 物品列表 -->
                    <DataGrid Grid.Row="2"
                             ItemsSource="{Binding FilteredItems}"
                             SelectedItem="{Binding SelectedItem}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="False"
                             CanUserResizeRows="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             Style="{StaticResource MaterialDesignDataGrid}">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" 
                                              Binding="{Binding Id}" 
                                              Width="80"
                                              IsReadOnly="True"/>
                            <DataGridTextColumn Header="名称" 
                                              Binding="{Binding Name}" 
                                              Width="*"
                                              IsReadOnly="True"/>
                            <DataGridTextColumn Header="类型" 
                                              Binding="{Binding ArmorType}" 
                                              Width="100"
                                              IsReadOnly="True"/>
                        </DataGrid.Columns>
                        
                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="编辑" Command="{Binding EditItemCommand}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Edit"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="复制" Command="{Binding CopyItemCommand}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ContentCopy"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="删除" Command="{Binding DeleteItemCommand}">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" 
                         Width="8" 
                         HorizontalAlignment="Center" 
                         VerticalAlignment="Stretch"
                         Background="Transparent"
                         ShowsPreview="True"/>

            <!-- 右侧编辑区域 -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 编辑区域标题 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <TextBlock Text="物品编辑" Style="{StaticResource GroupHeaderStyle}" Margin="0"/>
                        <TextBlock Text="{Binding SelectedItem.DisplayName, StringFormat=' - {0}'}"
                                  Style="{StaticResource GroupHeaderStyle}" 
                                  Margin="0"
                                  Foreground="{StaticResource AccentBrush}"/>
                    </StackPanel>

                    <!-- 编辑内容区域 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <views:ItemEditView DataContext="{Binding ItemEditViewModel}"/>
                    </ScrollViewer>

                    <!-- 底部按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                        <Button Style="{StaticResource SecondaryButtonStyle}"
                               Command="{Binding ResetItemCommand}"
                               Content="重置"/>
                        <Button Style="{StaticResource PrimaryButtonStyle}"
                               Command="{Binding SaveItemCommand}"
                               Content="保存更改"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" Padding="16,8" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                          Text="{Binding StatusMessage}"
                          VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding LastSavedTime, StringFormat='最后保存: {0:yyyy-MM-dd HH:mm:ss}'}"
                              VerticalAlignment="Center"
                              Margin="0,0,16,0"/>
                    <materialDesign:PackIcon Kind="Information" 
                                           VerticalAlignment="Center"
                                           ToolTip="点击查看帮助"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
