namespace ModifiedOnlyTest;

/// <summary>
/// 简化的客户端物品数据模型
/// </summary>
public class ClientItem
{
    public int Id { get; set; } = 0;
    public string Name { get; set; } = string.Empty;
    public string Desc { get; set; } = string.Empty;
    public string WeaponType { get; set; } = string.Empty;
    public string ItemType { get; set; } = string.Empty;
    public int Price { get; set; } = 0;
    public string ExtraCurrencyItem { get; set; } = string.Empty;
    public int ExtraCurrencyItemCount { get; set; } = 0;
    
    /// <summary>
    /// 兑换物品列表
    /// </summary>
    public List<TradeInItemData> TradeInItemList { get; set; } = new List<TradeInItemData>();

    /// <summary>
    /// 标记物品是否被修改过
    /// </summary>
    public bool IsModified { get; set; } = false;
}

/// <summary>
/// 兑换物品数据
/// </summary>
public class TradeInItemData
{
    public string TradeInItem { get; set; } = string.Empty;
    public int TradeInItemCount { get; set; } = 1;
}
