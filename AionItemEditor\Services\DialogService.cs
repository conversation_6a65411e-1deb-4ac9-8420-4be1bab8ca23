using System.Windows;
using Microsoft.Win32;

namespace AionItemEditor.Services;

/// <summary>
/// 对话框服务实现类
/// </summary>
public class DialogService : IDialogService
{
    /// <summary>
    /// 显示信息对话框
    /// </summary>
    public async Task ShowInfoAsync(string message, string title = "信息")
    {
        await Task.Run(() =>
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            });
        });
    }

    /// <summary>
    /// 显示警告对话框
    /// </summary>
    public async Task ShowWarningAsync(string message, string title = "警告")
    {
        await Task.Run(() =>
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            });
        });
    }

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    public async Task ShowErrorAsync(string message, string title = "错误")
    {
        await Task.Run(() =>
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            });
        });
    }

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    public async Task<bool> ShowConfirmAsync(string message, string title = "确认")
    {
        return await Task.Run(() =>
        {
            return System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
                return result == MessageBoxResult.Yes;
            });
        });
    }

    /// <summary>
    /// 显示文件选择对话框
    /// </summary>
    public async Task<string?> ShowOpenFileDialogAsync(string filter = "XML文件|*.xml|所有文件|*.*", string title = "选择文件")
    {
        return await Task.Run(() =>
        {
            return System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                var dialog = new OpenFileDialog
                {
                    Title = title,
                    Filter = filter,
                    FilterIndex = 1,
                    RestoreDirectory = true
                };

                return dialog.ShowDialog() == true ? dialog.FileName : null;
            });
        });
    }

    /// <summary>
    /// 显示文件保存对话框
    /// </summary>
    public async Task<string?> ShowSaveFileDialogAsync(string filter = "XML文件|*.xml|所有文件|*.*", string title = "保存文件", string defaultFileName = "")
    {
        return await Task.Run(() =>
        {
            return System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                var dialog = new SaveFileDialog
                {
                    Title = title,
                    Filter = filter,
                    FilterIndex = 1,
                    RestoreDirectory = true,
                    FileName = defaultFileName
                };

                return dialog.ShowDialog() == true ? dialog.FileName : null;
            });
        });
    }

    /// <summary>
    /// 显示文件夹选择对话框
    /// </summary>
    public async Task<string?> ShowFolderDialogAsync(string title = "选择文件夹")
    {
        return await Task.Run(() =>
        {
            return System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 简化实现，使用OpenFileDialog选择文件夹
                var dialog = new OpenFileDialog
                {
                    Title = title,
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "选择文件夹"
                };

                return dialog.ShowDialog() == true ? System.IO.Path.GetDirectoryName(dialog.FileName) : null;
            });
        });
    }

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    public async Task<string?> ShowInputDialogAsync(string message, string title = "输入", string defaultValue = "")
    {
        return await Task.Run(() =>
        {
            return System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 简化实现，返回默认值
                // 在实际应用中可以创建自定义输入对话框
                return defaultValue;
            });
        });
    }
}
