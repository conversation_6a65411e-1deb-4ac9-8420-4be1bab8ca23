using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace SimpleWpfEditor;

public partial class ItemEditorWindow : Window
{
    private readonly ClientItem _originalItem;
    private readonly StringService _stringService;
    private ClientItem _editingItem;

    public bool DataChanged { get; private set; } = false;
    public ClientItem EditedItem => _editingItem;

    public ItemEditorWindow(ClientItem item, StringService stringService)
    {
        InitializeComponent();
        
        _originalItem = item;
        _stringService = stringService;
        _editingItem = CloneItem(item);
        
        InitializeEditor();
    }

    private void InitializeEditor()
    {
        // 更新标题信息
        TitleText.Text = $"编辑物品 - {_editingItem.Name}";
        SubtitleText.Text = $"ID: {_editingItem.Id}";
        ItemIdText.Text = $"ID: {_editingItem.Id}";
        ItemTypeText.Text = $"类型: {_editingItem.ArmorType}";
        
        // 加载数据到控件
        LoadItemData();
        
        // 更新本地化文本
        UpdateLocalizedText();
    }

    private void LoadItemData()
    {
        // 基础信息
        IdTextBox.Text = _editingItem.Id.ToString();
        NameTextBox.Text = _editingItem.Name;
        DescTextBox.Text = _editingItem.Desc;
        
        // 物品类型
        SetComboBoxValue(ArmorTypeComboBox, _editingItem.ArmorType);
        SetComboBoxValue(ItemTypeComboBox, _editingItem.ItemType);
        SetComboBoxValue(QualityComboBox, _editingItem.Quality);
        SetComboBoxValue(EquipmentSlotsComboBox, _editingItem.EquipmentSlots);
        
        // 数值属性
        PriceTextBox.Text = _editingItem.Price.ToString();
        LevelTextBox.Text = _editingItem.Level.ToString();
        MaxStackCountTextBox.Text = _editingItem.MaxStackCount.ToString();
        DodgeTextBox.Text = _editingItem.Dodge.ToString();
        PhysicalDefendTextBox.Text = _editingItem.PhysicalDefend.ToString();
        MagicalResistTextBox.Text = _editingItem.MagicalResist.ToString();

        // 新增的数值属性
        EquipTypeTextBox.Text = _editingItem.EquipType.ToString();
        ExpireTimeTextBox.Text = _editingItem.ExpireTime.ToString();
        UsableRankMinTextBox.Text = _editingItem.UsableRankMin.ToString();
        PurchableRankMinTextBox.Text = _editingItem.PurchableRankMin.ToString();
        ScaleTextBox.Text = _editingItem.Scale.ToString("F6");
        ExtractSkinTypeTextBox.Text = _editingItem.ExtractSkinType.ToString();
        
        // 职业权限
        WarriorCheckBox.IsChecked = _editingItem.Warrior == 1;
        ScoutCheckBox.IsChecked = _editingItem.Scout == 1;
        MageCheckBox.IsChecked = _editingItem.Mage == 1;
        ClericCheckBox.IsChecked = _editingItem.Cleric == 1;
        EngineerCheckBox.IsChecked = _editingItem.Engineer == 1;
        ArtistCheckBox.IsChecked = _editingItem.Artist == 1;
        FighterCheckBox.IsChecked = _editingItem.Fighter == 1;
        KnightCheckBox.IsChecked = _editingItem.Knight == 1;
        AssassinCheckBox.IsChecked = _editingItem.Assassin == 1;
        RangerCheckBox.IsChecked = _editingItem.Ranger == 1;
        WizardCheckBox.IsChecked = _editingItem.Wizard == 1;
        ElementalistCheckBox.IsChecked = _editingItem.Elementalist == 1;
        ChanterCheckBox.IsChecked = _editingItem.Chanter == 1;
        PriestCheckBox.IsChecked = _editingItem.Priest == 1;
        GunnerCheckBox.IsChecked = _editingItem.Gunner == 1;
        BardCheckBox.IsChecked = _editingItem.Bard == 1;
        RiderCheckBox.IsChecked = _editingItem.Rider == 1;
        
        // 交易信息
        TradePriceTextBox.Text = _editingItem.Price.ToString();
        SetComboBoxValue(ExtraCurrencyItemComboBox, _editingItem.ExtraCurrencyItem);
        ExtraCurrencyItemCountTextBox.Text = _editingItem.ExtraCurrencyItemCount.ToString();
        TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;

        // 设置交易模式
        SetTradeMode();

        // 物品属性
        CanExchangeCheckBox.IsChecked = _editingItem.CanExchange;
        CanSellToNpcCheckBox.IsChecked = _editingItem.CanSellToNpc;
        CanDepositToCharacterWarehouseCheckBox.IsChecked = _editingItem.CanDepositToCharacterWarehouse;
        CanDepositToAccountWarehouseCheckBox.IsChecked = _editingItem.CanDepositToAccountWarehouse;
        CanDepositToGuildWarehouseCheckBox.IsChecked = _editingItem.CanDepositToGuildWarehouse;
        BreakableCheckBox.IsChecked = _editingItem.Breakable;
        SoulBindCheckBox.IsChecked = _editingItem.SoulBind;
        RemoveWhenLogoutCheckBox.IsChecked = _editingItem.RemoveWhenLogout;
        LoreCheckBox.IsChecked = _editingItem.Lore;
        ItemDropPermittedCheckBox.IsChecked = _editingItem.ItemDropPermitted;

        // 额外权限
        CanSplitCheckBox.IsChecked = _editingItem.CanSplit;
        CanApExtractionCheckBox.IsChecked = _editingItem.CanApExtraction;

        // 种族和性别限制
        SetComboBoxValue(RacePermittedComboBox, _editingItem.RacePermitted);
        SetComboBoxValue(GenderPermittedComboBox, _editingItem.GenderPermitted);

        // 特殊字段
        ActivationSkillTextBox.Text = _editingItem.ActivationSkill;
        SetComboBoxValue(ActivationModeComboBox, _editingItem.ActivationMode);
        ExtraInventoryTextBox.Text = _editingItem.ExtraInventory.ToString();
        DisassemblyItemTextBox.Text = _editingItem.DisassemblyItem.ToString();

        // 更新摘要显示
        UpdateTradeSummaries();
        
        // 武器专用属性
        SetComboBoxValue(WeaponTypeComboBox, _editingItem.WeaponType);
        DmgDecalTextBox.Text = _editingItem.DmgDecal.ToString();
        EquipBoneTextBox.Text = _editingItem.EquipBone;
        CombatEquipBoneTextBox.Text = _editingItem.CombatEquipBone;

        // 武器攻击属性
        MinDamageTextBox.Text = _editingItem.MinDamage.ToString();
        MaxDamageTextBox.Text = _editingItem.MaxDamage.ToString();
        StrTextBox.Text = _editingItem.Str.ToString();
        AgiTextBox.Text = _editingItem.Agi.ToString();
        KnoTextBox.Text = _editingItem.Kno.ToString();
        HitAccuracyTextBox.Text = _editingItem.HitAccuracy.ToString();
        CriticalTextBox.Text = _editingItem.Critical.ToString();
        ParryTextBox.Text = _editingItem.Parry.ToString();
        MagicalSkillBoostTextBox.Text = _editingItem.MagicalSkillBoost.ToString();
        MagicalHitAccuracyTextBox.Text = _editingItem.MagicalHitAccuracy.ToString();
        SetComboBoxValue(AttackTypeComboBox, _editingItem.AttackType);
        AttackDelayTextBox.Text = _editingItem.AttackDelay.ToString();
        AttackRangeTextBox.Text = _editingItem.AttackRange.ToString("F6");
        RobotNameTextBox.Text = _editingItem.RobotName;

        // 基础奖励属性
        BonusAttr1TextBox.Text = _editingItem.BonusAttr1;
        BonusAttr2TextBox.Text = _editingItem.BonusAttr2;
        BonusAttr3TextBox.Text = _editingItem.BonusAttr3;
        BonusAttr4TextBox.Text = _editingItem.BonusAttr4;
        BonusAttr5TextBox.Text = _editingItem.BonusAttr5;
        BonusAttr6TextBox.Text = _editingItem.BonusAttr6;
        BonusAttr7TextBox.Text = _editingItem.BonusAttr7;
        BonusAttr8TextBox.Text = _editingItem.BonusAttr8;

        // 高级奖励属性
        BonusAttrA1TextBox.Text = _editingItem.BonusAttrA1;
        BonusAttrA2TextBox.Text = _editingItem.BonusAttrA2;
        BonusAttrA3TextBox.Text = _editingItem.BonusAttrA3;
        BonusAttrA4TextBox.Text = _editingItem.BonusAttrA4;
        BonusAttrA5TextBox.Text = _editingItem.BonusAttrA5;
        BonusAttrA6TextBox.Text = _editingItem.BonusAttrA6;
        BonusAttrA7TextBox.Text = _editingItem.BonusAttrA7;
        BonusAttrA8TextBox.Text = _editingItem.BonusAttrA8;

        // 特殊效果属性
        BonusAttrB1TextBox.Text = _editingItem.BonusAttrB1;
        BonusAttrB2TextBox.Text = _editingItem.BonusAttrB2;
        BonusAttrB3TextBox.Text = _editingItem.BonusAttrB3;
        BonusAttrB4TextBox.Text = _editingItem.BonusAttrB4;

        // 战斗和强化属性
        BurnOnAttackTextBox.Text = _editingItem.BurnOnAttack.ToString();
        BurnOnDefendTextBox.Text = _editingItem.BurnOnDefend.ToString();
        PolishBurnOnAttackTextBox.Text = _editingItem.PolishBurnOnAttack.ToString();
        PolishBurnOnDefendTextBox.Text = _editingItem.PolishBurnOnDefend.ToString();

        BmRestrictCategoryTextBox.Text = _editingItem.BmRestrictCategory.ToString();
        DisassemblyItemTextBox.Text = _editingItem.DisassemblyItem.ToString();

        // 高级属性
        DescLongTextBox.Text = _editingItem.DescLong;
        MaterialTextBox.Text = _editingItem.Material;
        IconNameTextBox.Text = _editingItem.IconName;
        MeshTextBox.Text = _editingItem.Mesh;
        VisualSlotTextBox.Text = _editingItem.VisualSlot;
        SetComboBoxValue(GenderPermittedComboBox, _editingItem.GenderPermitted);

        DefaultColorMTextBox.Text = _editingItem.DefaultColorM;
        DefaultColorFTextBox.Text = _editingItem.DefaultColorF;
        DisposableTradeItemTextBox.Text = _editingItem.DisposableTradeItem;
        DisposableTradeItemCountTextBox.Text = _editingItem.DisposableTradeItemCount.ToString();
        CanPackCountTextBox.Text = _editingItem.CanPackCount.ToString();

        // 根据物品类型显示相应的属性区域
        UpdateAttributeVisibility();

        // 绑定事件
        BindEvents();
    }

    private void BindEvents()
    {
        // 基础信息变更事件
        NameTextBox.TextChanged += (s, e) => { _editingItem.Name = NameTextBox.Text; DataChanged = true; };
        DescTextBox.TextChanged += (s, e) => { _editingItem.Desc = DescTextBox.Text; DataChanged = true; UpdateLocalizedText(); };
        
        // 类型变更事件
        ArmorTypeComboBox.SelectionChanged += (s, e) => {
            _editingItem.ArmorType = GetComboBoxValue(ArmorTypeComboBox);
            DataChanged = true;
            UpdateAttributeVisibility();
        };
        ItemTypeComboBox.SelectionChanged += (s, e) => {
            _editingItem.ItemType = GetComboBoxValue(ItemTypeComboBox);
            DataChanged = true;
        };
        QualityComboBox.SelectionChanged += (s, e) => {
            _editingItem.Quality = GetComboBoxValue(QualityComboBox);
            DataChanged = true;
        };
        EquipmentSlotsComboBox.SelectionChanged += (s, e) => {
            _editingItem.EquipmentSlots = GetComboBoxValue(EquipmentSlotsComboBox);
            DataChanged = true;
            UpdateAttributeVisibility();
        };
        
        // 数值变更事件
        PriceTextBox.TextChanged += (s, e) => { if (int.TryParse(PriceTextBox.Text, out var price)) { _editingItem.Price = price; DataChanged = true; } };
        LevelTextBox.TextChanged += (s, e) => { if (int.TryParse(LevelTextBox.Text, out var level)) { _editingItem.Level = level; DataChanged = true; } };
        MaxStackCountTextBox.TextChanged += (s, e) => { if (int.TryParse(MaxStackCountTextBox.Text, out var count)) { _editingItem.MaxStackCount = count; DataChanged = true; } };
        DodgeTextBox.TextChanged += (s, e) => { if (int.TryParse(DodgeTextBox.Text, out var dodge)) { _editingItem.Dodge = dodge; DataChanged = true; } };
        PhysicalDefendTextBox.TextChanged += (s, e) => { if (int.TryParse(PhysicalDefendTextBox.Text, out var defend)) { _editingItem.PhysicalDefend = defend; DataChanged = true; } };
        MagicalResistTextBox.TextChanged += (s, e) => { if (int.TryParse(MagicalResistTextBox.Text, out var resist)) { _editingItem.MagicalResist = resist; DataChanged = true; } };

        // 新增数值属性变更事件
        EquipTypeTextBox.TextChanged += (s, e) => { if (int.TryParse(EquipTypeTextBox.Text, out var equipType)) { _editingItem.EquipType = equipType; DataChanged = true; } };
        ExpireTimeTextBox.TextChanged += (s, e) => { if (int.TryParse(ExpireTimeTextBox.Text, out var expireTime)) { _editingItem.ExpireTime = expireTime; DataChanged = true; } };
        UsableRankMinTextBox.TextChanged += (s, e) => { if (int.TryParse(UsableRankMinTextBox.Text, out var usableRank)) { _editingItem.UsableRankMin = usableRank; DataChanged = true; } };
        PurchableRankMinTextBox.TextChanged += (s, e) => { if (int.TryParse(PurchableRankMinTextBox.Text, out var purchableRank)) { _editingItem.PurchableRankMin = purchableRank; DataChanged = true; } };
        ScaleTextBox.TextChanged += (s, e) => { if (float.TryParse(ScaleTextBox.Text, out var scale)) { _editingItem.Scale = scale; DataChanged = true; } };
        ExtractSkinTypeTextBox.TextChanged += (s, e) => { if (int.TryParse(ExtractSkinTypeTextBox.Text, out var extractSkin)) { _editingItem.ExtractSkinType = extractSkin; DataChanged = true; } };

        // 交易信息变更事件
        TradePriceTextBox.TextChanged += (s, e) => { if (int.TryParse(TradePriceTextBox.Text, out var price)) { _editingItem.Price = price; DataChanged = true; } };
        ExtraCurrencyItemComboBox.SelectionChanged += (s, e) => { _editingItem.ExtraCurrencyItem = GetComboBoxValue(ExtraCurrencyItemComboBox); DataChanged = true; UpdateTradeSummaries(); };
        ExtraCurrencyItemComboBox.LostFocus += (s, e) => { _editingItem.ExtraCurrencyItem = ExtraCurrencyItemComboBox.Text; DataChanged = true; UpdateTradeSummaries(); };
        ExtraCurrencyItemCountTextBox.TextChanged += (s, e) => { if (int.TryParse(ExtraCurrencyItemCountTextBox.Text, out var count)) { _editingItem.ExtraCurrencyItemCount = count; DataChanged = true; } };
        
        // 职业权限变更事件
        WarriorCheckBox.Checked += (s, e) => { _editingItem.Warrior = 1; DataChanged = true; };
        WarriorCheckBox.Unchecked += (s, e) => { _editingItem.Warrior = 0; DataChanged = true; };
        ScoutCheckBox.Checked += (s, e) => { _editingItem.Scout = 1; DataChanged = true; };
        ScoutCheckBox.Unchecked += (s, e) => { _editingItem.Scout = 0; DataChanged = true; };
        MageCheckBox.Checked += (s, e) => { _editingItem.Mage = 1; DataChanged = true; };
        MageCheckBox.Unchecked += (s, e) => { _editingItem.Mage = 0; DataChanged = true; };
        ClericCheckBox.Checked += (s, e) => { _editingItem.Cleric = 1; DataChanged = true; };
        ClericCheckBox.Unchecked += (s, e) => { _editingItem.Cleric = 0; DataChanged = true; };
        EngineerCheckBox.Checked += (s, e) => { _editingItem.Engineer = 1; DataChanged = true; };
        EngineerCheckBox.Unchecked += (s, e) => { _editingItem.Engineer = 0; DataChanged = true; };
        ArtistCheckBox.Checked += (s, e) => { _editingItem.Artist = 1; DataChanged = true; };
        ArtistCheckBox.Unchecked += (s, e) => { _editingItem.Artist = 0; DataChanged = true; };
        FighterCheckBox.Checked += (s, e) => { _editingItem.Fighter = 1; DataChanged = true; };
        FighterCheckBox.Unchecked += (s, e) => { _editingItem.Fighter = 0; DataChanged = true; };
        KnightCheckBox.Checked += (s, e) => { _editingItem.Knight = 1; DataChanged = true; };
        KnightCheckBox.Unchecked += (s, e) => { _editingItem.Knight = 0; DataChanged = true; };
        AssassinCheckBox.Checked += (s, e) => { _editingItem.Assassin = 1; DataChanged = true; };
        AssassinCheckBox.Unchecked += (s, e) => { _editingItem.Assassin = 0; DataChanged = true; };
        RangerCheckBox.Checked += (s, e) => { _editingItem.Ranger = 1; DataChanged = true; };
        RangerCheckBox.Unchecked += (s, e) => { _editingItem.Ranger = 0; DataChanged = true; };
        WizardCheckBox.Checked += (s, e) => { _editingItem.Wizard = 1; DataChanged = true; };
        WizardCheckBox.Unchecked += (s, e) => { _editingItem.Wizard = 0; DataChanged = true; };
        ElementalistCheckBox.Checked += (s, e) => { _editingItem.Elementalist = 1; DataChanged = true; };
        ElementalistCheckBox.Unchecked += (s, e) => { _editingItem.Elementalist = 0; DataChanged = true; };
        ChanterCheckBox.Checked += (s, e) => { _editingItem.Chanter = 1; DataChanged = true; };
        ChanterCheckBox.Unchecked += (s, e) => { _editingItem.Chanter = 0; DataChanged = true; };
        PriestCheckBox.Checked += (s, e) => { _editingItem.Priest = 1; DataChanged = true; };
        PriestCheckBox.Unchecked += (s, e) => { _editingItem.Priest = 0; DataChanged = true; };
        GunnerCheckBox.Checked += (s, e) => { _editingItem.Gunner = 1; DataChanged = true; };
        GunnerCheckBox.Unchecked += (s, e) => { _editingItem.Gunner = 0; DataChanged = true; };
        BardCheckBox.Checked += (s, e) => { _editingItem.Bard = 1; DataChanged = true; };
        BardCheckBox.Unchecked += (s, e) => { _editingItem.Bard = 0; DataChanged = true; };
        RiderCheckBox.Checked += (s, e) => { _editingItem.Rider = 1; DataChanged = true; };
        RiderCheckBox.Unchecked += (s, e) => { _editingItem.Rider = 0; DataChanged = true; };
        
        // 物品属性变更事件
        CanExchangeCheckBox.Checked += (s, e) => { _editingItem.CanExchange = true; DataChanged = true; UpdateTradeSummaries(); };
        CanExchangeCheckBox.Unchecked += (s, e) => { _editingItem.CanExchange = false; DataChanged = true; UpdateTradeSummaries(); };
        CanSellToNpcCheckBox.Checked += (s, e) => { _editingItem.CanSellToNpc = true; DataChanged = true; UpdateTradeSummaries(); };
        CanSellToNpcCheckBox.Unchecked += (s, e) => { _editingItem.CanSellToNpc = false; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToCharacterWarehouseCheckBox.Checked += (s, e) => { _editingItem.CanDepositToCharacterWarehouse = true; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToCharacterWarehouseCheckBox.Unchecked += (s, e) => { _editingItem.CanDepositToCharacterWarehouse = false; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToAccountWarehouseCheckBox.Checked += (s, e) => { _editingItem.CanDepositToAccountWarehouse = true; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToAccountWarehouseCheckBox.Unchecked += (s, e) => { _editingItem.CanDepositToAccountWarehouse = false; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToGuildWarehouseCheckBox.Checked += (s, e) => { _editingItem.CanDepositToGuildWarehouse = true; DataChanged = true; UpdateTradeSummaries(); };
        CanDepositToGuildWarehouseCheckBox.Unchecked += (s, e) => { _editingItem.CanDepositToGuildWarehouse = false; DataChanged = true; UpdateTradeSummaries(); };
        BreakableCheckBox.Checked += (s, e) => { _editingItem.Breakable = true; DataChanged = true; UpdateTradeSummaries(); };
        BreakableCheckBox.Unchecked += (s, e) => { _editingItem.Breakable = false; DataChanged = true; UpdateTradeSummaries(); };
        SoulBindCheckBox.Checked += (s, e) => { _editingItem.SoulBind = true; DataChanged = true; UpdateTradeSummaries(); };
        SoulBindCheckBox.Unchecked += (s, e) => { _editingItem.SoulBind = false; DataChanged = true; UpdateTradeSummaries(); };
        RemoveWhenLogoutCheckBox.Checked += (s, e) => { _editingItem.RemoveWhenLogout = true; DataChanged = true; UpdateTradeSummaries(); };
        RemoveWhenLogoutCheckBox.Unchecked += (s, e) => { _editingItem.RemoveWhenLogout = false; DataChanged = true; UpdateTradeSummaries(); };
        LoreCheckBox.Checked += (s, e) => { _editingItem.Lore = true; DataChanged = true; UpdateTradeSummaries(); };
        LoreCheckBox.Unchecked += (s, e) => { _editingItem.Lore = false; DataChanged = true; UpdateTradeSummaries(); };
        ItemDropPermittedCheckBox.Checked += (s, e) => { _editingItem.ItemDropPermitted = true; DataChanged = true; UpdateTradeSummaries(); };
        ItemDropPermittedCheckBox.Unchecked += (s, e) => { _editingItem.ItemDropPermitted = false; DataChanged = true; UpdateTradeSummaries(); };

        // 额外权限变更事件
        CanSplitCheckBox.Checked += (s, e) => { _editingItem.CanSplit = true; DataChanged = true; UpdateTradeSummaries(); };
        CanSplitCheckBox.Unchecked += (s, e) => { _editingItem.CanSplit = false; DataChanged = true; UpdateTradeSummaries(); };
        CanApExtractionCheckBox.Checked += (s, e) => { _editingItem.CanApExtraction = true; DataChanged = true; UpdateTradeSummaries(); };
        CanApExtractionCheckBox.Unchecked += (s, e) => { _editingItem.CanApExtraction = false; DataChanged = true; UpdateTradeSummaries(); };


        // 种族和性别限制变更事件
        RacePermittedComboBox.SelectionChanged += (s, e) => { _editingItem.RacePermitted = GetComboBoxValue(RacePermittedComboBox); DataChanged = true; };
        GenderPermittedComboBox.SelectionChanged += (s, e) => { _editingItem.GenderPermitted = GetComboBoxValue(GenderPermittedComboBox); DataChanged = true; };

        // 特殊字段变更事件
        ActivationSkillTextBox.TextChanged += (s, e) => { _editingItem.ActivationSkill = ActivationSkillTextBox.Text; DataChanged = true; UpdateTradeSummaries(); };
        ActivationModeComboBox.SelectionChanged += (s, e) => { _editingItem.ActivationMode = GetComboBoxValue(ActivationModeComboBox); DataChanged = true; UpdateTradeSummaries(); };
        ExtraInventoryTextBox.TextChanged += (s, e) => { if (int.TryParse(ExtraInventoryTextBox.Text, out var inventory)) { _editingItem.ExtraInventory = inventory; DataChanged = true; } };
        DisassemblyItemTextBox.TextChanged += (s, e) => { if (int.TryParse(DisassemblyItemTextBox.Text, out var disassembly)) { _editingItem.DisassemblyItem = disassembly; DataChanged = true; } };

        // 交易信息变更时更新摘要
        TradePriceTextBox.TextChanged += (s, e) => UpdateTradeSummaries();
        ExtraCurrencyItemCountTextBox.TextChanged += (s, e) => UpdateTradeSummaries();
        
        // 武器专用属性变更事件
        WeaponTypeComboBox.SelectionChanged += (s, e) => { _editingItem.WeaponType = GetComboBoxValue(WeaponTypeComboBox); DataChanged = true; UpdateAttributeVisibility(); };
        DmgDecalTextBox.TextChanged += (s, e) => { if (int.TryParse(DmgDecalTextBox.Text, out var decal)) { _editingItem.DmgDecal = decal; DataChanged = true; } };
        EquipBoneTextBox.TextChanged += (s, e) => { _editingItem.EquipBone = EquipBoneTextBox.Text; DataChanged = true; };
        CombatEquipBoneTextBox.TextChanged += (s, e) => { _editingItem.CombatEquipBone = CombatEquipBoneTextBox.Text; DataChanged = true; };

        // 武器攻击属性变更事件
        MinDamageTextBox.TextChanged += (s, e) => { if (int.TryParse(MinDamageTextBox.Text, out var minDmg)) { _editingItem.MinDamage = minDmg; DataChanged = true; } };
        MaxDamageTextBox.TextChanged += (s, e) => { if (int.TryParse(MaxDamageTextBox.Text, out var maxDmg)) { _editingItem.MaxDamage = maxDmg; DataChanged = true; } };
        StrTextBox.TextChanged += (s, e) => { if (int.TryParse(StrTextBox.Text, out var str)) { _editingItem.Str = str; DataChanged = true; } };
        AgiTextBox.TextChanged += (s, e) => { if (int.TryParse(AgiTextBox.Text, out var agi)) { _editingItem.Agi = agi; DataChanged = true; } };
        KnoTextBox.TextChanged += (s, e) => { if (int.TryParse(KnoTextBox.Text, out var kno)) { _editingItem.Kno = kno; DataChanged = true; } };
        HitAccuracyTextBox.TextChanged += (s, e) => { if (int.TryParse(HitAccuracyTextBox.Text, out var hitAcc)) { _editingItem.HitAccuracy = hitAcc; DataChanged = true; } };
        CriticalTextBox.TextChanged += (s, e) => { if (int.TryParse(CriticalTextBox.Text, out var crit)) { _editingItem.Critical = crit; DataChanged = true; } };
        ParryTextBox.TextChanged += (s, e) => { if (int.TryParse(ParryTextBox.Text, out var parry)) { _editingItem.Parry = parry; DataChanged = true; } };
        MagicalSkillBoostTextBox.TextChanged += (s, e) => { if (int.TryParse(MagicalSkillBoostTextBox.Text, out var magBoost)) { _editingItem.MagicalSkillBoost = magBoost; DataChanged = true; } };
        MagicalHitAccuracyTextBox.TextChanged += (s, e) => { if (int.TryParse(MagicalHitAccuracyTextBox.Text, out var magHitAcc)) { _editingItem.MagicalHitAccuracy = magHitAcc; DataChanged = true; } };
        AttackTypeComboBox.SelectionChanged += (s, e) => { _editingItem.AttackType = GetComboBoxValue(AttackTypeComboBox); DataChanged = true; };
        AttackDelayTextBox.TextChanged += (s, e) => { if (int.TryParse(AttackDelayTextBox.Text, out var atkDelay)) { _editingItem.AttackDelay = atkDelay; DataChanged = true; } };
        AttackRangeTextBox.TextChanged += (s, e) => { if (float.TryParse(AttackRangeTextBox.Text, out var atkRange)) { _editingItem.AttackRange = atkRange; DataChanged = true; } };
        RobotNameTextBox.TextChanged += (s, e) => { _editingItem.RobotName = RobotNameTextBox.Text; DataChanged = true; };

        // 基础奖励属性变更事件
        BonusAttr1TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr1 = BonusAttr1TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr2TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr2 = BonusAttr2TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr3TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr3 = BonusAttr3TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr4TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr4 = BonusAttr4TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr5TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr5 = BonusAttr5TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr6TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr6 = BonusAttr6TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr7TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr7 = BonusAttr7TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttr8TextBox.TextChanged += (s, e) => { _editingItem.BonusAttr8 = BonusAttr8TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };

        // 高级奖励属性变更事件
        BonusAttrA1TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA1 = BonusAttrA1TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA2TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA2 = BonusAttrA2TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA3TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA3 = BonusAttrA3TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA4TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA4 = BonusAttrA4TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA5TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA5 = BonusAttrA5TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA6TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA6 = BonusAttrA6TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA7TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA7 = BonusAttrA7TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrA8TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrA8 = BonusAttrA8TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };

        // 特殊效果属性变更事件
        BonusAttrB1TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrB1 = BonusAttrB1TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrB2TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrB2 = BonusAttrB2TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrB3TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrB3 = BonusAttrB3TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        BonusAttrB4TextBox.TextChanged += (s, e) => { _editingItem.BonusAttrB4 = BonusAttrB4TextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };

        // 战斗和强化属性变更事件
        BurnOnAttackTextBox.TextChanged += (s, e) => { if (int.TryParse(BurnOnAttackTextBox.Text, out var burn)) { _editingItem.BurnOnAttack = burn; DataChanged = true; } };
        BurnOnDefendTextBox.TextChanged += (s, e) => { if (int.TryParse(BurnOnDefendTextBox.Text, out var burn)) { _editingItem.BurnOnDefend = burn; DataChanged = true; } };
        PolishBurnOnAttackTextBox.TextChanged += (s, e) => { if (int.TryParse(PolishBurnOnAttackTextBox.Text, out var burn)) { _editingItem.PolishBurnOnAttack = burn; DataChanged = true; } };
        PolishBurnOnDefendTextBox.TextChanged += (s, e) => { if (int.TryParse(PolishBurnOnDefendTextBox.Text, out var burn)) { _editingItem.PolishBurnOnDefend = burn; DataChanged = true; } };

        BmRestrictCategoryTextBox.TextChanged += (s, e) => { if (int.TryParse(BmRestrictCategoryTextBox.Text, out var cat)) { _editingItem.BmRestrictCategory = cat; DataChanged = true; } };
        DisassemblyItemTextBox.TextChanged += (s, e) => { if (int.TryParse(DisassemblyItemTextBox.Text, out var item)) { _editingItem.DisassemblyItem = item; DataChanged = true; } };

        // 高级属性变更事件
        DescLongTextBox.TextChanged += (s, e) => { _editingItem.DescLong = DescLongTextBox.Text; DataChanged = true; };
        MaterialTextBox.TextChanged += (s, e) => { _editingItem.Material = MaterialTextBox.Text; DataChanged = true; };
        IconNameTextBox.TextChanged += (s, e) => { _editingItem.IconName = IconNameTextBox.Text; DataChanged = true; };
        MeshTextBox.TextChanged += (s, e) => { _editingItem.Mesh = MeshTextBox.Text; DataChanged = true; };
        VisualSlotTextBox.TextChanged += (s, e) => { _editingItem.VisualSlot = VisualSlotTextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        GenderPermittedComboBox.SelectionChanged += (s, e) => { _editingItem.GenderPermitted = GetComboBoxValue(GenderPermittedComboBox); DataChanged = true; };

        DefaultColorMTextBox.TextChanged += (s, e) => { _editingItem.DefaultColorM = DefaultColorMTextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        DefaultColorFTextBox.TextChanged += (s, e) => { _editingItem.DefaultColorF = DefaultColorFTextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        DisposableTradeItemTextBox.TextChanged += (s, e) => { _editingItem.DisposableTradeItem = DisposableTradeItemTextBox.Text; DataChanged = true; UpdateAttributeVisibility(); };
        DisposableTradeItemCountTextBox.TextChanged += (s, e) => { if (int.TryParse(DisposableTradeItemCountTextBox.Text, out var count)) { _editingItem.DisposableTradeItemCount = count; DataChanged = true; } };
        CanPackCountTextBox.TextChanged += (s, e) => { if (int.TryParse(CanPackCountTextBox.Text, out var count)) { _editingItem.CanPackCount = count; DataChanged = true; UpdateAttributeVisibility(); } };
    }

    private void UpdateLocalizedText()
    {
        if (!string.IsNullOrEmpty(_editingItem.Desc))
        {
            var localizedText = _stringService.GetString(_editingItem.Desc);
            LocalizedNameText.Text = !string.IsNullOrEmpty(localizedText) && localizedText != _editingItem.Desc 
                ? localizedText 
                : "未找到本地化文本";
        }
        else
        {
            LocalizedNameText.Text = "未设置描述键";
        }
    }

    private void SetComboBoxValue(ComboBox comboBox, string value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Content.ToString() == value)
            {
                comboBox.SelectedItem = item;
                break;
            }
        }
    }

    private string GetComboBoxValue(ComboBox comboBox)
    {
        return (comboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "";
    }

    /// <summary>
    /// 根据物品类型和属性更新属性区域的可见性
    /// </summary>
    private void UpdateAttributeVisibility()
    {
        if (_editingItem == null) return;

        // 检查武器专用属性
        bool hasWeaponSpecific = HasWeaponSpecificAttributes(_editingItem);
        WeaponSpecificBorder.Visibility = hasWeaponSpecific ? Visibility.Visible : Visibility.Collapsed;

        // 检查基础奖励属性
        bool hasBasicBonus = HasBasicBonusAttributes(_editingItem);
        BasicBonusBorder.Visibility = hasBasicBonus ? Visibility.Visible : Visibility.Collapsed;

        // 检查高级奖励属性
        bool hasAdvancedBonus = HasAdvancedBonusAttributes(_editingItem);
        AdvancedBonusBorder.Visibility = hasAdvancedBonus ? Visibility.Visible : Visibility.Collapsed;

        // 检查特殊效果属性
        bool hasSpecialEffect = HasSpecialEffectAttributes(_editingItem);
        SpecialEffectBorder.Visibility = hasSpecialEffect ? Visibility.Visible : Visibility.Collapsed;

        // 检查战斗和强化属性
        bool hasCombatEnhance = HasCombatEnhanceAttributes(_editingItem);
        CombatEnhanceBorder.Visibility = hasCombatEnhance ? Visibility.Visible : Visibility.Collapsed;

        // 更新窗口标题，显示物品类型信息
        UpdateWindowTitle();
    }

    /// <summary>
    /// 检查是否有武器专用属性
    /// </summary>
    private bool HasWeaponSpecificAttributes(ClientItem item)
    {
        return !string.IsNullOrEmpty(item.WeaponType) ||
               !string.IsNullOrEmpty(item.EquipBone) ||
               !string.IsNullOrEmpty(item.CombatEquipBone) ||
               item.DmgDecal > 0 ||
               item.ArmorType == "weapon" ||
               item.EquipmentSlots == "main_hand" ||
               item.EquipmentSlots == "sub_hand" ||
               item.EquipmentSlots == "main_or_sub";
    }

    /// <summary>
    /// 检查是否有基础奖励属性
    /// </summary>
    private bool HasBasicBonusAttributes(ClientItem item)
    {
        return !string.IsNullOrEmpty(item.BonusAttr1) ||
               !string.IsNullOrEmpty(item.BonusAttr2) ||
               !string.IsNullOrEmpty(item.BonusAttr3) ||
               !string.IsNullOrEmpty(item.BonusAttr4) ||
               !string.IsNullOrEmpty(item.BonusAttr5) ||
               !string.IsNullOrEmpty(item.BonusAttr6) ||
               !string.IsNullOrEmpty(item.BonusAttr7) ||
               !string.IsNullOrEmpty(item.BonusAttr8);
    }

    /// <summary>
    /// 检查是否有高级奖励属性
    /// </summary>
    private bool HasAdvancedBonusAttributes(ClientItem item)
    {
        return !string.IsNullOrEmpty(item.BonusAttrA1) ||
               !string.IsNullOrEmpty(item.BonusAttrA2) ||
               !string.IsNullOrEmpty(item.BonusAttrA3) ||
               !string.IsNullOrEmpty(item.BonusAttrA4) ||
               !string.IsNullOrEmpty(item.BonusAttrA5) ||
               !string.IsNullOrEmpty(item.BonusAttrA6) ||
               !string.IsNullOrEmpty(item.BonusAttrA7) ||
               !string.IsNullOrEmpty(item.BonusAttrA8);
    }

    /// <summary>
    /// 检查是否有特殊效果属性
    /// </summary>
    private bool HasSpecialEffectAttributes(ClientItem item)
    {
        return !string.IsNullOrEmpty(item.BonusAttrB1) ||
               !string.IsNullOrEmpty(item.BonusAttrB2) ||
               !string.IsNullOrEmpty(item.BonusAttrB3) ||
               !string.IsNullOrEmpty(item.BonusAttrB4);
    }

    /// <summary>
    /// 检查是否有战斗和强化属性
    /// </summary>
    private bool HasCombatEnhanceAttributes(ClientItem item)
    {
        return item.BurnOnAttack > 0 ||
               item.BurnOnDefend > 0 ||
               item.PolishBurnOnAttack > 0 ||
               item.PolishBurnOnDefend > 0 ||
               !item.CanProcEnchant ||
               !item.CanCompositeWeapon ||
               item.CanPolish ||
               item.DisassemblyItem > 0 ||
               item.BmRestrictCategory > 0;
    }

    /// <summary>
    /// 检查物品是否具有武器装备属性
    /// </summary>
    private bool HasWeaponAttributes(ClientItem item)
    {
        // 检查武器专用属性
        if (!string.IsNullOrEmpty(item.WeaponType) ||
            !string.IsNullOrEmpty(item.EquipBone) ||
            !string.IsNullOrEmpty(item.CombatEquipBone) ||
            item.DmgDecal > 0)
        {
            return true;
        }

        // 检查基础奖励属性
        if (!string.IsNullOrEmpty(item.BonusAttr1) ||
            !string.IsNullOrEmpty(item.BonusAttr2) ||
            !string.IsNullOrEmpty(item.BonusAttr3) ||
            !string.IsNullOrEmpty(item.BonusAttr4) ||
            !string.IsNullOrEmpty(item.BonusAttr5) ||
            !string.IsNullOrEmpty(item.BonusAttr6) ||
            !string.IsNullOrEmpty(item.BonusAttr7) ||
            !string.IsNullOrEmpty(item.BonusAttr8))
        {
            return true;
        }

        // 检查高级奖励属性
        if (!string.IsNullOrEmpty(item.BonusAttrA1) ||
            !string.IsNullOrEmpty(item.BonusAttrA2) ||
            !string.IsNullOrEmpty(item.BonusAttrA3) ||
            !string.IsNullOrEmpty(item.BonusAttrA4) ||
            !string.IsNullOrEmpty(item.BonusAttrA5) ||
            !string.IsNullOrEmpty(item.BonusAttrA6) ||
            !string.IsNullOrEmpty(item.BonusAttrA7) ||
            !string.IsNullOrEmpty(item.BonusAttrA8))
        {
            return true;
        }

        // 检查特殊效果属性
        if (!string.IsNullOrEmpty(item.BonusAttrB1) ||
            !string.IsNullOrEmpty(item.BonusAttrB2) ||
            !string.IsNullOrEmpty(item.BonusAttrB3) ||
            !string.IsNullOrEmpty(item.BonusAttrB4))
        {
            return true;
        }

        // 检查战斗属性
        if (item.BurnOnAttack > 0 || item.BurnOnDefend > 0 ||
            item.PolishBurnOnAttack > 0 || item.PolishBurnOnDefend > 0)
        {
            return true;
        }

        // 检查强化属性
        if (!item.CanProcEnchant || !item.CanCompositeWeapon || item.CanPolish ||
            item.DisassemblyItem > 0)
        {
            return true;
        }

        // 检查特殊外观属性
        if (!string.IsNullOrEmpty(item.VisualSlot) ||
            !string.IsNullOrEmpty(item.DisposableTradeItem) ||
            item.CanPackCount > 0)
        {
            return true;
        }

        // 检查是否为武器类型
        if (item.ArmorType == "weapon" ||
            item.EquipmentSlots == "main_hand" ||
            item.EquipmentSlots == "sub_hand" ||
            item.EquipmentSlots == "main_or_sub")
        {
            return true;
        }

        // 检查是否为高级装备（有特殊颜色设置）
        if (item.DefaultColorM != "255,255,255" || item.DefaultColorF != "255,255,255")
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 更新窗口标题，显示物品类型和属性信息
    /// </summary>
    private void UpdateWindowTitle()
    {
        if (_editingItem == null) return;

        var titleParts = new List<string> { "物品编辑器" };

        // 添加物品类型信息
        if (!string.IsNullOrEmpty(_editingItem.ArmorType))
        {
            string typeDisplay = _editingItem.ArmorType switch
            {
                "weapon" => "武器",
                "clothes" => "服装",
                "armor" => "护甲",
                "accessory" => "饰品",
                "shield" => "盾牌",
                _ => _editingItem.ArmorType
            };
            titleParts.Add($"类型: {typeDisplay}");
        }

        // 添加装备部位信息
        if (!string.IsNullOrEmpty(_editingItem.EquipmentSlots))
        {
            string slotDisplay = _editingItem.EquipmentSlots switch
            {
                "main_hand" => "主手",
                "sub_hand" => "副手",
                "torso" => "上身",
                "leg" => "下身",
                "head" => "头部",
                "hand" => "手部",
                "foot" => "脚部",
                "earring" => "耳环",
                "neck" => "项链",
                "ring" => "戒指",
                _ => _editingItem.EquipmentSlots
            };
            titleParts.Add($"部位: {slotDisplay}");
        }

        // 添加属性信息
        if (HasWeaponAttributes(_editingItem))
        {
            titleParts.Add("包含武器装备属性");
        }

        Title = string.Join(" - ", titleParts);
    }

    private ClientItem CloneItem(ClientItem original)
    {
        // 创建物品的深拷贝
        return new ClientItem
        {
            Id = original.Id,
            Name = original.Name,
            Desc = original.Desc,
            ArmorType = original.ArmorType,
            ItemType = original.ItemType,
            Material = original.Material,
            IconName = original.IconName,
            Price = original.Price,
            MaxStackCount = original.MaxStackCount,
            EquipmentSlots = original.EquipmentSlots,
            Dodge = original.Dodge,
            Quality = original.Quality,
            Level = original.Level,
            MagicalResist = original.MagicalResist,
            PhysicalDefend = original.PhysicalDefend,
            Lore = original.Lore,
            CanExchange = original.CanExchange,
            CanSellToNpc = original.CanSellToNpc,
            CanDepositToCharacterWarehouse = original.CanDepositToCharacterWarehouse,
            CanDepositToAccountWarehouse = original.CanDepositToAccountWarehouse,
            CanDepositToGuildWarehouse = original.CanDepositToGuildWarehouse,
            Breakable = original.Breakable,
            SoulBind = original.SoulBind,
            RemoveWhenLogout = original.RemoveWhenLogout,
            GenderPermitted = original.GenderPermitted,
            Warrior = original.Warrior,
            Scout = original.Scout,
            Mage = original.Mage,
            Cleric = original.Cleric,
            Engineer = original.Engineer,
            Artist = original.Artist,
            Fighter = original.Fighter,
            Knight = original.Knight,
            Assassin = original.Assassin,
            Ranger = original.Ranger,
            Wizard = original.Wizard,
            Elementalist = original.Elementalist,
            Chanter = original.Chanter,
            Priest = original.Priest,
            Gunner = original.Gunner,
            Bard = original.Bard,
            Rider = original.Rider,
            OptionSlotValue = original.OptionSlotValue,
            SpecialSlotValue = original.SpecialSlotValue,
            OptionSlotBonus = original.OptionSlotBonus,
            BonusApply = original.BonusApply,
            NoEnchant = original.NoEnchant,
            MaxEnchantValue = original.MaxEnchantValue,
            CannotChangeskin = original.CannotChangeskin,
            UiSoundType = original.UiSoundType,
            CashItem = original.CashItem,
            CanSplit = original.CanSplit,
            ItemDropPermitted = original.ItemDropPermitted,
            CanApExtraction = original.CanApExtraction,
            Mesh = original.Mesh,
            MeshChange = original.MeshChange,
            CanDye = original.CanDye,
            RacePermitted = original.RacePermitted,

            // 武器专用属性
            WeaponType = original.WeaponType,
            DmgDecal = original.DmgDecal,
            EquipBone = original.EquipBone,
            CombatEquipBone = original.CombatEquipBone,

            // 基础奖励属性
            BonusAttr1 = original.BonusAttr1,
            BonusAttr2 = original.BonusAttr2,
            BonusAttr3 = original.BonusAttr3,
            BonusAttr4 = original.BonusAttr4,
            BonusAttr5 = original.BonusAttr5,
            BonusAttr6 = original.BonusAttr6,
            BonusAttr7 = original.BonusAttr7,
            BonusAttr8 = original.BonusAttr8,

            // 高级奖励属性
            BonusAttrA1 = original.BonusAttrA1,
            BonusAttrA2 = original.BonusAttrA2,
            BonusAttrA3 = original.BonusAttrA3,
            BonusAttrA4 = original.BonusAttrA4,
            BonusAttrA5 = original.BonusAttrA5,
            BonusAttrA6 = original.BonusAttrA6,
            BonusAttrA7 = original.BonusAttrA7,
            BonusAttrA8 = original.BonusAttrA8,

            // 特殊效果属性
            BonusAttrB1 = original.BonusAttrB1,
            BonusAttrB2 = original.BonusAttrB2,
            BonusAttrB3 = original.BonusAttrB3,
            BonusAttrB4 = original.BonusAttrB4,

            // 战斗和强化属性
            BurnOnAttack = original.BurnOnAttack,
            BurnOnDefend = original.BurnOnDefend,
            PolishBurnOnAttack = original.PolishBurnOnAttack,
            PolishBurnOnDefend = original.PolishBurnOnDefend,
            CanProcEnchant = original.CanProcEnchant,
            CanCompositeWeapon = original.CanCompositeWeapon,
            CanPolish = original.CanPolish,
            BmRestrictCategory = original.BmRestrictCategory,
            DisassemblyItem = original.DisassemblyItem,

            // 高级属性
            DescLong = original.DescLong,
            DisposableTradeItem = original.DisposableTradeItem,
            DisposableTradeItemCount = original.DisposableTradeItemCount,
            CanPackCount = original.CanPackCount,
            VisualSlot = original.VisualSlot,
            DefaultColorM = original.DefaultColorM,
            DefaultColorF = original.DefaultColorF
        };
    }

    private void SelectAllClassesButton_Click(object sender, RoutedEventArgs e)
    {
        WarriorCheckBox.IsChecked = true;
        ScoutCheckBox.IsChecked = true;
        MageCheckBox.IsChecked = true;
        ClericCheckBox.IsChecked = true;
        EngineerCheckBox.IsChecked = true;
        ArtistCheckBox.IsChecked = true;
        FighterCheckBox.IsChecked = true;
        KnightCheckBox.IsChecked = true;
        AssassinCheckBox.IsChecked = true;
        RangerCheckBox.IsChecked = true;
        WizardCheckBox.IsChecked = true;
        ElementalistCheckBox.IsChecked = true;
        ChanterCheckBox.IsChecked = true;
        PriestCheckBox.IsChecked = true;
        GunnerCheckBox.IsChecked = true;
        BardCheckBox.IsChecked = true;
        RiderCheckBox.IsChecked = true;
    }

    private void ClearAllClassesButton_Click(object sender, RoutedEventArgs e)
    {
        WarriorCheckBox.IsChecked = false;
        ScoutCheckBox.IsChecked = false;
        MageCheckBox.IsChecked = false;
        ClericCheckBox.IsChecked = false;
        EngineerCheckBox.IsChecked = false;
        ArtistCheckBox.IsChecked = false;
        FighterCheckBox.IsChecked = false;
        KnightCheckBox.IsChecked = false;
        AssassinCheckBox.IsChecked = false;
        RangerCheckBox.IsChecked = false;
        WizardCheckBox.IsChecked = false;
        ElementalistCheckBox.IsChecked = false;
        ChanterCheckBox.IsChecked = false;
        PriestCheckBox.IsChecked = false;
        GunnerCheckBox.IsChecked = false;
        BardCheckBox.IsChecked = false;
        RiderCheckBox.IsChecked = false;
    }

    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("确定要重置所有更改吗？", "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            _editingItem = CloneItem(_originalItem);
            LoadItemData();
            DataChanged = false;
        }
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 验证数据
            if (string.IsNullOrWhiteSpace(_editingItem.Name))
            {
                MessageBox.Show("物品名称不能为空！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return;
            }

            if (_editingItem.Price < 0)
            {
                MessageBox.Show("价格不能为负数！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                PriceTextBox.Focus();
                return;
            }

            if (_editingItem.MaxStackCount < 1)
            {
                MessageBox.Show("堆叠数量必须大于0！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                MaxStackCountTextBox.Focus();
                return;
            }

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataChanged)
        {
            var result = MessageBox.Show("有未保存的更改，确定要取消吗？", "确认取消", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.No)
                return;
        }

        DialogResult = false;
        Close();
    }

    /// <summary>
    /// 添加兑换物品按钮点击事件
    /// </summary>
    private void AddTradeInItemButton_Click(object sender, RoutedEventArgs e)
    {
        _editingItem.TradeInItemList.Add(new TradeInItemData
        {
            TradeInItem = "new_item_id",
            TradeInItemCount = 1
        });

        // 刷新列表显示
        TradeInItemListBox.ItemsSource = null;
        TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;

        DataChanged = true;
    }

    /// <summary>
    /// 删除兑换物品按钮点击事件
    /// </summary>
    private void RemoveTradeInItemButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is TradeInItemData tradeInItem)
        {
            _editingItem.TradeInItemList.Remove(tradeInItem);

            // 刷新列表显示
            TradeInItemListBox.ItemsSource = null;
            TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;

            DataChanged = true;
        }
    }

    /// <summary>
    /// 更新交易摘要显示
    /// </summary>
    private void UpdateTradeSummaries()
    {
        if (_editingItem == null) return;

        try
        {
            // 更新交易成本摘要
            TradeCostSummaryTextBlock.Text = _editingItem.TradeCostSummary;
            DetailedTradeCostTextBlock.Text = _editingItem.TradeCostSummary;

            // 更新权限摘要
            PermissionSummaryTextBlock.Text = _editingItem.PermissionSummary;

            // 更新职业限制摘要
            ClassRestrictionSummaryTextBlock.Text = _editingItem.ClassRestrictionSummary;
        }
        catch (Exception ex)
        {
            // 如果更新摘要时出错，显示默认值
            TradeCostSummaryTextBlock.Text = "计算中...";
            DetailedTradeCostTextBlock.Text = "计算中...";
            PermissionSummaryTextBlock.Text = "计算中...";
            ClassRestrictionSummaryTextBlock.Text = "计算中...";
        }
    }

    /// <summary>
    /// 设置交易模式
    /// </summary>
    private void SetTradeMode()
    {
        if (_editingItem == null) return;

        // 根据当前数据判断交易模式
        bool hasPrice = _editingItem.Price > 0;
        bool hasExtraCurrency = !string.IsNullOrEmpty(_editingItem.ExtraCurrencyItem) && _editingItem.ExtraCurrencyItemCount > 0;
        bool hasTradeInItems = _editingItem.TradeInItemList != null && _editingItem.TradeInItemList.Count > 0;

        if (hasPrice && hasExtraCurrency && hasTradeInItems)
        {
            TradeModeComboBox.SelectedIndex = 3; // 复杂兑换购买
        }
        else if (hasTradeInItems)
        {
            TradeModeComboBox.SelectedIndex = 2; // 兑换购买
        }
        else if (hasPrice && hasExtraCurrency)
        {
            TradeModeComboBox.SelectedIndex = 1; // 混合货币购买
        }
        else
        {
            TradeModeComboBox.SelectedIndex = 0; // 基础基纳购买
        }

        UpdateTradeModeUI();
    }

    /// <summary>
    /// 交易模式选择变更事件
    /// </summary>
    private void TradeModeComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
    {
        UpdateTradeModeUI();
    }

    /// <summary>
    /// 更新交易模式UI
    /// </summary>
    private void UpdateTradeModeUI()
    {
        if (TradeModeComboBox.SelectedItem is not ComboBoxItem selectedItem) return;
        if (_editingItem == null) return;

        string mode = selectedItem.Tag?.ToString() ?? "basic";

        // 更新物品的交易模式
        _editingItem.TradeMode = mode;

        // 根据交易模式清理和设置字段
        switch (mode)
        {
            case "basic": // 基础基纳购买
                BasicPriceGrid.Visibility = Visibility.Visible;
                ExtraCurrencyPanel.Visibility = Visibility.Collapsed;
                TradeInItemPanel.Visibility = Visibility.Collapsed;

                // 清理不需要的字段
                _editingItem.ExtraCurrencyItem = "";
                _editingItem.ExtraCurrencyItemCount = 0;
                _editingItem.TradeInItemList?.Clear();

                // 确保有基纳价格
                if (_editingItem.Price <= 0)
                {
                    _editingItem.Price = 1;
                    TradePriceTextBox.Text = "1";
                }
                break;

            case "mixed": // 混合货币购买
                BasicPriceGrid.Visibility = Visibility.Visible;
                ExtraCurrencyPanel.Visibility = Visibility.Visible;
                TradeInItemPanel.Visibility = Visibility.Collapsed;

                // 清理兑换物品列表
                _editingItem.TradeInItemList?.Clear();

                // 确保有基纳价格和额外货币
                if (_editingItem.Price <= 0)
                {
                    _editingItem.Price = 1;
                    TradePriceTextBox.Text = "1";
                }
                if (string.IsNullOrEmpty(_editingItem.ExtraCurrencyItem))
                {
                    _editingItem.ExtraCurrencyItem = "coin_combineskill_01";
                    ExtraCurrencyItemComboBox.Text = "coin_combineskill_01";
                }
                if (_editingItem.ExtraCurrencyItemCount <= 0)
                {
                    _editingItem.ExtraCurrencyItemCount = 1;
                    ExtraCurrencyItemCountTextBox.Text = "1";
                }
                break;

            case "exchange": // 兑换购买
                BasicPriceGrid.Visibility = Visibility.Collapsed;
                ExtraCurrencyPanel.Visibility = Visibility.Collapsed;
                TradeInItemPanel.Visibility = Visibility.Visible;

                // 清理基纳和额外货币
                _editingItem.Price = 0;
                _editingItem.ExtraCurrencyItem = "";
                _editingItem.ExtraCurrencyItemCount = 0;
                TradePriceTextBox.Text = "0";

                // 确保有兑换物品
                if (_editingItem.TradeInItemList == null || _editingItem.TradeInItemList.Count == 0)
                {
                    _editingItem.TradeInItemList = new List<TradeInItemData>
                    {
                        new TradeInItemData { TradeInItem = "black_aion_toll_31", TradeInItemCount = 1 }
                    };
                    TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;
                }
                break;

            case "complex": // 复杂兑换购买
                BasicPriceGrid.Visibility = Visibility.Visible;
                ExtraCurrencyPanel.Visibility = Visibility.Visible;
                TradeInItemPanel.Visibility = Visibility.Visible;

                // 确保所有字段都有值
                if (_editingItem.Price <= 0)
                {
                    _editingItem.Price = 1;
                    TradePriceTextBox.Text = "1";
                }
                if (string.IsNullOrEmpty(_editingItem.ExtraCurrencyItem))
                {
                    _editingItem.ExtraCurrencyItem = "coin_combineskill_01";
                    ExtraCurrencyItemComboBox.Text = "coin_combineskill_01";
                }
                if (_editingItem.ExtraCurrencyItemCount <= 0)
                {
                    _editingItem.ExtraCurrencyItemCount = 1;
                    ExtraCurrencyItemCountTextBox.Text = "1";
                }
                if (_editingItem.TradeInItemList == null || _editingItem.TradeInItemList.Count == 0)
                {
                    _editingItem.TradeInItemList = new List<TradeInItemData>
                    {
                        new TradeInItemData { TradeInItem = "black_aion_toll_31", TradeInItemCount = 1 }
                    };
                    TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;
                }
                break;
        }

        // 标记数据已更改
        DataChanged = true;

        UpdateTradeModeExample(mode);
        UpdateTradeSummaries();
    }

    /// <summary>
    /// 更新交易模式示例
    /// </summary>
    private void UpdateTradeModeExample(string mode)
    {
        string example = mode switch
        {
            "basic" => @"<!-- 模式1: 基础基纳购买 -->
<item>
    <id>141000001</id>
    <name>stigma_shard</name>
    <price>10</price>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>TRUE</can_sell_to_npc>
    <item_drop_permitted>FALSE</item_drop_permitted>
</item>",

            "mixed" => @"<!-- 模式2: 混合货币购买 -->
<item>
    <id>123456789</id>
    <name>skill_combine_item</name>
    <price>1000</price>
    <extra_currency_item>coin_combineskill_01</extra_currency_item>
    <extra_currency_item_count>2</extra_currency_item_count>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>",

            "exchange" => @"<!-- 模式3: 兑换购买 -->
<item>
    <id>987654321</id>
    <name>premium_item</name>
    <trade_in_item_list>
        <data>
            <trade_in_item>black_aion_toll_31</trade_in_item>
            <trade_in_item_count>10000</trade_in_item_count>
        </data>
    </trade_in_item_list>
    <can_exchange>FALSE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>",

            "complex" => @"<!-- 模式4: 复杂兑换购买 -->
<item>
    <id>555666777</id>
    <name>multi_currency_item</name>
    <price>500</price>
    <extra_currency_item>black_aion_toll_01</extra_currency_item>
    <extra_currency_item_count>1</extra_currency_item_count>
    <trade_in_item_list>
        <data>
            <trade_in_item>black_aion_toll_31</trade_in_item>
            <trade_in_item_count>100</trade_in_item_count>
        </data>
    </trade_in_item_list>
    <can_exchange>TRUE</can_exchange>
    <can_sell_to_npc>FALSE</can_sell_to_npc>
</item>",

            _ => "未知模式"
        };

        TradeModeExampleTextBlock.Text = example;
    }

    /// <summary>
    /// 添加常用兑换物品按钮点击事件
    /// </summary>
    private void AddCommonTradeItemButton_Click(object sender, RoutedEventArgs e)
    {
        var commonItems = new[]
        {
            new TradeInItemData { TradeInItem = "black_aion_toll_31", TradeInItemCount = 100 },
            new TradeInItemData { TradeInItem = "COIN_01", TradeInItemCount = 15 },
            new TradeInItemData { TradeInItem = "COIN_02", TradeInItemCount = 10 },
            new TradeInItemData { TradeInItem = "COIN_03", TradeInItemCount = 5 }
        };

        foreach (var item in commonItems)
        {
            _editingItem.TradeInItemList.Add(item);
        }

        // 刷新列表显示
        TradeInItemListBox.ItemsSource = null;
        TradeInItemListBox.ItemsSource = _editingItem.TradeInItemList;

        DataChanged = true;
        UpdateTradeSummaries();
    }
}
