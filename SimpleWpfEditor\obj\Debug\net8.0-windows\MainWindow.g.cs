﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0E637E02126C14FA0167128211FE209B481CC856"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TitleManagerButton;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SkillManagerButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NpcManagerButton;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ServerMonsterButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NpcShopManagerButton;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NpcTradeManagerButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveFileButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveWithServerConversionButton;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearModifiedButton;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddItemButton;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteItemButton;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GlobalSearchButton;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemCountText;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EditTitleText;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EditPanel;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedNameText;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescKeyText;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IdTextBox;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ArmorTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxStackCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WarriorCheckBox;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ScoutCheckBox;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MageCheckBox;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ClericCheckBox;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox FighterCheckBox;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KnightCheckBox;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AssassinCheckBox;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RangerCheckBox;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WizardCheckBox;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ElementalistCheckBox;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChanterCheckBox;
        
        #line default
        #line hidden
        
        
        #line 417 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PriestCheckBox;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox GunnerCheckBox;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BardCheckBox;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RiderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AllowAllClassesButton;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DenyAllClassesButton;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 446 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveChangesButton;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\MainWindow.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TitleManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\MainWindow.xaml"
            this.TitleManagerButton.Click += new System.Windows.RoutedEventHandler(this.TitleManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SkillManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\MainWindow.xaml"
            this.SkillManagerButton.Click += new System.Windows.RoutedEventHandler(this.SkillManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.NpcManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\MainWindow.xaml"
            this.NpcManagerButton.Click += new System.Windows.RoutedEventHandler(this.NpcManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ServerMonsterButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\MainWindow.xaml"
            this.ServerMonsterButton.Click += new System.Windows.RoutedEventHandler(this.ServerMonsterButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.NpcShopManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\MainWindow.xaml"
            this.NpcShopManagerButton.Click += new System.Windows.RoutedEventHandler(this.NpcShopManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.NpcTradeManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\MainWindow.xaml"
            this.NpcTradeManagerButton.Click += new System.Windows.RoutedEventHandler(this.NpcTradeManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SaveFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\MainWindow.xaml"
            this.SaveFileButton.Click += new System.Windows.RoutedEventHandler(this.SaveFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SaveWithServerConversionButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\MainWindow.xaml"
            this.SaveWithServerConversionButton.Click += new System.Windows.RoutedEventHandler(this.SaveWithServerConversionButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ClearModifiedButton = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\MainWindow.xaml"
            this.ClearModifiedButton.Click += new System.Windows.RoutedEventHandler(this.ClearModifiedButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.AddItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\MainWindow.xaml"
            this.AddItemButton.Click += new System.Windows.RoutedEventHandler(this.AddItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DeleteItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\MainWindow.xaml"
            this.DeleteItemButton.Click += new System.Windows.RoutedEventHandler(this.DeleteItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.GlobalSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\MainWindow.xaml"
            this.GlobalSearchButton.Click += new System.Windows.RoutedEventHandler(this.GlobalSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ItemCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 235 "..\..\..\MainWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 248 "..\..\..\MainWindow.xaml"
            this.ItemsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ItemsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 249 "..\..\..\MainWindow.xaml"
            this.ItemsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.ItemsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 261 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 266 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 272 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteItemMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.EditTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.EditPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.LocalizedNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.DescKeyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.IdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.ArmorTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 29:
            this.PriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.MaxStackCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.WarriorCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.ScoutCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.MageCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.ClericCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.FighterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 36:
            this.KnightCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 37:
            this.AssassinCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 38:
            this.RangerCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 39:
            this.WizardCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 40:
            this.ElementalistCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.ChanterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.PriestCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.GunnerCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.BardCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 45:
            this.RiderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 46:
            this.AllowAllClassesButton = ((System.Windows.Controls.Button)(target));
            
            #line 428 "..\..\..\MainWindow.xaml"
            this.AllowAllClassesButton.Click += new System.Windows.RoutedEventHandler(this.AllowAllClassesButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.DenyAllClassesButton = ((System.Windows.Controls.Button)(target));
            
            #line 433 "..\..\..\MainWindow.xaml"
            this.DenyAllClassesButton.Click += new System.Windows.RoutedEventHandler(this.DenyAllClassesButton_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 444 "..\..\..\MainWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            this.SaveChangesButton = ((System.Windows.Controls.Button)(target));
            
            #line 450 "..\..\..\MainWindow.xaml"
            this.SaveChangesButton.Click += new System.Windows.RoutedEventHandler(this.SaveChangesButton_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

