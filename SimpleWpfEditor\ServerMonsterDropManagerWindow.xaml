<Window x:Class="SimpleWpfEditor.ServerMonsterDropManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="服务端怪物掉落管理器" Height="950" Width="1600"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- 样式定义 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SearchTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="LoadButton" Content="📁 加载文件" Style="{StaticResource ModernButton}" Click="LoadButton_Click"/>
                    <Button x:Name="SaveButton" Content="💾 保存文件" Style="{StaticResource ModernButton}" Click="SaveButton_Click"/>
                    <Button x:Name="AddButton" Content="➕ 添加怪物" Style="{StaticResource ModernButton}" Click="AddButton_Click"/>
                    <Button x:Name="DeleteButton" Content="🗑️ 删除怪物" Style="{StaticResource ModernButton}" Click="DeleteButton_Click"/>
                    <Button x:Name="CopyButton" Content="📋 复制怪物" Style="{StaticResource ModernButton}" Click="CopyButton_Click"/>
                </StackPanel>

                <!-- 搜索框 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="🔍" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="16"/>
                    <TextBox x:Name="SearchTextBox" Width="300" Style="{StaticResource SearchTextBox}"
                             Text="搜索怪物..." GotFocus="SearchTextBox_GotFocus" 
                             LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 怪物列表 -->
            <Border Grid.Column="0" Background="White" Margin="8" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 列表标题 -->
                    <Border Grid.Row="0" Background="#FF5722" CornerRadius="8,8,0,0" Padding="16,12">
                        <TextBlock Text="👹 服务端怪物列表" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    </Border>

                    <!-- 怪物列表 -->
                    <ListBox Grid.Row="1" x:Name="MonsterListBox" SelectionChanged="MonsterListBox_SelectionChanged"
                             BorderThickness="0" Padding="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="6" Padding="12" Margin="0,2">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=ListBoxItem}}" Value="True">
                                                    <Setter Property="Background" Value="#FFEBEE"/>
                                                    <Setter Property="BorderBrush" Value="#FF5722"/>
                                                    <Setter Property="BorderThickness" Value="2"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                        <TextBlock Text="{Binding Id, StringFormat='ID: {0}'}" FontSize="11" Foreground="#666" Margin="0,2,0,0"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                            <Border Background="#4CAF50" CornerRadius="10" Padding="6,2" Margin="0,0,4,0">
                                                <TextBlock Text="{Binding Level, StringFormat='Lv.{0}'}" Foreground="White" FontSize="10"/>
                                            </Border>
                                            <Border Background="#FF9800" CornerRadius="10" Padding="6,2" Margin="0,0,4,0">
                                                <TextBlock Text="{Binding MaxHp, StringFormat='{}{0} HP'}" Foreground="White" FontSize="10"/>
                                            </Border>
                                            <Border Background="#9C27B0" CornerRadius="10" Padding="6,2">
                                                <TextBlock Text="{Binding DiskType}" Foreground="White" FontSize="10"/>
                                            </Border>
                                        </StackPanel>
                                        <TextBlock Text="{Binding Tribe}" FontSize="10" Foreground="#999" Margin="0,2,0,0"/>
                                        <TextBlock Text="{Binding SpawnZoneName, StringFormat='{}区域: {0}'}" FontSize="10" Foreground="#999" Margin="0,1,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="#E0E0E0"/>

            <!-- 怪物详情和掉落编辑区域 -->
            <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto" Margin="8">
                <StackPanel x:Name="DetailsPanel" IsEnabled="False">
                    
                    <!-- 基础信息 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="📋 基础信息" Style="{StaticResource SectionHeader}"/>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="ID:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="IdTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="NameTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="描述:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DescTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="等级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="LevelTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="最大HP:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MaxHpTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="种族:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TribeTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="区域:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="SpawnZoneTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 金币掉落 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="💰 金币掉落设置" Style="{StaticResource SectionHeader}"/>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="最小金币数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MinCashTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="最大金币数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MaxCashTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="金币掉落概率:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="CashDropProbTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 掉落组设置 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🎲 掉落组设置" Style="{StaticResource SectionHeader}"/>
                                <Button Grid.Column="1" x:Name="AddDropGroupButton" Content="➕ 添加掉落组" 
                                        Style="{StaticResource ModernButton}" Background="#4CAF50" Click="AddDropGroupButton_Click"/>
                            </Grid>

                            <DataGrid x:Name="DropGroupsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" 
                                      GridLinesVisibility="Horizontal" HeadersVisibility="Column" Height="150">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="最小掉落数" Binding="{Binding DropGroupMin}" Width="120"/>
                                    <DataGridTextColumn Header="最大掉落数" Binding="{Binding DropGroupMax}" Width="120"/>
                                    <DataGridTemplateColumn Header="操作" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="删除" Background="#F44336" Foreground="White" 
                                                        BorderThickness="0" Padding="8,4" Click="DeleteDropGroupButton_Click"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- 掉落物品设置 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🎁 掉落物品设置" Style="{StaticResource SectionHeader}"/>
                                <Button Grid.Column="1" x:Name="AddDropItemButton" Content="➕ 添加掉落物品" 
                                        Style="{StaticResource ModernButton}" Background="#FF9800" Click="AddDropItemButton_Click"/>
                            </Grid>

                            <DataGrid x:Name="DropItemsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      GridLinesVisibility="Horizontal" HeadersVisibility="Column" Height="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="物品名称" Binding="{Binding Item}" Width="150"/>
                                    <DataGridTextColumn Header="物品ID" Binding="{Binding ItemId}" Width="80"/>
                                    <DataGridTextColumn Header="掉落概率" Binding="{Binding Prob}" Width="100"/>
                                    <DataGridTextColumn Header="物品数量" Binding="{Binding ItemCount}" Width="80"/>
                                    <DataGridTextColumn Header="最小数量" Binding="{Binding MinCount}" Width="80"/>
                                    <DataGridTextColumn Header="最大数量" Binding="{Binding MaxCount}" Width="80"/>
                                    <DataGridTextColumn Header="掉落属性" Binding="{Binding DropAttribute}" Width="80"/>
                                    <DataGridTextColumn Header="掉落组" Binding="{Binding DropGroupIdx}" Width="60"/>
                                    <DataGridTemplateColumn Header="操作" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="删除" Background="#F44336" Foreground="White"
                                                        BorderThickness="0" Padding="8,4" Click="DeleteDropItemButton_Click"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- 通用掉落设置 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <materialDesign:PackIcon Kind="Gift" Margin="0,0,8,0"/>
                                <TextBlock Text="通用掉落设置" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                                <Button x:Name="AddCommonDropButton" Content="添加通用掉落" Style="{StaticResource ModernButton}"
                                        Background="#4CAF50" Margin="16,0,0,0" Click="AddCommonDropButton_Click"/>
                            </StackPanel>

                            <DataGrid x:Name="CommonDropsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      GridLinesVisibility="Horizontal" HeadersVisibility="Column" Height="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="通用掉落名称" Binding="{Binding CommonDrop}" Width="200"/>
                                    <DataGridTextColumn Header="掉落调整" Binding="{Binding CommonDropAdjustment}" Width="100"/>
                                    <DataGridTextColumn Header="掉落组索引" Binding="{Binding CommonDropGroupIdx}" Width="100"/>
                                    <DataGridTemplateColumn Header="操作" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="删除" Background="#F44336" Foreground="White"
                                                        BorderThickness="0" Padding="8,4" Click="DeleteCommonDropButton_Click"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- 操作按钮 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button x:Name="SaveMonsterButton" Content="💾 保存修改" Style="{StaticResource ModernButton}" Click="SaveMonsterButton_Click"/>
                            <Button x:Name="CancelButton" Content="❌ 取消修改" Style="{StaticResource ModernButton}" 
                                    Background="#F44336" Click="CancelButton_Click"/>
                            <Button x:Name="PreviewDropButton" Content="👁️ 预览掉落" Style="{StaticResource ModernButton}" 
                                    Background="#9C27B0" Click="PreviewDropButton_Click"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="就绪" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" x:Name="CountTextBlock" Text="怪物数量: 0" VerticalAlignment="Center" FontWeight="Medium"/>
            </Grid>
        </Border>
    </Grid>
</Window>
