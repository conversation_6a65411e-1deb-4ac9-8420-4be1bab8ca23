﻿#pragma checksum "..\..\..\TitleEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D8EBC98438DB6AA7C26A059FDD707DEEFCF321D1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// TitleEditorWindow
    /// </summary>
    public partial class TitleEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleIdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleDescTextBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedDescTextBlock;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TitleRaceComboBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitlePriorityTextBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleLocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedLocationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddBonusButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveBonusButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox BonusAttrsListBox;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\TitleEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/titleeditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\TitleEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TitleIdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.IdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.LocalizedNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TitleDescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.LocalizedDescTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TitleRaceComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.TitleTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.TitlePriorityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TitleLocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.LocalizedLocationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.AddBonusButton = ((System.Windows.Controls.Button)(target));
            return;
            case 15:
            this.RemoveBonusButton = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.BonusAttrsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 17:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            return;
            case 19:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            return;
            case 20:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

