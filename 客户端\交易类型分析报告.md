# 物品交易类型分析报告

## 分析范围
- client_items_armor.xml (防具物品)
- client_items_etc.xml (其他物品)  
- client_items_misc.xml (杂项物品)

## 发现的交易类型字段

### 1. 基础价格字段
- **`<price>`** - 物品的基础价格（基纳）
  - 示例：`<price>5</price>`, `<price>850</price>`, `<price>1274950</price>`
  - 这是最常见的交易货币类型

### 2. 额外货币字段
- **`<extra_currency_item>`** - 额外需要的货币物品ID
- **`<extra_currency_item_count>`** - 额外货币物品的数量

#### 发现的额外货币类型：

#### 2.1 技能合成币
- **`coin_combineskill_01`** - 技能合成币
  - 数量：2, 4, 6, 8, 10, 16, 24个不等
  - 用途：用于技能相关的物品合成

#### 2.2 活动币
- **`event_chaos_wingwar_coin_02`** - 混沌翼战活动币
  - 数量：2个
  - 用途：活动相关物品兑换

#### 2.3 任务材料币
- **`junk_master_recipe_quest_40a`** - 40级任务配方材料
  - 数量：60, 180个
- **`junk_master_recipe_quest_50a`** - 50级任务配方材料
  - 数量：具体数量需要进一步确认

### 3. 交易兑换字段
- **`<trade_in_item_list>`** - 交易兑换物品列表
- **`<trade_in_item>`** - 可兑换的物品ID
- **`<trade_in_item_count>`** - 兑换所需数量

#### 发现的兑换币类型：
- **`COIN_01`** - 一级币（需要15个）
- **`COIN_02`** - 二级币（需要10个）  
- **`COIN_03`** - 三级币（需要5个）

### 4. 物品类型字段
- **`<item_type>`** - 物品类型
  - 示例：`<item_type>normal</item_type>`
  - 影响交易方式和限制

### 5. 掉落权限字段
- **`<item_drop_permitted>`** - 是否允许掉落
  - 示例：`<item_drop_permitted>FALSE</item_drop_permitted>`
  - 影响物品的交易流通性

## 交易类型总结

### 主要货币类型：
1. **基纳 (Kinah)** - 通过 `<price>` 字段表示，是游戏的主要货币
2. **技能合成币** - 用于技能相关物品的合成
3. **活动币** - 特定活动期间使用的特殊货币
4. **任务材料币** - 完成特定任务所需的材料货币
5. **等级币** - 分为三个等级（COIN_01, COIN_02, COIN_03）

### 交易方式：
1. **直接购买** - 使用基纳购买
2. **混合购买** - 基纳 + 额外货币
3. **兑换购买** - 使用特定物品兑换
4. **任务获得** - 通过完成任务获得

### 字段结构示例：
```xml
<item>
    <price>1000</price>                           <!-- 基纳价格 -->
    <extra_currency_item>coin_combineskill_01</extra_currency_item>  <!-- 额外货币 -->
    <extra_currency_item_count>2</extra_currency_item_count>         <!-- 额外货币数量 -->
    <trade_in_item_list>                          <!-- 兑换列表 -->
        <trade_in_item>COIN_01</trade_in_item>
        <trade_in_item_count>15</trade_in_item_count>
    </trade_in_item_list>
</item>
```

## 注意事项
- 大部分物品使用基纳作为主要交易货币
- 特殊物品可能需要额外的货币或材料
- 某些物品有交易限制（如不允许掉落）
- 不同等级的币种有不同的兑换比例 