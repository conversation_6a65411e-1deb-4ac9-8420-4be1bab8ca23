﻿<?xml version="1.0" encoding="utf-8"?>
<item_etc>
	<item_templates>
		<item_template>
			<id>*********</id>
			<name>healing_potion_major</name>
			<desc>STR_healing_potion_major</desc>
			<item_type>consumable</item_type>
			<material>liquid</material>
			<price>5000</price>
			<max_stack_count>200</max_stack_count>
			<quality>common</quality>
			<level>1</level>
			<lore>FALSE</lore>
			<can_exchange>TRUE</can_exchange>
			<can_sell_to_npc>TRUE</can_sell_to_npc>
			<can_deposit_to_character_warehouse>TRUE</can_deposit_to_character_warehouse>
			<can_deposit_to_account_warehouse>TRUE</can_deposit_to_account_warehouse>
			<can_deposit_to_guild_warehouse>TRUE</can_deposit_to_guild_warehouse>
			<item_drop_permitted>TRUE</item_drop_permitted>
			<breakable>FALSE</breakable>
			<soul_bind>FALSE</soul_bind>
			<remove_when_logout>FALSE</remove_when_logout>
			<race_permitted>pc_light pc_dark</race_permitted>
			<gender_permitted>all</gender_permitted>
			<warrior>1</warrior>
			<scout>1</scout>
			<mage>1</mage>
			<cleric>1</cleric>
			<engineer>1</engineer>
			<artist>1</artist>
			<fighter>1</fighter>
			<knight>1</knight>
			<assassin>1</assassin>
			<ranger>1</ranger>
			<wizard>1</wizard>
			<elementalist>1</elementalist>
			<chanter>1</chanter>
			<priest>1</priest>
			<gunner>1</gunner>
			<bard>1</bard>
			<rider>1</rider>
			<bonus_apply>equip</bonus_apply>
		</item_template>
		<item_template>
			<id>*********</id>
			<name>premium_mixed_currency_item</name>
			<desc>STR_premium_mixed_currency_item</desc>
			<item_type>cash</item_type>
			<material>special</material>
			<price>1000</price>
			<max_stack_count>1</max_stack_count>
			<quality>unique</quality>
			<level>50</level>
			<lore>FALSE</lore>
			<can_exchange>FALSE</can_exchange>
			<can_sell_to_npc>FALSE</can_sell_to_npc>
			<can_deposit_to_character_warehouse>TRUE</can_deposit_to_character_warehouse>
			<can_deposit_to_account_warehouse>TRUE</can_deposit_to_account_warehouse>
			<can_deposit_to_guild_warehouse>TRUE</can_deposit_to_guild_warehouse>
			<item_drop_permitted>FALSE</item_drop_permitted>
			<breakable>TRUE</breakable>
			<soul_bind>FALSE</soul_bind>
			<remove_when_logout>FALSE</remove_when_logout>
			<race_permitted>pc_light pc_dark</race_permitted>
			<gender_permitted>all</gender_permitted>
			<warrior>1</warrior>
			<scout>1</scout>
			<mage>1</mage>
			<cleric>1</cleric>
			<engineer>1</engineer>
			<artist>1</artist>
			<fighter>1</fighter>
			<knight>1</knight>
			<assassin>1</assassin>
			<ranger>1</ranger>
			<wizard>1</wizard>
			<elementalist>1</elementalist>
			<chanter>1</chanter>
			<priest>1</priest>
			<gunner>1</gunner>
			<bard>1</bard>
			<rider>1</rider>
			<bonus_apply>equip</bonus_apply>
			<extra_currency_item>coin_combineskill_01</extra_currency_item>
			<extra_currency_item_count>5</extra_currency_item_count>
			<trade_in_item_list>
				<data>
					<trade_in_item>black_aion_toll_31</trade_in_item>
					<trade_in_item_count>100</trade_in_item_count>
				</data>
				<data>
					<trade_in_item>COIN_01</trade_in_item>
					<trade_in_item_count>15</trade_in_item_count>
				</data>
			</trade_in_item_list>
			<activation_skill>cash_food_l_shape_herlock_phy_01</activation_skill>
			<activation_mode>Both</activation_mode>
			<extra_inventory>10</extra_inventory>
			<disassembly_item>123456</disassembly_item>
		</item_template>
	</item_templates>
</item_etc>