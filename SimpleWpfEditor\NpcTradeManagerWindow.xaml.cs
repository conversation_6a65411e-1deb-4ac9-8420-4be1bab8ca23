using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor;

/// <summary>
/// NPC交易管理器窗口
/// </summary>
public partial class NpcTradeManagerWindow : Window
{
    private readonly ILogger<NpcTradeManagerWindow> _logger;
    private readonly NpcTradeInListXmlService _xmlService;
    private readonly StringService _stringService;
    private ObservableCollection<ClientNpcTradeInList> _tradeLists;
    private CollectionViewSource _filteredTradeLists;
    private string? _currentFilePath;
    private NpcTradeInListStatistics? _statistics;

    public NpcTradeManagerWindow(ILogger<NpcTradeManagerWindow> logger, NpcTradeInListXmlService xmlService, StringService stringService)
    {
        InitializeComponent();
        
        _logger = logger;
        _xmlService = xmlService;
        _stringService = stringService;
        _tradeLists = new ObservableCollection<ClientNpcTradeInList>();
        _filteredTradeLists = new CollectionViewSource { Source = _tradeLists };
        
        InitializeUI();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        TradeListsDataGrid.ItemsSource = _filteredTradeLists.View;
        
        // 设置筛选
        _filteredTradeLists.Filter += FilterTradeLists;
        
        // 初始化筛选下拉框
        InitializeFilterComboBoxes();
    }

    /// <summary>
    /// 初始化筛选下拉框
    /// </summary>
    private void InitializeFilterComboBoxes()
    {
        // 交易类型筛选
        TradeTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 武器类型筛选
        WeaponTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 阵营筛选
        FactionFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        
        // 默认选择"全部"
        TradeTypeFilterComboBox.SelectedIndex = 0;
        WeaponTypeFilterComboBox.SelectedIndex = 0;
        FactionFilterComboBox.SelectedIndex = 0;
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 工具栏按钮事件
        OpenFileButton.Click += OpenFileButton_Click;
        SaveFileButton.Click += SaveFileButton_Click;
        
        // 搜索和筛选事件
        SearchTextBox.TextChanged += (s, e) => _filteredTradeLists.View.Refresh();
        TradeTypeFilterComboBox.SelectionChanged += (s, e) => _filteredTradeLists.View.Refresh();
        WeaponTypeFilterComboBox.SelectionChanged += (s, e) => _filteredTradeLists.View.Refresh();
        FactionFilterComboBox.SelectionChanged += (s, e) => _filteredTradeLists.View.Refresh();
        ClearFilterButton.Click += ClearFilterButton_Click;
        
        // 数据网格事件
        TradeListsDataGrid.MouseDoubleClick += TradeListsDataGrid_MouseDoubleClick;
        TradeListsDataGrid.KeyDown += TradeListsDataGrid_KeyDown;
    }

    /// <summary>
    /// 打开文件按钮点击
    /// </summary>
    private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择NPC交易列表文件",
                Filter = "NPC交易列表文件 (client_npc_trade_in_list.xml)|client_npc_trade_in_list.xml|XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadTradeListsFile(openFileDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开文件时出错");
            MessageBox.Show($"打开文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 保存文件按钮点击
    /// </summary>
    private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存NPC交易列表文件",
                    Filter = "NPC交易列表文件 (client_npc_trade_in_list.xml)|client_npc_trade_in_list.xml|XML文件 (*.xml)|*.xml",
                    DefaultExt = "xml",
                    FileName = "client_npc_trade_in_list.xml"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _currentFilePath = saveFileDialog.FileName;
                }
                else
                {
                    return;
                }
            }

            await SaveTradeListsFile(_currentFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件时出错");
            MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 清除筛选按钮点击
    /// </summary>
    private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Text = string.Empty;
        TradeTypeFilterComboBox.SelectedIndex = 0;
        WeaponTypeFilterComboBox.SelectedIndex = 0;
        FactionFilterComboBox.SelectedIndex = 0;
        _filteredTradeLists.View.Refresh();
        UpdateStatus("已清除所有筛选条件");
    }

    /// <summary>
    /// 数据网格双击事件
    /// </summary>
    private void TradeListsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (TradeListsDataGrid.SelectedItem is ClientNpcTradeInList selectedTradeList)
        {
            OpenTradeListEditor(selectedTradeList);
        }
    }

    /// <summary>
    /// 数据网格键盘事件
    /// </summary>
    private void TradeListsDataGrid_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && TradeListsDataGrid.SelectedItem is ClientNpcTradeInList selectedTradeList)
        {
            OpenTradeListEditor(selectedTradeList);
            e.Handled = true;
        }
    }

    /// <summary>
    /// 加载交易列表文件
    /// </summary>
    private async Task LoadTradeListsFile(string filePath)
    {
        try
        {
            UpdateStatus("正在加载NPC交易列表文件...");
            
            var tradeLists = await _xmlService.LoadNpcTradeInListsAsync(filePath);
            
            _tradeLists.Clear();
            foreach (var tradeList in tradeLists)
            {
                _tradeLists.Add(tradeList);
            }
            
            _currentFilePath = filePath;
            _statistics = _xmlService.GetStatistics(tradeLists);
            
            UpdateFilterComboBoxes();
            UpdateStatistics();
            UpdateStatus($"成功加载 {tradeLists.Count} 个NPC交易列表");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载NPC交易列表文件时出错: {FilePath}", filePath);
            UpdateStatus($"加载文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 保存交易列表文件
    /// </summary>
    private async Task SaveTradeListsFile(string filePath)
    {
        try
        {
            UpdateStatus("正在保存NPC交易列表文件...");
            
            await _xmlService.SaveNpcTradeInListsAsync(filePath, _tradeLists.ToList());
            
            UpdateStatus($"成功保存 {_tradeLists.Count} 个NPC交易列表");
            FilePathTextBlock.Text = Path.GetFileName(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存NPC交易列表文件时出错: {FilePath}", filePath);
            UpdateStatus($"保存文件失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 打开交易列表编辑器
    /// </summary>
    private void OpenTradeListEditor(ClientNpcTradeInList tradeList)
    {
        try
        {
            var tradeListEditor = new NpcTradeEditorWindow(tradeList, _stringService)
            {
                Owner = this
            };

            var result = tradeListEditor.ShowDialog();
            if (result == true && tradeListEditor.DataChanged)
            {
                // 更新原始数据
                UpdateTradeListData(tradeList, tradeListEditor.EditedTradeList);
                
                // 刷新显示
                _filteredTradeLists.View.Refresh();
                
                UpdateStatus($"已更新交易列表: {tradeList.DisplayName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开交易列表编辑器时出错");
            MessageBox.Show($"打开编辑器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 更新交易列表数据
    /// </summary>
    private void UpdateTradeListData(ClientNpcTradeInList original, ClientNpcTradeInList edited)
    {
        original.Name = edited.Name;
        original.Desc = edited.Desc;
        original.UseCategory = edited.UseCategory;
        original.GoodsList = edited.GoodsList.ToList();
    }

    /// <summary>
    /// 更新筛选下拉框
    /// </summary>
    private void UpdateFilterComboBoxes()
    {
        if (_statistics == null) return;

        // 更新交易类型筛选
        var currentTradeType = GetComboBoxValue(TradeTypeFilterComboBox);
        TradeTypeFilterComboBox.Items.Clear();
        TradeTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var tradeType in _statistics.TradeTypes.Keys.OrderBy(x => x))
        {
            TradeTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{tradeType} ({_statistics.TradeTypes[tradeType]})", Tag = tradeType });
        }
        SetComboBoxValue(TradeTypeFilterComboBox, currentTradeType);

        // 更新武器类型筛选
        var currentWeaponType = GetComboBoxValue(WeaponTypeFilterComboBox);
        WeaponTypeFilterComboBox.Items.Clear();
        WeaponTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var weaponType in _statistics.WeaponTypes.Keys.OrderBy(x => x))
        {
            WeaponTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{weaponType} ({_statistics.WeaponTypes[weaponType]})", Tag = weaponType });
        }
        SetComboBoxValue(WeaponTypeFilterComboBox, currentWeaponType);

        // 更新阵营筛选
        var currentFaction = GetComboBoxValue(FactionFilterComboBox);
        FactionFilterComboBox.Items.Clear();
        FactionFilterComboBox.Items.Add(new ComboBoxItem { Content = "全部", Tag = "" });
        foreach (var faction in _statistics.FactionTypes.Keys.OrderBy(x => x))
        {
            FactionFilterComboBox.Items.Add(new ComboBoxItem { Content = $"{faction} ({_statistics.FactionTypes[faction]})", Tag = faction });
        }
        SetComboBoxValue(FactionFilterComboBox, currentFaction);
    }

    /// <summary>
    /// 筛选交易列表
    /// </summary>
    private void FilterTradeLists(object sender, FilterEventArgs e)
    {
        if (e.Item is not ClientNpcTradeInList tradeList)
        {
            e.Accepted = false;
            return;
        }

        // 搜索文本筛选
        var searchText = SearchTextBox.Text?.Trim().ToLower();
        if (!string.IsNullOrEmpty(searchText))
        {
            var matchesSearch = tradeList.Id.ToString().Contains(searchText) ||
                               tradeList.Name.ToLower().Contains(searchText) ||
                               tradeList.Desc.ToLower().Contains(searchText) ||
                               tradeList.DisplayName.ToLower().Contains(searchText);
            
            if (!matchesSearch)
            {
                e.Accepted = false;
                return;
            }
        }

        // 交易类型筛选
        var tradeTypeFilter = GetComboBoxValue(TradeTypeFilterComboBox);
        if (!string.IsNullOrEmpty(tradeTypeFilter) && tradeList.TradeType != tradeTypeFilter)
        {
            e.Accepted = false;
            return;
        }

        // 武器类型筛选
        var weaponTypeFilter = GetComboBoxValue(WeaponTypeFilterComboBox);
        if (!string.IsNullOrEmpty(weaponTypeFilter) && tradeList.WeaponType != weaponTypeFilter)
        {
            e.Accepted = false;
            return;
        }

        // 阵营筛选
        var factionFilter = GetComboBoxValue(FactionFilterComboBox);
        if (!string.IsNullOrEmpty(factionFilter) && tradeList.FactionType != factionFilter)
        {
            e.Accepted = false;
            return;
        }

        e.Accepted = true;
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        if (_statistics == null) return;
        
        TotalTradeListsTextBlock.Text = $"总交易: {_statistics.TotalTradeLists}";
        TotalItemsTextBlock.Text = $"总商品: {_statistics.TotalItems}";
        AvgItemsTextBlock.Text = $"平均: {_statistics.AverageItemsPerList:F1}";
        
        // 更新筛选结果数量
        var filteredCount = _filteredTradeLists.View.Cast<object>().Count();
        FilteredCountTextBlock.Text = $"筛选结果: {filteredCount}";
    }

    /// <summary>
    /// 获取下拉框值
    /// </summary>
    private string GetComboBoxValue(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
        {
            return item.Tag.ToString() ?? "";
        }
        return "";
    }

    /// <summary>
    /// 设置下拉框值
    /// </summary>
    private void SetComboBoxValue(ComboBox comboBox, string value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Tag?.ToString() == value)
            {
                comboBox.SelectedItem = item;
                return;
            }
        }
        comboBox.SelectedIndex = 0; // 默认选择"全部"
    }

    /// <summary>
    /// 更新状态
    /// </summary>
    private void UpdateStatus(string message)
    {
        StatusTextBlock.Text = message;
        _logger.LogInformation(message);
        
        // 更新筛选结果数量
        if (_filteredTradeLists?.View != null)
        {
            var filteredCount = _filteredTradeLists.View.Cast<object>().Count();
            FilteredCountTextBlock.Text = $"筛选结果: {filteredCount}";
        }
    }
}
