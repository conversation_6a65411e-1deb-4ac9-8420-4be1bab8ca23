using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AionItemEditor.Models;
using Microsoft.Extensions.Logging;

namespace AionItemEditor.ViewModels;

/// <summary>
/// 物品编辑ViewModel
/// </summary>
public partial class ItemEditViewModel : ObservableObject
{
    private readonly ILogger<ItemEditViewModel> _logger;
    private ClientItem? _originalItem;
    private ClientItem? _workingCopy;

    [ObservableProperty]
    private ClientItem? currentItem;

    [ObservableProperty]
    private bool hasChanges = false;

    public ItemEditViewModel(ILogger<ItemEditViewModel> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 设置当前编辑的物品
    /// </summary>
    public void SetCurrentItem(ClientItem? item)
    {
        if (item == null)
        {
            CurrentItem = null;
            _originalItem = null;
            _workingCopy = null;
            HasChanges = false;
            return;
        }

        _originalItem = item;
        _workingCopy = CreateCopy(item);
        CurrentItem = _workingCopy;
        HasChanges = false;

        // 监听属性变化
        if (_workingCopy != null)
        {
            _workingCopy.PropertyChanged += OnItemPropertyChanged;
        }

        _logger.LogDebug("设置当前编辑物品: {ItemId} - {ItemName}", item.Id, item.Name);
    }

    /// <summary>
    /// 应用更改到原始物品
    /// </summary>
    public void ApplyChanges()
    {
        if (_originalItem == null || _workingCopy == null) return;

        CopyProperties(_workingCopy, _originalItem);
        HasChanges = false;

        _logger.LogDebug("应用物品更改: {ItemId} - {ItemName}", _originalItem.Id, _originalItem.Name);
    }

    /// <summary>
    /// 重置更改
    /// </summary>
    public void ResetChanges()
    {
        if (_originalItem == null) return;

        _workingCopy = CreateCopy(_originalItem);
        CurrentItem = _workingCopy;
        HasChanges = false;

        if (_workingCopy != null)
        {
            _workingCopy.PropertyChanged += OnItemPropertyChanged;
        }

        _logger.LogDebug("重置物品更改: {ItemId} - {ItemName}", _originalItem.Id, _originalItem.Name);
    }

    /// <summary>
    /// 设置所有职业权限
    /// </summary>
    [RelayCommand]
    private void SetAllClassPermissions(string parameter)
    {
        if (CurrentItem == null) return;

        var value = parameter == "1" ? 1 : 0;
        CurrentItem.SetAllClassPermissions(value);
        
        _logger.LogDebug("设置所有职业权限为: {Value}", value);
    }

    /// <summary>
    /// 重置为默认值
    /// </summary>
    [RelayCommand]
    private void ResetToDefaults()
    {
        if (CurrentItem == null) return;

        // 重置为默认值
        CurrentItem.Price = 100;
        CurrentItem.MaxStackCount = 1;
        CurrentItem.CanSellToNpc = true;
        CurrentItem.CanDepositToCharacterWarehouse = true;
        CurrentItem.CanDepositToAccountWarehouse = true;
        CurrentItem.CanDepositToGuildWarehouse = true;
        CurrentItem.Breakable = true;
        CurrentItem.SoulBind = false;
        CurrentItem.RemoveWhenLogout = false;
        CurrentItem.GenderPermitted = "all";
        CurrentItem.SetAllClassPermissions(1);
        CurrentItem.OptionSlotValue = 0;
        CurrentItem.SpecialSlotValue = 0;
        CurrentItem.OptionSlotBonus = 0;
        CurrentItem.BonusApply = "equip";
        CurrentItem.NoEnchant = false;
        CurrentItem.MaxEnchantValue = 15;
        CurrentItem.CanProcEnchant = true;
        CurrentItem.CanCompositeWeapon = true;
        CurrentItem.CannotChangeskin = 0;
        CurrentItem.MeshChange = 0;
        CurrentItem.CanDye = 0;
        CurrentItem.CashItem = 0;
        CurrentItem.CanSplit = false;
        CurrentItem.ItemDropPermitted = false;
        CurrentItem.BmRestrictCategory = 1;
        CurrentItem.CanApExtraction = false;
        CurrentItem.CanPolish = true;
        CurrentItem.PolishBurnOnAttack = 29;
        CurrentItem.PolishBurnOnDefend = 12;
        CurrentItem.RacePermitted = "pc_light pc_dark";

        _logger.LogDebug("重置物品为默认值: {ItemId}", CurrentItem.Id);
    }

    #region 私有方法

    /// <summary>
    /// 监听物品属性变化
    /// </summary>
    private void OnItemPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        HasChanges = true;
    }

    /// <summary>
    /// 创建物品副本
    /// </summary>
    private ClientItem CreateCopy(ClientItem original)
    {
        var copy = new ClientItem();
        CopyProperties(original, copy);
        return copy;
    }

    /// <summary>
    /// 复制物品属性
    /// </summary>
    private void CopyProperties(ClientItem source, ClientItem target)
    {
        // 基础信息
        target.Id = source.Id;
        target.Name = source.Name;
        target.Desc = source.Desc;
        target.ArmorType = source.ArmorType;
        target.ItemType = source.ItemType;
        target.Material = source.Material;
        target.IconName = source.IconName;

        // 经济属性
        target.Price = source.Price;
        target.MaxStackCount = source.MaxStackCount;
        target.CanSellToNpc = source.CanSellToNpc;

        // 存储权限
        target.CanDepositToCharacterWarehouse = source.CanDepositToCharacterWarehouse;
        target.CanDepositToAccountWarehouse = source.CanDepositToAccountWarehouse;
        target.CanDepositToGuildWarehouse = source.CanDepositToGuildWarehouse;

        // 物品属性
        target.EquipmentSlots = source.EquipmentSlots;
        target.Breakable = source.Breakable;
        target.SoulBind = source.SoulBind;
        target.RemoveWhenLogout = source.RemoveWhenLogout;
        target.GenderPermitted = source.GenderPermitted;

        // 职业限制
        target.Warrior = source.Warrior;
        target.Scout = source.Scout;
        target.Mage = source.Mage;
        target.Cleric = source.Cleric;
        target.Engineer = source.Engineer;
        target.Artist = source.Artist;
        target.Fighter = source.Fighter;
        target.Knight = source.Knight;
        target.Assassin = source.Assassin;
        target.Ranger = source.Ranger;
        target.Wizard = source.Wizard;
        target.Elementalist = source.Elementalist;
        target.Chanter = source.Chanter;
        target.Priest = source.Priest;
        target.Gunner = source.Gunner;
        target.Bard = source.Bard;
        target.Rider = source.Rider;

        // 强化属性
        target.OptionSlotValue = source.OptionSlotValue;
        target.SpecialSlotValue = source.SpecialSlotValue;
        target.OptionSlotBonus = source.OptionSlotBonus;
        target.BonusApply = source.BonusApply;
        target.NoEnchant = source.NoEnchant;
        target.MaxEnchantValue = source.MaxEnchantValue;
        target.CanProcEnchant = source.CanProcEnchant;
        target.CanCompositeWeapon = source.CanCompositeWeapon;

        // 外观和音效
        target.CannotChangeskin = source.CannotChangeskin;
        target.UiSoundType = source.UiSoundType;
        target.Mesh = source.Mesh;
        target.MeshChange = source.MeshChange;
        target.CanDye = source.CanDye;

        // 特殊属性
        target.CashItem = source.CashItem;
        target.CanSplit = source.CanSplit;
        target.ItemDropPermitted = source.ItemDropPermitted;
        target.BmRestrictCategory = source.BmRestrictCategory;
        target.CanApExtraction = source.CanApExtraction;
        target.CanPolish = source.CanPolish;
        target.PolishBurnOnAttack = source.PolishBurnOnAttack;
        target.PolishBurnOnDefend = source.PolishBurnOnDefend;
        target.RacePermitted = source.RacePermitted;
    }

    #endregion
}
