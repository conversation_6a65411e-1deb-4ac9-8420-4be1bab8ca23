using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleTest;

/// <summary>
/// 简化的XML数据服务
/// </summary>
public class XmlService
{
    private readonly ILogger<XmlService> _logger;

    public XmlService(ILogger<XmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加载XML文件中的所有物品
    /// </summary>
    public async Task<List<ClientItem>> LoadItemsAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载XML文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("文件不存在: {FilePath}", filePath);
                return new List<ClientItem>();
            }

            var items = new List<ClientItem>();
            var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);

            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);

            var itemNodes = xmlDoc.SelectNodes("//client_item");
            if (itemNodes == null)
            {
                _logger.LogWarning("未找到client_item节点");
                return items;
            }

            foreach (XmlNode itemNode in itemNodes)
            {
                try
                {
                    var item = ParseClientItem(itemNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析物品节点时出错");
                }
            }

            _logger.LogInformation("成功加载 {Count} 个物品", items.Count);
            return items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 保存物品列表到XML文件
    /// </summary>
    public async Task SaveItemsAsync(string filePath, List<ClientItem> items)
    {
        try
        {
            _logger.LogInformation("开始保存XML文件: {FilePath}, 物品数量: {Count}", filePath, items.Count);

            var xmlDoc = new XmlDocument();
            var declaration = xmlDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            xmlDoc.AppendChild(declaration);

            var rootElement = xmlDoc.CreateElement("client_items");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            foreach (var item in items)
            {
                var itemElement = CreateItemElement(xmlDoc, item);
                rootElement.AppendChild(itemElement);
            }

            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                NewLineChars = "\r\n"
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存XML文件: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 验证物品数据
    /// </summary>
    public (bool IsValid, List<string> Errors) ValidateItem(ClientItem item)
    {
        var errors = new List<string>();
        var context = new ValidationContext(item);
        var results = new List<ValidationResult>();

        if (!Validator.TryValidateObject(item, context, results, true))
        {
            errors.AddRange(results.Select(r => r.ErrorMessage ?? "未知验证错误"));
        }

        if (item.Id <= 0)
            errors.Add("物品ID必须大于0");

        if (string.IsNullOrWhiteSpace(item.Name))
            errors.Add("物品名称不能为空");

        if (item.MaxStackCount <= 0)
            errors.Add("最大堆叠数量必须大于0");

        return (errors.Count == 0, errors);
    }

    #region 私有方法

    /// <summary>
    /// 解析client_item XML节点
    /// </summary>
    private ClientItem? ParseClientItem(XmlNode itemNode)
    {
        try
        {
            var item = new ClientItem();

            item.Id = GetIntValue(itemNode, "id");
            item.Name = GetStringValue(itemNode, "name");
            item.Desc = GetStringValue(itemNode, "desc");
            item.ArmorType = GetStringValue(itemNode, "armor_type");
            item.ItemType = GetStringValue(itemNode, "item_type");
            item.Material = GetStringValue(itemNode, "material");
            item.IconName = GetStringValue(itemNode, "icon_name");
            item.Price = GetIntValue(itemNode, "price");
            item.MaxStackCount = GetIntValue(itemNode, "max_stack_count", 1);
            item.CanSellToNpc = GetBoolValue(itemNode, "can_sell_to_npc", true);
            item.CanDepositToCharacterWarehouse = GetBoolValue(itemNode, "can_deposit_to_character_warehouse", true);
            item.CanDepositToAccountWarehouse = GetBoolValue(itemNode, "can_deposit_to_account_warehouse", true);
            item.CanDepositToGuildWarehouse = GetBoolValue(itemNode, "can_deposit_to_guild_warehouse", true);
            item.EquipmentSlots = GetStringValue(itemNode, "equipment_slots");
            item.Breakable = GetBoolValue(itemNode, "breakable", true);
            item.SoulBind = GetBoolValue(itemNode, "soul_bind", false);
            item.RemoveWhenLogout = GetBoolValue(itemNode, "remove_when_logout", false);
            item.GenderPermitted = GetStringValue(itemNode, "gender_permitted", "all");

            // 职业限制
            item.Warrior = GetIntValue(itemNode, "warrior", 1);
            item.Scout = GetIntValue(itemNode, "scout", 1);
            item.Mage = GetIntValue(itemNode, "mage", 1);
            item.Cleric = GetIntValue(itemNode, "cleric", 1);
            item.Engineer = GetIntValue(itemNode, "engineer", 1);
            item.Artist = GetIntValue(itemNode, "artist", 1);
            item.Fighter = GetIntValue(itemNode, "fighter", 1);
            item.Knight = GetIntValue(itemNode, "knight", 1);
            item.Assassin = GetIntValue(itemNode, "assassin", 1);
            item.Ranger = GetIntValue(itemNode, "ranger", 1);
            item.Wizard = GetIntValue(itemNode, "wizard", 1);
            item.Elementalist = GetIntValue(itemNode, "elementalist", 1);
            item.Chanter = GetIntValue(itemNode, "chanter", 1);
            item.Priest = GetIntValue(itemNode, "priest", 1);
            item.Gunner = GetIntValue(itemNode, "gunner", 1);
            item.Bard = GetIntValue(itemNode, "bard", 1);
            item.Rider = GetIntValue(itemNode, "rider", 1);

            // 强化属性
            item.OptionSlotValue = GetIntValue(itemNode, "option_slot_value");
            item.SpecialSlotValue = GetIntValue(itemNode, "special_slot_value");
            item.OptionSlotBonus = GetIntValue(itemNode, "option_slot_bonus");
            item.BonusApply = GetStringValue(itemNode, "bonus_apply", "equip");
            item.NoEnchant = GetBoolValue(itemNode, "no_enchant", false);
            item.MaxEnchantValue = GetIntValue(itemNode, "max_enchant_value", 15);
            item.CanProcEnchant = GetBoolValue(itemNode, "can_proc_enchant", true);
            item.CanCompositeWeapon = GetBoolValue(itemNode, "can_composite_weapon", true);

            // 外观和音效
            item.CannotChangeskin = GetIntValue(itemNode, "cannot_changeskin");
            item.UiSoundType = GetStringValue(itemNode, "ui_sound_type");
            item.Mesh = GetStringValue(itemNode, "mesh");
            item.MeshChange = GetIntValue(itemNode, "mesh_change");
            item.CanDye = GetIntValue(itemNode, "can_dye");

            // 特殊属性
            item.CashItem = GetIntValue(itemNode, "cash_item");
            item.CanSplit = GetBoolValue(itemNode, "can_split", false);
            item.ItemDropPermitted = GetBoolValue(itemNode, "item_drop_permitted", false);
            item.BmRestrictCategory = GetIntValue(itemNode, "bm_restrict_category", 1);
            item.CanApExtraction = GetBoolValue(itemNode, "can_ap_extraction", false);
            item.CanPolish = GetBoolValue(itemNode, "can_polish", true);
            item.PolishBurnOnAttack = GetIntValue(itemNode, "polish_burn_on_attack", 29);
            item.PolishBurnOnDefend = GetIntValue(itemNode, "polish_burn_on_defend", 12);
            item.RacePermitted = GetStringValue(itemNode, "race_permitted", "pc_light pc_dark");

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析物品节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 创建物品的XML元素
    /// </summary>
    private XmlElement CreateItemElement(XmlDocument xmlDoc, ClientItem item)
    {
        var itemElement = xmlDoc.CreateElement("client_item");

        AddElement(xmlDoc, itemElement, "id", item.Id.ToString());
        AddElement(xmlDoc, itemElement, "name", item.Name);
        AddElement(xmlDoc, itemElement, "desc", item.Desc);
        AddElement(xmlDoc, itemElement, "armor_type", item.ArmorType);
        AddElement(xmlDoc, itemElement, "item_type", item.ItemType);
        AddElement(xmlDoc, itemElement, "material", item.Material);
        AddElement(xmlDoc, itemElement, "icon_name", item.IconName);
        AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
        AddElement(xmlDoc, itemElement, "max_stack_count", item.MaxStackCount.ToString());
        AddElement(xmlDoc, itemElement, "can_sell_to_npc", item.CanSellToNpc ? "TRUE" : "FALSE");

        return itemElement;
    }

    private void AddElement(XmlDocument xmlDoc, XmlElement parent, string name, string value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            var element = xmlDoc.CreateElement(name);
            element.InnerText = value;
            parent.AppendChild(element);
        }
    }

    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText ?? defaultValue;
    }

    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText, out var value))
            return value;
        return defaultValue;
    }

    private bool GetBoolValue(XmlNode parentNode, string elementName, bool defaultValue = false)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null)
        {
            var text = node.InnerText.ToUpperInvariant();
            return text == "TRUE" || text == "1";
        }
        return defaultValue;
    }

    #endregion
}
