<Window x:Class="SimpleWpfEditor.NpcTradeEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="NPC交易编辑器" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="🔄" FontSize="24" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="NPC交易编辑器" FontSize="20" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock x:Name="TradeInfoTextBlock" Text="编辑NPC交易列表信息和商品" FontSize="12" Foreground="#7F8C8D" Margin="0,4,0,0"/>
                </StackPanel>
                <TextBlock Grid.Column="2" x:Name="TradeIdTextBlock" Text="ID: 0" FontSize="14" FontWeight="Medium" Foreground="#34495E" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 主编辑区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧编辑区域 -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    
                    <!-- 基础信息 -->
                    <Border Background="White" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📋 基础信息" FontSize="16" FontWeight="SemiBold" Foreground="#3498DB" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="交易ID:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="IdTextBlock" Text="0" VerticalAlignment="Center" Background="#ECF0F1" Padding="8,4" FontFamily="Consolas"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="内部名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="NameTextBox" ToolTip="交易列表的内部标识名称"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="描述键:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DescTextBox" ToolTip="交易列表描述的本地化键"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="使用类别:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="UseCategoryTextBox" ToolTip="交易列表使用类别（通常为0）"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="本地化名称:" VerticalAlignment="Top" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBlock Grid.Column="1" x:Name="LocalizedNameTextBlock" Text="未加载" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4" TextWrapping="Wrap"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 交易分类 -->
                    <Border Background="White" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="🏷️ 交易分类" FontSize="16" FontWeight="SemiBold" Foreground="#E67E22" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="交易类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="TradeTypeTextBlock" Text="通用交易" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="武器类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="WeaponTypeTextBlock" Text="通用" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="防具类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="ArmorTypeTextBlock" Text="通用" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="阵营类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="FactionTypeTextBlock" Text="通用" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="等级类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="LevelTypeTextBlock" Text="基础" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="商品数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="ItemCountTextBlock" Text="0" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4" FontWeight="Bold"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 商品统计 -->
                    <Border Background="White" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📊 商品统计" FontSize="16" FontWeight="SemiBold" Foreground="#9B59B6" Margin="0,0,0,16"/>
                            
                            <ListBox x:Name="ItemStatsListBox" Height="200" Background="#FAFAFA" BorderBrush="#BDC3C7" BorderThickness="1">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="{Binding Key}" VerticalAlignment="Center"/>
                                            <Border Grid.Column="1" Background="#3498DB" Padding="4,2" Margin="4,0,0,0">
                                                <TextBlock Text="{Binding Value}" Foreground="White" FontWeight="Bold" FontSize="10"/>
                                            </Border>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 右侧编辑区域 -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    
                    <!-- 商品列表 -->
                    <Border Background="White" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🔄 交易商品" FontSize="16" FontWeight="SemiBold" Foreground="#27AE60" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" x:Name="AddItemButton" Content="➕ 添加" Padding="8,4" Margin="0,0,8,0" Background="#27AE60" Foreground="White" BorderThickness="0"/>
                                <Button Grid.Column="2" x:Name="RemoveItemButton" Content="➖ 删除" Padding="8,4" Background="#E74C3C" Foreground="White" BorderThickness="0"/>
                            </Grid>
                            
                            <ListBox x:Name="GoodsListBox" Height="450" Background="#FAFAFA" BorderBrush="#BDC3C7" BorderThickness="1">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="🔄" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBox Grid.Column="1" Text="{Binding Item, UpdateSourceTrigger=PropertyChanged}" 
                                                     ToolTip="物品ID (例如: sword_n_e1_55e, armor_torso_n_e1_55d)"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            
                            <Expander Header="💡 常用交易物品ID格式示例" FontSize="12" FontWeight="Medium" Margin="0,12,0,0" Foreground="#7F8C8D">
                                <TextBlock FontSize="11" Foreground="#95A5A6" TextWrapping="Wrap" Margin="0,8,0,0">
                                    • 武器: sword_n_e1_55e, mace_n_e1_55e, dagger_n_e1_55e<LineBreak/>
                                    • 远程武器: bow_n_e1_55e, gun_n_e1_55e<LineBreak/>
                                    • 法系武器: staff_n_e1_55e, orb_n_e1_55e, book_n_e1_55e<LineBreak/>
                                    • 防具: lt_torso_n_e1_55d, pl_head_n_e1_55d, rb_leg_n_e1_55d<LineBreak/>
                                    • 防具配件: lt_shoes_n_e1_55d, lt_glove_n_e1_55d, lt_shoulder_n_e1_55d<LineBreak/>
                                    • 盾牌: shield_n_e1_55e<LineBreak/>
                                    • 饰品: ring_n_e1_55e, earring_n_e1_55e, necklace_n_e1_55e<LineBreak/>
                                    • 材料: ws_material_n_e_55a, as_material_n_e_55a<LineBreak/>
                                    • 测试物品: test_[类型]_[编号]
                                </TextBlock>
                            </Expander>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="准备就绪" VerticalAlignment="Center" Foreground="#7F8C8D"/>
            <Button Grid.Column="1" x:Name="ResetButton" Content="🔄 重置" Padding="12,8" Margin="0,0,8,0" Background="#95A5A6" Foreground="White" BorderThickness="0"/>
            <Button Grid.Column="2" x:Name="CancelButton" Content="❌ 取消" Padding="12,8" Margin="0,0,8,0" Background="#BDC3C7" Foreground="White" BorderThickness="0"/>
            <Button Grid.Column="3" x:Name="SaveButton" Content="💾 保存" Padding="12,8" Background="#27AE60" Foreground="White" BorderThickness="0"/>
        </Grid>
    </Grid>
</Window>
