<Window x:Class="SimpleWpfEditor.SkillManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="技能管理器" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#34495E" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Grid.Column="0" Kind="Magic" Width="32" Height="32" 
                                         Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="技能管理器" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="管理游戏中的所有技能和技能属性" FontSize="14" Foreground="#7F8C8D" Margin="0,4,0,0"/>
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="OpenFileButton" Content="📁 打开文件" Padding="12,8" Margin="0,0,8,0" Background="#3498DB" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveFileButton" Content="💾 保存文件" Padding="12,8" Margin="0,0,8,0" Background="#27AE60" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveWithServerConversionButton" Content="🔄 保存并转换服务端" Padding="12,8" Background="#FF5722" Foreground="White" BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏和筛选 -->
        <Border Grid.Row="1" Background="#ECF0F1" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 搜索框 -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0" 
                         materialDesign:HintAssist.Hint="🔍 搜索技能名称或ID..." 
                         Margin="0,0,16,0" VerticalAlignment="Center"/>

                <!-- 技能类型筛选 -->
                <ComboBox x:Name="SkillTypeFilterComboBox" Grid.Column="1" Width="120" Margin="0,0,8,0"
                          materialDesign:HintAssist.Hint="技能类型">
                    <ComboBoxItem Content="全部" Tag=""/>
                    <ComboBoxItem Content="主动技能" Tag="ACTIVE"/>
                    <ComboBoxItem Content="被动技能" Tag="PASSIVE"/>
                    <ComboBoxItem Content="切换技能" Tag="TOGGLE"/>
                    <ComboBoxItem Content="连锁技能" Tag="CHAIN"/>
                </ComboBox>

                <!-- 等级筛选 -->
                <ComboBox x:Name="LevelFilterComboBox" Grid.Column="2" Width="100" Margin="0,0,8,0"
                          materialDesign:HintAssist.Hint="等级">
                    <ComboBoxItem Content="全部" Tag=""/>
                    <ComboBoxItem Content="1级" Tag="1"/>
                    <ComboBoxItem Content="2级" Tag="2"/>
                    <ComboBoxItem Content="3级" Tag="3"/>
                    <ComboBoxItem Content="4级" Tag="4"/>
                    <ComboBoxItem Content="5级" Tag="5"/>
                </ComboBox>

                <!-- 清除筛选按钮 -->
                <Button x:Name="ClearFilterButton" Grid.Column="3" Content="🧹 清除筛选" 
                        Padding="8,6" Margin="0,0,8,0" Background="#95A5A6" Foreground="White" BorderThickness="0"/>

                <!-- 添加技能按钮 -->
                <Button x:Name="AddSkillButton" Grid.Column="4" Content="➕ 添加技能" 
                        Padding="8,6" Margin="0,0,8,0" Background="#2ECC71" Foreground="White" BorderThickness="0"/>

                <!-- 删除技能按钮 -->
                <Button x:Name="DeleteSkillButton" Grid.Column="5" Content="🗑️ 删除技能" 
                        Padding="8,6" Background="#E74C3C" Foreground="White" BorderThickness="0"/>
            </Grid>
        </Border>

        <!-- 主内容区域 - 左右分栏 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：技能列表 -->
            <DataGrid x:Name="SkillsDataGrid" Grid.Column="0"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      Background="White"
                      AlternatingRowBackground="#F8F9FA"
                      RowHeight="35"
                      SelectionChanged="SkillsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>
                    <DataGridTextColumn Header="技能名称" Binding="{Binding Name}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="类型" Binding="{Binding SkillTypeDisplay}" Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="等级" Binding="{Binding Level}" Width="50" IsReadOnly="True"/>
                    <DataGridTextColumn Header="MP消耗" Binding="{Binding MpConsume}" Width="60" IsReadOnly="True"/>
                </DataGrid.Columns>

                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem x:Name="EditSkillMenuItem" Header="📝 编辑技能" Click="EditSkillMenuItem_Click"/>
                        <MenuItem x:Name="CopySkillMenuItem" Header="📋 复制技能" Click="CopySkillMenuItem_Click"/>
                        <Separator/>
                        <MenuItem x:Name="DeleteSkillMenuItem" Header="🗑️ 删除技能" Click="DeleteSkillMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>

            <!-- 分隔线 -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="#BDC3C7"/>

            <!-- 右侧：技能编辑面板 -->
            <Border Grid.Column="2" Background="White" BorderBrush="#BDC3C7" BorderThickness="1,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="SkillEditPanel" Margin="16" IsEnabled="False">
                        <!-- 编辑面板标题 -->
                        <TextBlock Text="技能编辑" FontSize="18" FontWeight="Bold" Margin="0,0,0,16" Foreground="#2C3E50"/>

                        <!-- 基础信息 -->
                        <TextBlock Text="基础信息" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="技能ID:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillIdTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8" IsReadOnly="True" Background="#F8F9FA"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="技能名称:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillNameTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="描述:" VerticalAlignment="Top" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillDescTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"
                                     TextWrapping="Wrap" AcceptsReturn="True" Height="60"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="技能类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <ComboBox x:Name="SkillTypeComboBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8">
                                <ComboBoxItem Content="主动技能" Tag="ACTIVE"/>
                                <ComboBoxItem Content="被动技能" Tag="PASSIVE"/>
                                <ComboBoxItem Content="连锁技能" Tag="CHAIN"/>
                                <ComboBoxItem Content="切换技能" Tag="TOGGLE"/>
                            </ComboBox>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="等级:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillLevelTextBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="最大等级:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillMaxLevelTextBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 消耗属性 -->
                        <TextBlock Text="消耗属性" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="MP消耗:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillMpConsumeTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="HP消耗:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillHpConsumeTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="DP消耗:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillDpConsumeTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 时间属性 -->
                        <TextBlock Text="时间属性" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="施法时间(ms):" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillCastTimeTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="冷却时间(ms):" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillCoolTimeTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="持续时间(ms):" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillDurationTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 目标属性 -->
                        <TextBlock Text="目标属性" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="目标类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <ComboBox x:Name="SkillTargetTypeComboBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8">
                                <ComboBoxItem Content="敌人" Tag="ENEMY"/>
                                <ComboBoxItem Content="盟友" Tag="ALLY"/>
                                <ComboBoxItem Content="自己" Tag="SELF"/>
                                <ComboBoxItem Content="任意" Tag="ANY"/>
                            </ComboBox>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="目标范围:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillTargetRangeTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="最大目标数:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillTargetMaxCountTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 效果属性 -->
                        <TextBlock Text="效果属性" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="效果类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillEffectTypeTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="效果数值:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillEffectValueTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 显示属性 -->
                        <TextBlock Text="显示属性" FontSize="14" FontWeight="Bold" Margin="0,0,0,8" Foreground="#34495E"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="动画:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillAnimationTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="图标:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                            <TextBox x:Name="SkillIconTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                            <Button x:Name="SaveSkillButton" Content="💾 保存修改" Padding="12,8" Margin="0,0,8,0"
                                    Background="#27AE60" Foreground="White" BorderThickness="0" Click="SaveSkillButton_Click"/>
                            <Button x:Name="CancelEditButton" Content="❌ 取消" Padding="12,8"
                                    Background="#95A5A6" Foreground="White" BorderThickness="0" Click="CancelEditButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#BDC3C7" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusText" Grid.Column="0" Text="就绪" VerticalAlignment="Center"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="技能总数: " VerticalAlignment="Center"/>
                    <TextBlock x:Name="TotalSkillsText" Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock Text="当前显示: " VerticalAlignment="Center"/>
                    <TextBlock x:Name="FilteredSkillsText" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
