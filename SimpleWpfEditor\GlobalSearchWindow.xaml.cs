using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SimpleWpfEditor;

public partial class GlobalSearchWindow : Window
{
    private readonly ItemStringLinkService _linkService;
    private readonly ObservableCollection<ItemSearchResult> _searchResults;

    public ClientItem? SelectedItem { get; private set; }

    public GlobalSearchWindow(ItemStringLinkService linkService)
    {
        InitializeComponent();
        
        _linkService = linkService;
        _searchResults = new ObservableCollection<ItemSearchResult>();
        
        ResultsDataGrid.ItemsSource = _searchResults;
        ResultsDataGrid.SelectionChanged += ResultsDataGrid_SelectionChanged;
        
        SearchTextBox.Focus();
        
        // 显示统计信息
        var stats = _linkService.GetStatistics();
        StatsText.Text = $"数据库: {stats.TotalItems} 个物品, {stats.LinkedItems} 个已关联";
    }

    private void SearchButton_Click(object sender, RoutedEventArgs e)
    {
        PerformSearch();
    }

    private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            PerformSearch();
        }
    }

    private void ClearButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Clear();
        _searchResults.Clear();
        ResultCountText.Text = "输入关键词开始搜索";
        SelectButton.IsEnabled = false;
    }

    private void PerformSearch()
    {
        var searchText = SearchTextBox.Text?.Trim();
        if (string.IsNullOrEmpty(searchText))
        {
            ResultCountText.Text = "请输入搜索关键词";
            return;
        }

        _searchResults.Clear();
        
        try
        {
            var results = _linkService.SearchItems(searchText, 1000);
            
            foreach (var result in results)
            {
                _searchResults.Add(result);
            }
            
            if (_searchResults.Count > 0)
            {
                ResultCountText.Text = $"找到 {_searchResults.Count} 个匹配的物品";
                
                // 统计匹配类型
                var idMatches = _searchResults.Count(r => r.MatchType == "ID");
                var nameMatches = _searchResults.Count(r => r.MatchType == "内部名称");
                var localizedMatches = _searchResults.Count(r => r.MatchType == "本地化名称");
                
                var breakdown = new List<string>();
                if (idMatches > 0) breakdown.Add($"ID匹配: {idMatches}");
                if (nameMatches > 0) breakdown.Add($"内部名称: {nameMatches}");
                if (localizedMatches > 0) breakdown.Add($"本地化名称: {localizedMatches}");
                
                if (breakdown.Count > 0)
                {
                    ResultCountText.Text += $" ({string.Join(", ", breakdown)})";
                }
            }
            else
            {
                ResultCountText.Text = "未找到匹配的物品";
            }
        }
        catch (Exception ex)
        {
            ResultCountText.Text = $"搜索出错: {ex.Message}";
        }
    }

    private void ResultsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        SelectButton.IsEnabled = ResultsDataGrid.SelectedItem != null;
    }

    private void ResultsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (ResultsDataGrid.SelectedItem != null)
        {
            SelectItem();
        }
    }

    private void SelectButton_Click(object sender, RoutedEventArgs e)
    {
        SelectItem();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void SelectItem()
    {
        if (ResultsDataGrid.SelectedItem is ItemSearchResult selectedResult)
        {
            SelectedItem = selectedResult.Item;
            DialogResult = true;
            Close();
        }
    }
}
