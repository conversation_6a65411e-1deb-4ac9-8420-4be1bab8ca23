<Window x:Class="SimpleWpfEditor.ProfessionalNpcManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="专业NPC管理器" Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- 样式定义 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SearchTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="LoadButton" Content="📁 加载文件" Style="{StaticResource ModernButton}" Click="LoadButton_Click"/>
                    <Button x:Name="SaveButton" Content="💾 保存文件" Style="{StaticResource ModernButton}" Click="SaveButton_Click"/>
                    <Button x:Name="AddButton" Content="➕ 添加NPC" Style="{StaticResource ModernButton}" Click="AddButton_Click"/>
                    <Button x:Name="DeleteButton" Content="🗑️ 删除NPC" Style="{StaticResource ModernButton}" Click="DeleteButton_Click"/>
                </StackPanel>

                <!-- 搜索框 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="🔍" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="16"/>
                    <TextBox x:Name="SearchTextBox" Width="300" Style="{StaticResource SearchTextBox}"
                             Text="搜索NPC..." GotFocus="SearchTextBox_GotFocus" 
                             LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- NPC列表 -->
            <Border Grid.Column="0" Background="White" Margin="8" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 列表标题 -->
                    <Border Grid.Row="0" Background="#2196F3" CornerRadius="8,8,0,0" Padding="16,12">
                        <TextBlock Text="🎭 NPC列表" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    </Border>

                    <!-- NPC列表 -->
                    <ListBox Grid.Row="1" x:Name="NpcListBox" SelectionChanged="NpcListBox_SelectionChanged"
                             BorderThickness="0" Padding="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="6" Padding="12" Margin="0,2">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=ListBoxItem}}" Value="True">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                    <Setter Property="BorderBrush" Value="#2196F3"/>
                                                    <Setter Property="BorderThickness" Value="2"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                        <TextBlock Text="{Binding Id, StringFormat='ID: {0}'}" FontSize="11" Foreground="#666" Margin="0,2,0,0"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                            <Border Background="#4CAF50" CornerRadius="10" Padding="6,2" Margin="0,0,4,0">
                                                <TextBlock Text="{Binding Level, StringFormat='Lv.{0}'}" Foreground="White" FontSize="10"/>
                                            </Border>
                                            <Border Background="#FF9800" CornerRadius="10" Padding="6,2">
                                                <TextBlock Text="{Binding NpcType}" Foreground="White" FontSize="10"/>
                                            </Border>
                                        </StackPanel>
                                        <TextBlock Text="{Binding Tribe}" FontSize="10" Foreground="#999" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="#E0E0E0"/>

            <!-- NPC详情编辑区域 -->
            <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto" Margin="8">
                <StackPanel x:Name="DetailsPanel" IsEnabled="False">
                    
                    <!-- 基础信息 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="📋 基础信息" FontSize="16" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,16"/>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="ID:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="IdTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="NameTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="描述:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DescTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="模型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MeshTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="目录:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DirTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="标题:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="NpcTitleTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="3" x:Name="NpcTypeComboBox">
                                    <ComboBoxItem Content="Monster"/>
                                    <ComboBoxItem Content="general"/>
                                    <ComboBoxItem Content="NPC"/>
                                    <ComboBoxItem Content="Boss"/>
                                </ComboBox>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 基础属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="⚡ 基础属性" FontSize="16" FontWeight="SemiBold" Foreground="#4CAF50" Margin="0,0,0,16"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="等级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="LevelTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="经验:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="ExpTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="最大HP:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MaxHpTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="HP回复:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="HpRegenTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="缩放:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="ScaleTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="武器缩放:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="WeaponScaleTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 战斗属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="⚔️ 战斗属性" FontSize="16" FontWeight="SemiBold" Foreground="#FF5722" Margin="0,0,0,16"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="物理攻击:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="PhysicalAttackTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="物理防御:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="PhysicalDefendTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="魔法攻击:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MagicalAttackTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="魔法防御:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MagicalDefendTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="最小伤害:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MinDamageTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="最大伤害:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MaxDamageTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="命中:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="HitAccuracyTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="闪避:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DodgeTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- AI和行为 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="🤖 AI和行为" FontSize="16" FontWeight="SemiBold" Foreground="#9C27B0" Margin="0,0,0,16"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="AI名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="AiNameTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="空闲行为:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="3" x:Name="IdleNameComboBox">
                                    <ComboBoxItem Content="NoMove"/>
                                    <ComboBoxItem Content="RandomMove"/>
                                    <ComboBoxItem Content="Patrol"/>
                                </ComboBox>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="种族:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TribeTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="种族类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="RaceTypeTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="感知范围:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="SensoryRangeTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="感知角度:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="SensoryAngleTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 操作按钮 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button x:Name="SaveNpcButton" Content="💾 保存修改" Style="{StaticResource ModernButton}" Click="SaveNpcButton_Click"/>
                            <Button x:Name="CancelButton" Content="❌ 取消修改" Style="{StaticResource ModernButton}" 
                                    Background="#F44336" Click="CancelButton_Click"/>
                            <Button x:Name="CopyNpcButton" Content="📋 复制NPC" Style="{StaticResource ModernButton}" 
                                    Background="#FF9800" Click="CopyNpcButton_Click"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="就绪" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" x:Name="CountTextBlock" Text="NPC数量: 0" VerticalAlignment="Center" FontWeight="Medium"/>
            </Grid>
        </Border>
    </Grid>
</Window>
