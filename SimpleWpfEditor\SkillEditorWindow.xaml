<Window x:Class="SimpleWpfEditor.SkillEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="技能编辑器" Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="技能编辑器" FontSize="24" FontWeight="Bold" 
                   Foreground="#2C3E50" Margin="0,0,0,16"/>

        <!-- 编辑区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 基础信息 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="基础信息" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox x:Name="IdTextBox" Grid.Row="0" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="技能ID" Margin="0,0,0,8"/>
                            <TextBox x:Name="NameTextBox" Grid.Row="0" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="技能名称" Margin="0,0,0,8"/>
                            
                            <TextBox x:Name="DescTextBox" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3"
                                     materialDesign:HintAssist.Hint="技能描述" Margin="0,0,0,8"/>
                            
                            <ComboBox x:Name="SkillTypeComboBox" Grid.Row="2" Grid.Column="0"
                                      materialDesign:HintAssist.Hint="技能类型" Margin="0,0,0,8">
                                <ComboBoxItem Content="ACTIVE" Tag="ACTIVE"/>
                                <ComboBoxItem Content="PASSIVE" Tag="PASSIVE"/>
                                <ComboBoxItem Content="TOGGLE" Tag="TOGGLE"/>
                                <ComboBoxItem Content="CHAIN" Tag="CHAIN"/>
                            </ComboBox>
                            
                            <StackPanel Grid.Row="2" Grid.Column="2" Orientation="Horizontal">
                                <TextBox x:Name="LevelTextBox" Width="100"
                                         materialDesign:HintAssist.Hint="等级" Margin="0,0,8,8"/>
                                <TextBox x:Name="MaxLevelTextBox" Width="100"
                                         materialDesign:HintAssist.Hint="最大等级" Margin="0,0,0,8"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 消耗属性 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="消耗属性" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="MpConsumeTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="MP消耗"/>
                            <TextBox x:Name="HpConsumeTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="HP消耗"/>
                            <TextBox x:Name="DpConsumeTextBox" Grid.Column="4"
                                     materialDesign:HintAssist.Hint="DP消耗"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 时间属性 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="时间属性 (毫秒)" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="CastTimeTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="施法时间"/>
                            <TextBox x:Name="CoolTimeTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="冷却时间"/>
                            <TextBox x:Name="DurationTextBox" Grid.Column="4"
                                     materialDesign:HintAssist.Hint="持续时间"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 目标属性 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="目标属性" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox x:Name="TargetTypeComboBox" Grid.Column="0"
                                      materialDesign:HintAssist.Hint="目标类型">
                                <ComboBoxItem Content="SELF" Tag="SELF"/>
                                <ComboBoxItem Content="ENEMY" Tag="ENEMY"/>
                                <ComboBoxItem Content="ALLY" Tag="ALLY"/>
                                <ComboBoxItem Content="POINT" Tag="POINT"/>
                                <ComboBoxItem Content="NONE" Tag="NONE"/>
                            </ComboBox>
                            
                            <TextBox x:Name="TargetRangeTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="目标范围"/>
                            <TextBox x:Name="TargetMaxCountTextBox" Grid.Column="4"
                                     materialDesign:HintAssist.Hint="最大目标数"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 效果属性 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="效果属性" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="EffectTypeTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="效果类型"/>
                            <TextBox x:Name="EffectValueTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="效果数值"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 显示属性 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="显示属性" FontSize="18" FontWeight="Bold" Margin="0,0,0,12"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="AnimationTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="动画名称"/>
                            <TextBox x:Name="IconTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="图标名称"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="SaveButton" Content="💾 保存" Padding="16,8" Margin="0,0,8,0"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="#27AE60" Foreground="White" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ 取消" Padding="16,8"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
