<Window x:Class="SimpleWpfEditor.TitleEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="称号编辑器" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="🏆" FontSize="24" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="称号编辑器" FontSize="20" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock x:Name="TitleInfoTextBlock" Text="编辑称号属性和奖励" FontSize="12" Foreground="#7F8C8D" Margin="0,4,0,0"/>
                </StackPanel>
                <TextBlock Grid.Column="2" x:Name="TitleIdTextBlock" Text="ID: 0" FontSize="14" FontWeight="Medium" Foreground="#34495E" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 主编辑区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧编辑区域 -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    
                    <!-- 基础信息 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📋 基础信息" FontSize="16" FontWeight="SemiBold" Foreground="#3498DB" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="称号ID:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="IdTextBlock" Text="0" VerticalAlignment="Center" Background="#ECF0F1" Padding="8,4" FontFamily="Consolas"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="内部名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="NameTextBox" ToolTip="称号的内部标识名称"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="显示名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DescTextBox" ToolTip="称号显示名称的本地化键"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="本地化名称:" VerticalAlignment="Top" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBlock Grid.Column="1" x:Name="LocalizedNameTextBlock" Text="未加载" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4" TextWrapping="Wrap"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="详细描述:" VerticalAlignment="Top" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBox Grid.Column="1" x:Name="TitleDescTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" ToolTip="称号详细描述的本地化键"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="本地化描述:" VerticalAlignment="Top" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBlock Grid.Column="1" x:Name="LocalizedDescTextBlock" Text="未加载" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4" TextWrapping="Wrap" Height="60"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 称号属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="⚙️ 称号属性" FontSize="16" FontWeight="SemiBold" Foreground="#E67E22" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="种族限制:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="TitleRaceComboBox">
                                    <ComboBoxItem Content="天族" Tag="0"/>
                                    <ComboBoxItem Content="魔族" Tag="1"/>
                                    <ComboBoxItem Content="通用" Tag="2"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="称号类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TitleTypeTextBox" ToolTip="称号类型（通常为0）"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="优先级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TitlePriorityTextBox" ToolTip="称号显示优先级（数值越小优先级越高）"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="获得地点:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TitleLocationTextBox" ToolTip="称号获得地点的本地化键"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="本地化地点:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="LocalizedLocationTextBlock" Text="未加载" VerticalAlignment="Center" Background="#E8F5E8" Padding="8,4"/>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 右侧编辑区域 -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    
                    <!-- 奖励属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🎁 奖励属性" FontSize="16" FontWeight="SemiBold" Foreground="#9B59B6" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" x:Name="AddBonusButton" Content="➕ 添加" Padding="8,4" Margin="0,0,8,0" Background="#27AE60" Foreground="White" BorderThickness="0"/>
                                <Button Grid.Column="2" x:Name="RemoveBonusButton" Content="➖ 删除" Padding="8,4" Background="#E74C3C" Foreground="White" BorderThickness="0"/>
                            </Grid>
                            
                            <ListBox x:Name="BonusAttrsListBox" Height="400" Background="#FAFAFA" BorderBrush="#BDC3C7" BorderThickness="1">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="🎯" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBox Grid.Column="1" Text="{Binding BonusAttr, UpdateSourceTrigger=PropertyChanged}" 
                                                     ToolTip="格式: 属性名 数值 (例如: maxHp 50, Critical 10, speed 3%)"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            
                            <Expander Header="💡 常用属性格式示例" FontSize="12" FontWeight="Medium" Margin="0,12,0,0" Foreground="#7F8C8D">
                                <TextBlock FontSize="11" Foreground="#95A5A6" TextWrapping="Wrap" Margin="0,8,0,0">
                                    • 生命值: maxHp 50<LineBreak/>
                                    • 魔法值: maxMp 30<LineBreak/>
                                    • 物理攻击: phyAttack 5<LineBreak/>
                                    • 物理防御: PhysicalDefend 15<LineBreak/>
                                    • 魔法抗性: MagicalResist 10<LineBreak/>
                                    • 暴击: Critical 8<LineBreak/>
                                    • 闪避: Dodge 6<LineBreak/>
                                    • 格挡: Block 10<LineBreak/>
                                    • 招架: Parry 8<LineBreak/>
                                    • 命中: HitAccuracy 12<LineBreak/>
                                    • 移动速度: speed 3%<LineBreak/>
                                    • 飞行速度: flyspeed 5%<LineBreak/>
                                    • 攻击延迟: AttackDelay 2%<LineBreak/>
                                    • 施法时间: boostCastingTime 4%<LineBreak/>
                                    • PVP攻击: PVPAttackRatio 20<LineBreak/>
                                    • PVP防御: PVPDefendRatio 15
                                </TextBlock>
                            </Expander>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="准备就绪" VerticalAlignment="Center" Foreground="#7F8C8D"/>
            <Button Grid.Column="1" x:Name="ResetButton" Content="🔄 重置" Padding="12,8" Margin="0,0,8,0" Background="#95A5A6" Foreground="White" BorderThickness="0"/>
            <Button Grid.Column="2" x:Name="CancelButton" Content="❌ 取消" Padding="12,8" Margin="0,0,8,0" Background="#BDC3C7" Foreground="White" BorderThickness="0"/>
            <Button Grid.Column="3" x:Name="SaveButton" Content="💾 保存" Padding="12,8" Background="#27AE60" Foreground="White" BorderThickness="0"/>
        </Grid>
    </Grid>
</Window>
