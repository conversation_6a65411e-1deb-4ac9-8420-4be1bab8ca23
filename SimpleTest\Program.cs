using Microsoft.Extensions.Logging;
using SimpleTest;

Console.WriteLine("=================================");
Console.WriteLine("    Aion物品编辑器 - 测试程序");
Console.WriteLine("=================================");
Console.WriteLine();

// 创建日志记录器
using var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
var logger = loggerFactory.CreateLogger<XmlService>();

// 创建服务
var xmlService = new XmlService(logger);

try
{
    // 测试创建一个示例物品
    var testItem = new ClientItem
    {
        Id = 999999,
        Name = "test_sword",
        Desc = "STR_TEST_SWORD",
        ArmorType = "weapon",
        ItemType = "normal",
        Material = "mat_metal",
        IconName = "Icon_Item_Sword_Test",
        Price = 1000,
        MaxStackCount = 1,
        EquipmentSlots = "main_hand",
        Warrior = 1,
        Scout = 0,
        Mage = 0,
        Cleric = 0,
        MaxEnchantValue = 15,
        CanProcEnchant = true,
        UiSoundType = "Sounds/item/item_att_sword"
    };

    Console.WriteLine($"✓ 创建测试物品: {testItem.DisplayName}");
    Console.WriteLine($"  类型: {testItem.ArmorType}");
    Console.WriteLine($"  价格: {testItem.Price}");
    Console.WriteLine($"  装备部位: {testItem.EquipmentSlots}");
    Console.WriteLine();

    // 验证物品
    var (isValid, errors) = xmlService.ValidateItem(testItem);
    Console.WriteLine($"✓ 物品验证结果: {(isValid ? "通过" : "失败")}");
    
    if (!isValid)
    {
        Console.WriteLine("  验证错误:");
        foreach (var error in errors)
        {
            Console.WriteLine($"    - {error}");
        }
    }
    Console.WriteLine();

    // 测试保存和加载
    var testItems = new List<ClientItem> { testItem };
    var testFilePath = "test_items.xml";

    Console.WriteLine($"✓ 保存测试文件: {testFilePath}");
    await xmlService.SaveItemsAsync(testFilePath, testItems);
    Console.WriteLine($"  文件大小: {new FileInfo(testFilePath).Length} 字节");
    Console.WriteLine();

    Console.WriteLine("✓ 加载测试文件...");
    var loadedItems = await xmlService.LoadItemsAsync(testFilePath);
    Console.WriteLine($"  加载了 {loadedItems.Count} 个物品");
    Console.WriteLine();
    
    if (loadedItems.Count > 0)
    {
        var loadedItem = loadedItems[0];
        Console.WriteLine("✓ 验证加载的物品数据:");
        Console.WriteLine($"  ID: {loadedItem.Id}");
        Console.WriteLine($"  名称: {loadedItem.Name}");
        Console.WriteLine($"  描述: {loadedItem.Desc}");
        Console.WriteLine($"  类型: {loadedItem.ArmorType}");
        Console.WriteLine($"  价格: {loadedItem.Price}");
        Console.WriteLine($"  装备部位: {loadedItem.EquipmentSlots}");
        Console.WriteLine($"  战士可用: {(loadedItem.Warrior == 1 ? "是" : "否")}");
        Console.WriteLine($"  最大强化值: {loadedItem.MaxEnchantValue}");
        Console.WriteLine();
        
        // 验证数据一致性
        bool dataMatches = loadedItem.Id == testItem.Id &&
                          loadedItem.Name == testItem.Name &&
                          loadedItem.Price == testItem.Price &&
                          loadedItem.MaxEnchantValue == testItem.MaxEnchantValue;
        
        Console.WriteLine($"✓ 数据一致性检查: {(dataMatches ? "通过" : "失败")}");
    }
    Console.WriteLine();

    // 测试现有XML文件加载
    Console.WriteLine("✓ 测试加载现有XML文件...");
    var existingFiles = new[]
    {
        "客户端/client_items_armor.xml",
        "客户端/client_items_etc.xml",
        "客户端/client_items_misc.xml"
    };

    foreach (var file in existingFiles)
    {
        if (File.Exists(file))
        {
            try
            {
                Console.WriteLine($"  正在加载: {file}");
                var items = await xmlService.LoadItemsAsync(file);
                Console.WriteLine($"    ✓ 成功加载 {items.Count} 个物品");
                
                if (items.Count > 0)
                {
                    var firstItem = items[0];
                    Console.WriteLine($"    第一个物品: {firstItem.DisplayName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ✗ 加载失败: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine($"  文件不存在: {file}");
        }
    }

    Console.WriteLine();
    Console.WriteLine("=================================");
    Console.WriteLine("✓ 所有测试完成！");
    Console.WriteLine("=================================");
    Console.WriteLine();
    Console.WriteLine("核心功能验证:");
    Console.WriteLine("  ✓ XML数据模型定义");
    Console.WriteLine("  ✓ XML文件读取和解析");
    Console.WriteLine("  ✓ XML文件写入和格式化");
    Console.WriteLine("  ✓ 数据验证");
    Console.WriteLine("  ✓ 中文注释和属性映射");
    Console.WriteLine();
    Console.WriteLine("这个核心库可以用于:");
    Console.WriteLine("  • WPF桌面应用程序");
    Console.WriteLine("  • Web应用程序");
    Console.WriteLine("  • 控制台工具");
    Console.WriteLine("  • 批处理脚本");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 测试过程中出现错误: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

Console.WriteLine();
Console.WriteLine("按任意键退出...");
Console.ReadKey();
