<Window x:Class="SimpleWpfEditor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Aion物品编辑器 - 简化版" 
        Height="700" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#FAFAFA"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="#2196F3" Padding="16,12" CornerRadius="8" Margin="0,0,0,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="OpenFileButton" 
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="OpenFileButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderOpen" Margin="0,0,8,0"/>
                            <TextBlock Text="打开文件"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="TitleManagerButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="TitleManagerButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Trophy" Margin="0,0,8,0"/>
                            <TextBlock Text="称号管理"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SkillManagerButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#9C27B0"
                            Click="SkillManagerButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magic" Margin="0,0,8,0"/>
                            <TextBlock Text="技能管理"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="NpcManagerButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#FF5722"
                            Click="NpcManagerButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountGroup" Margin="0,0,8,0"/>
                            <TextBlock Text="NPC管理"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ServerMonsterButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#8BC34A"
                            Click="ServerMonsterButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Skull" Margin="0,0,8,0"/>
                            <TextBlock Text="服务端怪物掉落"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="NpcShopManagerButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="NpcShopManagerButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Store" Margin="0,0,8,0"/>
                            <TextBlock Text="商店管理"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="NpcTradeManagerButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="NpcTradeManagerButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="SwapHorizontal" Margin="0,0,8,0"/>
                            <TextBlock Text="交易管理"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SaveFileButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="SaveFileButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                            <TextBlock Text="保存文件"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SaveWithServerConversionButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#FF5722"
                            Click="SaveWithServerConversionButton_Click"
                            Margin="0,0,8,0"
                            ToolTip="只转换修改过的物品到服务端格式，不修改客户端文件">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Sync" Margin="0,0,8,0"/>
                            <TextBlock Text="🔄 保存并转换服务端"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ClearModifiedButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Background="White"
                            Foreground="#9C27B0"
                            Click="ClearModifiedButton_Click"
                            Margin="0,0,8,0"
                            ToolTip="清除所有物品的修改标记，重置修改状态">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Margin="0,0,8,0"/>
                            <TextBlock Text="🧹 清除修改标记"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AddItemButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="AddItemButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                            <TextBlock Text="新增物品"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="DeleteItemButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="#F44336"
                            Foreground="White"
                            Click="DeleteItemButton_Click"
                            Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Margin="0,0,8,0"/>
                            <TextBlock Text="删除物品"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="GlobalSearchButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="White"
                            Foreground="#2196F3"
                            Click="GlobalSearchButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="DatabaseSearch" Margin="0,0,8,0"/>
                            <TextBlock Text="全局搜索"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <TextBlock Grid.Column="1" 
                          x:Name="FileNameText"
                          Text="未打开文件"
                          Foreground="White"
                          FontSize="16"
                          FontWeight="Medium"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>

                <TextBlock Grid.Column="2" 
                          x:Name="ItemCountText"
                          Text="物品数量: 0"
                          Foreground="White"
                          FontSize="14"
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="8"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧物品列表 -->
            <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="16">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="物品列表" FontSize="18" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,16"/>
                    
                    <TextBox Grid.Row="1" 
                            x:Name="SearchTextBox"
                            materialDesign:HintAssist.Hint="搜索物品名称或ID..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            TextChanged="SearchTextBox_TextChanged"
                            Margin="0,0,0,16"/>

                    <DataGrid Grid.Row="2"
                             x:Name="ItemsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="False"
                             CanUserResizeRows="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             SelectionChanged="ItemsDataGrid_SelectionChanged"
                             MouseDoubleClick="ItemsDataGrid_MouseDoubleClick"
                             Style="{StaticResource MaterialDesignDataGrid}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                            <DataGridTextColumn Header="内部名称" Binding="{Binding Name}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="显示名称" Binding="{Binding Desc}" Width="*" IsReadOnly="True"/>
                            <DataGridTextColumn Header="类型" Binding="{Binding ArmorType}" Width="100" IsReadOnly="True"/>
                        </DataGrid.Columns>

                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="编辑物品" Click="EditItemMenuItem_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Edit"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="复制物品" Click="CopyItemMenuItem_Click">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="ContentCopy"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="删除物品" Click="DeleteItemMenuItem_Click" Foreground="#F44336">
                                    <MenuItem.Icon>
                                        <materialDesign:PackIcon Kind="Delete" Foreground="#F44336"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- 右侧编辑区域 -->
            <Border Grid.Column="2" Background="White" CornerRadius="8" Padding="16">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" 
                              x:Name="EditTitleText"
                              Text="选择一个物品进行编辑" 
                              FontSize="18" 
                              FontWeight="SemiBold" 
                              Foreground="#2196F3" 
                              Margin="0,0,0,16"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="EditPanel" Visibility="Collapsed">
                            <!-- 本地化信息 -->
                            <Border Background="#E3F2FD" CornerRadius="4" Padding="12" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="本地化信息" FontSize="16" FontWeight="SemiBold" Foreground="#1976D2" Margin="0,0,0,12"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="本地化名称:" FontWeight="Medium" Margin="0,0,8,4" VerticalAlignment="Top"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="LocalizedNameText" Text="未加载" TextWrapping="Wrap" Margin="0,0,0,8"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="描述键:" FontWeight="Medium" Margin="0,0,8,4" VerticalAlignment="Top"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="DescKeyText" Text="未选择" TextWrapping="Wrap" FontFamily="Consolas"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 基础信息 -->
                            <Border Background="#F5F5F5" CornerRadius="4" Padding="12" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础信息" FontSize="16" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,12"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="物品ID" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <TextBox x:Name="IdTextBox" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                        </StackPanel>

                                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="物品名称" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <TextBox x:Name="NameTextBox" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                        </StackPanel>

                                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,8,8,0">
                                            <TextBlock Text="描述键" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <TextBox x:Name="DescTextBox" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                        </StackPanel>

                                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="8,8,0,0">
                                            <TextBlock Text="护甲类型" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <ComboBox x:Name="ArmorTypeComboBox" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                                <ComboBoxItem Content="clothes"/>
                                                <ComboBoxItem Content="leather"/>
                                                <ComboBoxItem Content="chain"/>
                                                <ComboBoxItem Content="plate"/>
                                                <ComboBoxItem Content="weapon"/>
                                            </ComboBox>
                                        </StackPanel>

                                        <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,8,8,0">
                                            <TextBlock Text="价格" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <TextBox x:Name="PriceTextBox" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                        </StackPanel>

                                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="8,8,0,0">
                                            <TextBlock Text="最大堆叠数量" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <TextBox x:Name="MaxStackCountTextBox" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 职业限制 -->
                            <Border Background="#F5F5F5" CornerRadius="4" Padding="12" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="职业限制" FontSize="16" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,12"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <CheckBox Grid.Row="0" Grid.Column="0" x:Name="WarriorCheckBox" Content="战士" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="0" Grid.Column="1" x:Name="ScoutCheckBox" Content="侦察兵" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="0" Grid.Column="2" x:Name="MageCheckBox" Content="法师" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="0" Grid.Column="3" x:Name="ClericCheckBox" Content="牧师" Style="{StaticResource MaterialDesignCheckBox}"/>

                                        <CheckBox Grid.Row="1" Grid.Column="0" x:Name="FighterCheckBox" Content="格斗家" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="1" Grid.Column="1" x:Name="KnightCheckBox" Content="骑士" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="1" Grid.Column="2" x:Name="AssassinCheckBox" Content="刺客" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="1" Grid.Column="3" x:Name="RangerCheckBox" Content="游侠" Style="{StaticResource MaterialDesignCheckBox}"/>

                                        <CheckBox Grid.Row="2" Grid.Column="0" x:Name="WizardCheckBox" Content="魔导星" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="2" Grid.Column="1" x:Name="ElementalistCheckBox" Content="精灵星" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="2" Grid.Column="2" x:Name="ChanterCheckBox" Content="护法星" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="2" Grid.Column="3" x:Name="PriestCheckBox" Content="治愈星" Style="{StaticResource MaterialDesignCheckBox}"/>

                                        <CheckBox Grid.Row="3" Grid.Column="0" x:Name="GunnerCheckBox" Content="枪炮兵" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="3" Grid.Column="1" x:Name="BardCheckBox" Content="吟游诗人" Style="{StaticResource MaterialDesignCheckBox}"/>
                                        <CheckBox Grid.Row="3" Grid.Column="2" x:Name="RiderCheckBox" Content="驭灵师" Style="{StaticResource MaterialDesignCheckBox}"/>
                                    </Grid>

                                    <StackPanel Orientation="Horizontal" Margin="0,12,0,0">
                                        <Button x:Name="AllowAllClassesButton" 
                                                Content="允许所有职业" 
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Click="AllowAllClassesButton_Click"
                                                Margin="0,0,8,0"/>
                                        <Button x:Name="DenyAllClassesButton" 
                                                Content="禁止所有职业" 
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Click="DenyAllClassesButton_Click"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                        <Button x:Name="ResetButton" 
                                Content="重置" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="ResetButton_Click"
                                Margin="0,0,8,0"/>
                        <Button x:Name="SaveChangesButton" 
                                Content="保存更改" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#2196F3"
                                Click="SaveChangesButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="16,8" Margin="0,16,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="1" BlurRadius="4"/>
            </Border.Effect>
            
            <TextBlock x:Name="StatusText" 
                      Text="就绪" 
                      VerticalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
