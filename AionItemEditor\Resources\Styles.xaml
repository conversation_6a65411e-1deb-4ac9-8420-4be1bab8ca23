<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 分组标题样式 -->
    <Style x:Key="GroupHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="0,16,0,8"/>
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="LabelStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 输入框样式 -->
    <Style x:Key="InputStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 数字输入框样式 -->
    <Style x:Key="NumberInputStyle" TargetType="TextBox" BasedOn="{StaticResource InputStyle}">
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="CheckBoxStyle" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

</ResourceDictionary>
