using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;

using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 技能管理窗口
    /// </summary>
    public partial class SkillManagerWindow : Window
    {
        private readonly SkillXmlService _xmlService;
    private readonly XmlService _mainXmlService;
        private readonly ILogger<SkillManagerWindow> _logger;
        private readonly ObservableCollection<ClientSkill> _skills;
        private readonly CollectionViewSource _filteredSkills;
        private string? _currentFilePath;
        private ClientSkill? _currentEditingSkill;

        public SkillManagerWindow(ILogger<SkillManagerWindow> logger, SkillXmlService xmlService, XmlService mainXmlService)
        {
            InitializeComponent();

            // 设置服务
            _logger = logger;
            _xmlService = xmlService;
            _mainXmlService = mainXmlService;

            // 初始化集合
            _skills = new ObservableCollection<ClientSkill>();
            _filteredSkills = new CollectionViewSource { Source = _skills };
            _filteredSkills.Filter += FilterSkills;

            // 设置数据源
            SkillsDataGrid.ItemsSource = _filteredSkills.View;

            // 绑定事件
            BindEvents();

            // 初始化界面
            UpdateSkillCounts();
            UpdateStatus("就绪");

            _logger.LogInformation("技能管理器已启动");
        }

        /// <summary>
        /// 绑定事件
        /// </summary>
        private void BindEvents()
        {
            // 工具栏按钮事件
            OpenFileButton.Click += OpenFileButton_Click;
            SaveFileButton.Click += SaveFileButton_Click;
            SaveWithServerConversionButton.Click += SaveWithServerConversionButton_Click;
            
            // 搜索和筛选事件
            SearchTextBox.TextChanged += (s, e) => _filteredSkills.View.Refresh();
            SkillTypeFilterComboBox.SelectionChanged += (s, e) => _filteredSkills.View.Refresh();
            LevelFilterComboBox.SelectionChanged += (s, e) => _filteredSkills.View.Refresh();
            ClearFilterButton.Click += ClearFilterButton_Click;
            
            // 技能操作事件
            AddSkillButton.Click += AddSkillButton_Click;
            DeleteSkillButton.Click += DeleteSkillButton_Click;
            
            // 数据网格事件
            SkillsDataGrid.MouseDoubleClick += SkillsDataGrid_MouseDoubleClick;
            SkillsDataGrid.KeyDown += SkillsDataGrid_KeyDown;
        }

        /// <summary>
        /// 打开文件按钮点击
        /// </summary>
        private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "打开技能文件",
                    Filter = "技能文件 (client_skills.xml)|client_skills.xml|XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    await LoadSkillsFile(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件时出错");
                MessageBox.Show($"打开文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载技能文件
        /// </summary>
        private async Task LoadSkillsFile(string filePath)
        {
            try
            {
                UpdateStatus("正在加载技能文件...");

                _logger.LogInformation("开始加载技能文件: {FilePath}", filePath);

                // 首先尝试使用专门的SkillXmlService
                var skills = await _xmlService.LoadSkillsFromXmlAsync(filePath);

                // 如果没有加载到技能，尝试使用主界面的XmlService
                if (skills.Count == 0)
                {
                    _logger.LogInformation("SkillXmlService未加载到技能，尝试使用主界面XmlService");
                    var items = await _mainXmlService.LoadItemsAsync(filePath);

                    // 将ClientItem转换为ClientSkill
                    foreach (var item in items)
                    {
                        // 所有物品都当作技能处理
                        var skill = new ClientSkill
                        {
                            Id = item.Id,
                            Name = item.Name,
                            Desc = item.Desc,
                            SkillType = "ACTIVE", // 默认值
                            Level = 1,
                            MaxLevel = 1
                        };
                        skills.Add(skill);
                    }
                }

                _logger.LogInformation("加载到 {Count} 个技能", skills.Count);

                _skills.Clear();
                foreach (var skill in skills)
                {
                    _skills.Add(skill);
                }

                _currentFilePath = filePath;
                UpdateSkillCounts();
                UpdateStatus($"成功加载 {skills.Count} 个技能");
                Title = $"技能管理器 - {Path.GetFileName(filePath)}";

                // 显示加载结果
                if (skills.Count > 0)
                {
                    MessageBox.Show($"成功加载 {skills.Count} 个技能！", "加载成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("文件中没有找到技能数据，请检查XML格式是否正确。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载技能文件时出错: {FilePath}", filePath);
                MessageBox.Show($"加载技能文件时出错:\n\n{ex.Message}\n\n详细信息:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("加载文件失败");
            }
        }

        /// <summary>
        /// 保存文件按钮点击
        /// </summary>
        private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentFilePath))
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Title = "保存技能文件",
                        Filter = "技能文件 (client_skills.xml)|client_skills.xml|XML文件 (*.xml)|*.xml",
                        DefaultExt = "xml",
                        FileName = "client_skills.xml"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        _currentFilePath = saveFileDialog.FileName;
                    }
                    else
                    {
                        return;
                    }
                }

                UpdateStatus("正在保存技能文件...");
                await _xmlService.SaveSkillsToXmlAsync(_currentFilePath, _skills.ToList());
                UpdateStatus($"已保存 {_skills.Count} 个技能");
                
                Title = $"技能管理器 - {Path.GetFileName(_currentFilePath)}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存技能文件时出错");
                MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("保存文件失败");
            }
        }

        /// <summary>
        /// 保存并转换服务端按钮点击
        /// </summary>
        private async void SaveWithServerConversionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_skills.Count == 0)
                {
                    MessageBox.Show("没有技能数据需要转换", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 选择服务端文件保存目录
                var folderDialog = new OpenFileDialog
                {
                    Title = "选择服务端文件保存目录",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "选择文件夹",
                    Filter = "文件夹|*.folder"
                };

                if (folderDialog.ShowDialog() == true)
                {
                    var serverBasePath = Path.GetDirectoryName(folderDialog.FileName) ?? "";

                    UpdateStatus("正在转换技能数据到服务端格式...");

                    var success = await _xmlService.SaveWithServerConversionAsync(_currentFilePath ?? "", _skills.ToList(), serverBasePath);

                    if (success)
                    {
                        var message = $"技能数据转换完成！\n\n" +
                                      $"技能总数: {_skills.Count}\n" +
                                      $"文件保存位置: {Path.Combine(serverBasePath, "skills.xml")}\n\n" +
                                      $"使用服务端根标签: <skills>";

                        MessageBox.Show(message, "转换成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        UpdateStatus("服务端转换完成");
                    }
                    else
                    {
                        MessageBox.Show("转换失败，请检查日志", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        UpdateStatus("服务端转换失败");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换技能到服务端格式时出错");
                MessageBox.Show($"转换过程中出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("服务端转换失败");
            }
        }

        /// <summary>
        /// 清除筛选按钮点击
        /// </summary>
        private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = string.Empty;
            SkillTypeFilterComboBox.SelectedIndex = 0;
            LevelFilterComboBox.SelectedIndex = 0;
            _filteredSkills.View.Refresh();
            UpdateStatus("已清除所有筛选条件");
        }

        /// <summary>
        /// 添加技能按钮点击
        /// </summary>
        private void AddSkillButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 如果没有技能，先加载测试数据
                if (_skills.Count == 0)
                {
                    LoadTestData();
                    return;
                }

                var newId = _skills.Count > 0 ? _skills.Max(s => s.Id) + 1 : 1;
                var newSkill = new ClientSkill
                {
                    Id = newId,
                    Name = $"新技能_{newId}",
                    Desc = "新技能描述",
                    SkillType = "ACTIVE",
                    Level = 1,
                    MaxLevel = 1,
                    TargetType = "ENEMY",
                    TargetMaxCount = 1
                };

                var editor = new SkillEditorWindow(newSkill);
                editor.Owner = this;

                if (editor.ShowDialog() == true)
                {
                    _skills.Add(editor.EditedSkill);
                    UpdateSkillCounts();
                    UpdateStatus($"已添加技能: {editor.EditedSkill.Name}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加技能时出错");
                MessageBox.Show($"添加技能时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载测试数据
        /// </summary>
        private void LoadTestData()
        {
            try
            {
                var testSkills = new List<ClientSkill>
                {
                    new ClientSkill
                    {
                        Id = 1001,
                        Name = "火球术",
                        Desc = "发射一个火球攻击敌人",
                        SkillType = "ACTIVE",
                        Level = 1,
                        MaxLevel = 5,
                        MpConsume = 50,
                        CastTime = 2000,
                        CoolTime = 5000,
                        TargetType = "ENEMY",
                        TargetRange = "20",
                        TargetMaxCount = 1,
                        EffectType = "DAMAGE",
                        EffectValue = 100,
                        Animation = "fireball_cast",
                        Icon = "skill_fireball"
                    },
                    new ClientSkill
                    {
                        Id = 1002,
                        Name = "治疗术",
                        Desc = "恢复目标的生命值",
                        SkillType = "ACTIVE",
                        Level = 1,
                        MaxLevel = 3,
                        MpConsume = 30,
                        CastTime = 1500,
                        CoolTime = 3000,
                        TargetType = "ALLY",
                        TargetRange = "15",
                        TargetMaxCount = 1,
                        EffectType = "HEAL",
                        EffectValue = 80,
                        Animation = "heal_cast",
                        Icon = "skill_heal"
                    },
                    new ClientSkill
                    {
                        Id = 1003,
                        Name = "力量增强",
                        Desc = "永久增加角色的力量属性",
                        SkillType = "PASSIVE",
                        Level = 1,
                        MaxLevel = 10,
                        TargetType = "SELF",
                        EffectType = "STAT_BOOST",
                        EffectValue = 5,
                        Icon = "skill_strength"
                    }
                };

                _skills.Clear();
                foreach (var skill in testSkills)
                {
                    _skills.Add(skill);
                }

                UpdateSkillCounts();
                UpdateStatus($"已加载 {testSkills.Count} 个测试技能");
                MessageBox.Show($"已加载 {testSkills.Count} 个测试技能！\n\n现在您可以测试技能管理功能了。", "测试数据", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载测试数据时出错");
                MessageBox.Show($"加载测试数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 删除技能按钮点击
        /// </summary>
        private void DeleteSkillButton_Click(object sender, RoutedEventArgs e)
        {
            if (SkillsDataGrid.SelectedItem is ClientSkill selectedSkill)
            {
                var result = MessageBox.Show($"确定要删除技能 '{selectedSkill.Name}' 吗？",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _skills.Remove(selectedSkill);
                    UpdateSkillCounts();
                    UpdateStatus($"已删除技能: {selectedSkill.Name}");
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的技能", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 数据网格双击事件
        /// </summary>
        private void SkillsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (SkillsDataGrid.SelectedItem is ClientSkill selectedSkill)
            {
                EditSkill(selectedSkill);
            }
        }

        /// <summary>
        /// 数据网格键盘事件
        /// </summary>
        private void SkillsDataGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && SkillsDataGrid.SelectedItem is ClientSkill selectedSkill)
            {
                EditSkill(selectedSkill);
                e.Handled = true;
            }
            else if (e.Key == Key.Delete)
            {
                DeleteSkillButton_Click(sender, new RoutedEventArgs());
                e.Handled = true;
            }
        }

        /// <summary>
        /// 编辑技能菜单项点击
        /// </summary>
        private void EditSkillMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (SkillsDataGrid.SelectedItem is ClientSkill selectedSkill)
            {
                EditSkill(selectedSkill);
            }
        }

        /// <summary>
        /// 复制技能菜单项点击
        /// </summary>
        private void CopySkillMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (SkillsDataGrid.SelectedItem is not ClientSkill selectedSkill)
                return;

            try
            {
                var newId = _skills.Count > 0 ? _skills.Max(s => s.Id) + 1 : 1;
                var copiedSkill = new ClientSkill
                {
                    Id = newId,
                    Name = $"{selectedSkill.Name}_副本",
                    Desc = selectedSkill.Desc,
                    SkillType = selectedSkill.SkillType,
                    Level = selectedSkill.Level,
                    MaxLevel = selectedSkill.MaxLevel,
                    MpConsume = selectedSkill.MpConsume,
                    HpConsume = selectedSkill.HpConsume,
                    DpConsume = selectedSkill.DpConsume,
                    CastTime = selectedSkill.CastTime,
                    CoolTime = selectedSkill.CoolTime,
                    Duration = selectedSkill.Duration,
                    TargetType = selectedSkill.TargetType,
                    TargetRange = selectedSkill.TargetRange,
                    TargetMaxCount = selectedSkill.TargetMaxCount,
                    EffectType = selectedSkill.EffectType,
                    EffectValue = selectedSkill.EffectValue,
                    Animation = selectedSkill.Animation,
                    Icon = selectedSkill.Icon
                };

                _skills.Add(copiedSkill);
                UpdateSkillCounts();
                UpdateStatus($"已复制技能: {copiedSkill.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制技能时出错");
                MessageBox.Show($"复制技能时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除技能菜单项点击
        /// </summary>
        private void DeleteSkillMenuItem_Click(object sender, RoutedEventArgs e)
        {
            DeleteSkillButton_Click(sender, e);
        }

        /// <summary>
        /// 编辑技能
        /// </summary>
        private void EditSkill(ClientSkill skill)
        {
            try
            {
                var editor = new SkillEditorWindow(skill);
                editor.Owner = this;

                if (editor.ShowDialog() == true && editor.DataChanged)
                {
                    // 更新原始技能数据
                    UpdateSkillData(skill, editor.EditedSkill);
                    UpdateStatus($"已更新技能: {skill.Name}");

                    // 刷新显示
                    _filteredSkills.View.Refresh();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "编辑技能时出错");
                MessageBox.Show($"编辑技能时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新技能数据
        /// </summary>
        private void UpdateSkillData(ClientSkill original, ClientSkill edited)
        {
            original.Id = edited.Id;
            original.Name = edited.Name;
            original.Desc = edited.Desc;
            original.SkillType = edited.SkillType;
            original.Level = edited.Level;
            original.MaxLevel = edited.MaxLevel;
            original.MpConsume = edited.MpConsume;
            original.HpConsume = edited.HpConsume;
            original.DpConsume = edited.DpConsume;
            original.CastTime = edited.CastTime;
            original.CoolTime = edited.CoolTime;
            original.Duration = edited.Duration;
            original.TargetType = edited.TargetType;
            original.TargetRange = edited.TargetRange;
            original.TargetMaxCount = edited.TargetMaxCount;
            original.EffectType = edited.EffectType;
            original.EffectValue = edited.EffectValue;
            original.Animation = edited.Animation;
            original.Icon = edited.Icon;

            // 标记技能已被修改
            original.IsModified = true;
        }

        /// <summary>
        /// 筛选技能
        /// </summary>
        private void FilterSkills(object sender, FilterEventArgs e)
        {
            if (e.Item is not ClientSkill skill)
            {
                e.Accepted = false;
                return;
            }

            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            var skillTypeFilter = (SkillTypeFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "";
            var levelFilter = (LevelFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "";

            // 搜索筛选
            if (!string.IsNullOrEmpty(searchText))
            {
                var matchesSearch = skill.Name.ToLower().Contains(searchText) ||
                                   skill.Id.ToString().Contains(searchText) ||
                                   skill.Desc.ToLower().Contains(searchText);
                if (!matchesSearch)
                {
                    e.Accepted = false;
                    return;
                }
            }

            // 技能类型筛选
            if (!string.IsNullOrEmpty(skillTypeFilter) && skill.SkillType != skillTypeFilter)
            {
                e.Accepted = false;
                return;
            }

            // 等级筛选
            if (!string.IsNullOrEmpty(levelFilter) && skill.Level.ToString() != levelFilter)
            {
                e.Accepted = false;
                return;
            }

            e.Accepted = true;
        }

        /// <summary>
        /// 更新技能数量显示
        /// </summary>
        private void UpdateSkillCounts()
        {
            TotalSkillsText.Text = _skills.Count.ToString();
            FilteredSkillsText.Text = _filteredSkills.View.Cast<object>().Count().ToString();
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
            UpdateSkillCounts();
        }

        #region 技能编辑功能

        /// <summary>
        /// 技能选择变化事件
        /// </summary>
        private void SkillsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SkillsDataGrid.SelectedItem is ClientSkill selectedSkill)
            {
                LoadSkillToEditor(selectedSkill);
                SkillEditPanel.IsEnabled = true;
            }
            else
            {
                ClearEditor();
                SkillEditPanel.IsEnabled = false;
            }
        }

        /// <summary>
        /// 将技能数据加载到编辑器
        /// </summary>
        private void LoadSkillToEditor(ClientSkill skill)
        {
            _currentEditingSkill = skill;

            // 基础信息
            SkillIdTextBox.Text = skill.Id.ToString();
            SkillNameTextBox.Text = skill.Name;
            SkillDescTextBox.Text = skill.Desc;

            // 设置技能类型
            foreach (ComboBoxItem item in SkillTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == skill.SkillType)
                {
                    SkillTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            SkillLevelTextBox.Text = skill.Level.ToString();
            SkillMaxLevelTextBox.Text = skill.MaxLevel.ToString();

            // 消耗属性
            SkillMpConsumeTextBox.Text = skill.MpConsume.ToString();
            SkillHpConsumeTextBox.Text = skill.HpConsume.ToString();
            SkillDpConsumeTextBox.Text = skill.DpConsume.ToString();

            // 时间属性
            SkillCastTimeTextBox.Text = skill.CastTime.ToString();
            SkillCoolTimeTextBox.Text = skill.CoolTime.ToString();
            SkillDurationTextBox.Text = skill.Duration.ToString();

            // 目标属性
            foreach (ComboBoxItem item in SkillTargetTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == skill.TargetType)
                {
                    SkillTargetTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            SkillTargetRangeTextBox.Text = skill.TargetRange;
            SkillTargetMaxCountTextBox.Text = skill.TargetMaxCount.ToString();

            // 效果属性
            SkillEffectTypeTextBox.Text = skill.EffectType;
            SkillEffectValueTextBox.Text = skill.EffectValue.ToString();

            // 显示属性
            SkillAnimationTextBox.Text = skill.Animation;
            SkillIconTextBox.Text = skill.Icon;
        }

        /// <summary>
        /// 清空编辑器
        /// </summary>
        private void ClearEditor()
        {
            _currentEditingSkill = null;

            SkillIdTextBox.Text = "";
            SkillNameTextBox.Text = "";
            SkillDescTextBox.Text = "";
            SkillTypeComboBox.SelectedIndex = -1;
            SkillLevelTextBox.Text = "";
            SkillMaxLevelTextBox.Text = "";

            SkillMpConsumeTextBox.Text = "";
            SkillHpConsumeTextBox.Text = "";
            SkillDpConsumeTextBox.Text = "";

            SkillCastTimeTextBox.Text = "";
            SkillCoolTimeTextBox.Text = "";
            SkillDurationTextBox.Text = "";

            SkillTargetTypeComboBox.SelectedIndex = -1;
            SkillTargetRangeTextBox.Text = "";
            SkillTargetMaxCountTextBox.Text = "";

            SkillEffectTypeTextBox.Text = "";
            SkillEffectValueTextBox.Text = "";

            SkillAnimationTextBox.Text = "";
            SkillIconTextBox.Text = "";
        }

        /// <summary>
        /// 保存技能修改
        /// </summary>
        private void SaveSkillButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingSkill == null)
                return;

            try
            {
                // 验证并保存数据
                _currentEditingSkill.Name = SkillNameTextBox.Text;
                _currentEditingSkill.Desc = SkillDescTextBox.Text;

                if (SkillTypeComboBox.SelectedItem is ComboBoxItem skillTypeItem)
                    _currentEditingSkill.SkillType = skillTypeItem.Tag?.ToString() ?? "ACTIVE";

                if (int.TryParse(SkillLevelTextBox.Text, out int level))
                    _currentEditingSkill.Level = level;

                if (int.TryParse(SkillMaxLevelTextBox.Text, out int maxLevel))
                    _currentEditingSkill.MaxLevel = maxLevel;

                if (int.TryParse(SkillMpConsumeTextBox.Text, out int mpConsume))
                    _currentEditingSkill.MpConsume = mpConsume;

                if (int.TryParse(SkillHpConsumeTextBox.Text, out int hpConsume))
                    _currentEditingSkill.HpConsume = hpConsume;

                if (int.TryParse(SkillDpConsumeTextBox.Text, out int dpConsume))
                    _currentEditingSkill.DpConsume = dpConsume;

                if (int.TryParse(SkillCastTimeTextBox.Text, out int castTime))
                    _currentEditingSkill.CastTime = castTime;

                if (int.TryParse(SkillCoolTimeTextBox.Text, out int coolTime))
                    _currentEditingSkill.CoolTime = coolTime;

                if (int.TryParse(SkillDurationTextBox.Text, out int duration))
                    _currentEditingSkill.Duration = duration;

                if (SkillTargetTypeComboBox.SelectedItem is ComboBoxItem targetTypeItem)
                    _currentEditingSkill.TargetType = targetTypeItem.Tag?.ToString() ?? "ENEMY";

                _currentEditingSkill.TargetRange = SkillTargetRangeTextBox.Text;

                if (int.TryParse(SkillTargetMaxCountTextBox.Text, out int targetMaxCount))
                    _currentEditingSkill.TargetMaxCount = targetMaxCount;

                _currentEditingSkill.EffectType = SkillEffectTypeTextBox.Text;

                if (int.TryParse(SkillEffectValueTextBox.Text, out int effectValue))
                    _currentEditingSkill.EffectValue = effectValue;

                _currentEditingSkill.Animation = SkillAnimationTextBox.Text;
                _currentEditingSkill.Icon = SkillIconTextBox.Text;

                // 刷新显示
                _filteredSkills.View.Refresh();

                MessageBox.Show("技能修改已保存！", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                _logger.LogInformation("技能 {SkillId} - {SkillName} 修改已保存", _currentEditingSkill.Id, _currentEditingSkill.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存技能修改时出错");
                MessageBox.Show($"保存技能修改时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消编辑
        /// </summary>
        private void CancelEditButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingSkill != null)
            {
                // 重新加载原始数据
                LoadSkillToEditor(_currentEditingSkill);
                MessageBox.Show("已取消修改，恢复到原始数据。", "取消编辑", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        #endregion
    }
}
