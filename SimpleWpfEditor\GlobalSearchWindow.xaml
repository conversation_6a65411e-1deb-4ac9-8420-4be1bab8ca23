<Window x:Class="SimpleWpfEditor.GlobalSearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="全局物品搜索" 
        Height="700" 
        Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="#FAFAFA"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <StackPanel>
                <TextBlock Text="全局物品搜索" FontSize="18" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,16"/>
                <TextBlock Text="支持搜索：物品ID、内部名称、本地化名称" FontSize="12" Foreground="Gray" Margin="0,0,0,12"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0"
                            x:Name="SearchTextBox"
                            materialDesign:HintAssist.Hint="输入物品ID、名称或中文名称..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            KeyDown="SearchTextBox_KeyDown"
                            Margin="0,0,8,0"/>
                    
                    <Button Grid.Column="1"
                            x:Name="SearchButton"
                            Content="搜索"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="#2196F3"
                            Click="SearchButton_Click"
                            Margin="0,0,8,0"/>
                    
                    <Button Grid.Column="2"
                            x:Name="ClearButton"
                            Content="清空"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="ClearButton_Click"/>
                </Grid>
                
                <StackPanel Orientation="Horizontal" Margin="0,12,0,0">
                    <TextBlock x:Name="ResultCountText" 
                              Text="输入关键词开始搜索" 
                              FontSize="12" 
                              Foreground="Gray"
                              Margin="0,0,16,0"/>
                    
                    <TextBlock x:Name="StatsText" 
                              Text="" 
                              FontSize="12" 
                              Foreground="#2196F3"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 搜索结果 -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="16">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <DataGrid x:Name="ResultsDataGrid"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     CanUserReorderColumns="False"
                     CanUserResizeRows="False"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     SelectionMode="Single"
                     MouseDoubleClick="ResultsDataGrid_MouseDoubleClick"
                     Style="{StaticResource MaterialDesignDataGrid}">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="物品ID" Binding="{Binding Item.Id}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="内部名称" Binding="{Binding Item.Name}" Width="200" IsReadOnly="True"/>
                    <DataGridTextColumn Header="本地化名称" Binding="{Binding LocalizedName}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextWrapping" Value="Wrap"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="类型" Binding="{Binding Item.ArmorType}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="匹配类型" Binding="{Binding MatchType}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontSize" Value="11"/>
                                <Setter Property="Foreground" Value="#666"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
                
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding MatchType}" Value="本地化名称">
                                <Setter Property="Background" Value="#E8F5E8"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding MatchType}" Value="ID">
                                <Setter Property="Background" Value="#FFF3E0"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Border>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="CancelButton" 
                    Content="取消" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="SelectButton" 
                    Content="选择并编辑" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="#2196F3"
                    Click="SelectButton_Click"
                    IsEnabled="False"/>
        </StackPanel>
    </Grid>
</Window>
