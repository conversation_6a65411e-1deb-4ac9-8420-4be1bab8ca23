﻿#pragma checksum "..\..\..\ProfessionalNpcManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C988A325BF33091FF830AB7859FBEE99EA05D9FB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// ProfessionalNpcManagerWindow
    /// </summary>
    public partial class ProfessionalNpcManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 69 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadButton;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox NpcListBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IdTextBox;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MeshTextBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DirTextBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NpcTitleTextBox;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NpcTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpTextBox;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxHpTextBox;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HpRegenTextBox;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScaleTextBox;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WeaponScaleTextBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhysicalAttackTextBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhysicalDefendTextBox;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagicalAttackTextBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagicalDefendTextBox;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinDamageTextBox;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxDamageTextBox;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HitAccuracyTextBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DodgeTextBox;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AiNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox IdleNameComboBox;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TribeTextBox;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RaceTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SensoryRangeTextBox;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SensoryAngleTextBox;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveNpcButton;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyNpcButton;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\ProfessionalNpcManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/professionalnpcmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LoadButton = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.LoadButton.Click += new System.Windows.RoutedEventHandler(this.LoadButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.AddButton.Click += new System.Windows.RoutedEventHandler(this.AddButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.DeleteButton.Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 79 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 80 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 80 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.NpcListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 111 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.NpcListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NpcListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.IdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.MeshTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.DirTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.NpcTitleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.NpcTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.LevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.ExpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.MaxHpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.HpRegenTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.ScaleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.WeaponScaleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.PhysicalAttackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.PhysicalDefendTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.MagicalAttackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.MagicalDefendTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.MinDamageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.MaxDamageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.HitAccuracyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.DodgeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.AiNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.IdleNameComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 31:
            this.TribeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.RaceTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.SensoryRangeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.SensoryAngleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.SaveNpcButton = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.SaveNpcButton.Click += new System.Windows.RoutedEventHandler(this.SaveNpcButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 394 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.CopyNpcButton = ((System.Windows.Controls.Button)(target));
            
            #line 396 "..\..\..\ProfessionalNpcManagerWindow.xaml"
            this.CopyNpcButton.Click += new System.Windows.RoutedEventHandler(this.CopyNpcButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.CountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

