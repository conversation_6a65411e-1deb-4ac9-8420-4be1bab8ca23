<UserControl x:Class="AionItemEditor.Views.ItemEditView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:AionItemEditor.ViewModels">

    <UserControl.DataContext>
        <viewModels:ItemEditViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部操作按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <Button Style="{StaticResource SecondaryButtonStyle}"
                    Command="{Binding SetAllClassPermissionsCommand}"
                    CommandParameter="1"
                    Content="允许所有职业"/>
            <Button Style="{StaticResource SecondaryButtonStyle}"
                    Command="{Binding SetAllClassPermissionsCommand}"
                    CommandParameter="0"
                    Content="禁止所有职业"/>
            <Button Style="{StaticResource SecondaryButtonStyle}"
                    Command="{Binding ResetToDefaultsCommand}"
                    Content="重置为默认值"/>
        </StackPanel>

        <!-- 编辑内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel DataContext="{Binding CurrentItem}">
                
                <!-- 基础信息组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="基础信息" Style="{StaticResource GroupHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="物品ID" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Id}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="物品名称" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Name}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="描述键" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Desc}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="护甲类型" Style="{StaticResource LabelStyle}"/>
                                <ComboBox SelectedValue="{Binding ArmorType}" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                    <ComboBoxItem Content="clothes"/>
                                    <ComboBoxItem Content="leather"/>
                                    <ComboBoxItem Content="chain"/>
                                    <ComboBoxItem Content="plate"/>
                                    <ComboBoxItem Content="robe"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="物品类型" Style="{StaticResource LabelStyle}"/>
                                <ComboBox SelectedValue="{Binding ItemType}" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                    <ComboBoxItem Content="normal"/>
                                    <ComboBoxItem Content="quest"/>
                                    <ComboBoxItem Content="abyss"/>
                                    <ComboBoxItem Content="devanion"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="材质" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Material}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="图标名称" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding IconName}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3" Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="装备部位" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding EquipmentSlots}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 经济属性组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="经济属性" Style="{StaticResource GroupHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="价格" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Price}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="4,0">
                                <TextBlock Text="最大堆叠数量" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding MaxStackCount}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                <TextBlock Text="可售给NPC" Style="{StaticResource LabelStyle}"/>
                                <CheckBox IsChecked="{Binding CanSellToNpc}" Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 存储权限组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="存储权限" Style="{StaticResource GroupHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox IsChecked="{Binding CanDepositToCharacterWarehouse}" 
                                         Content="可存入角色仓库" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="4,0">
                                <CheckBox IsChecked="{Binding CanDepositToAccountWarehouse}" 
                                         Content="可存入账户仓库" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                <CheckBox IsChecked="{Binding CanDepositToGuildWarehouse}" 
                                         Content="可存入军团仓库" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 物品属性组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="物品属性" Style="{StaticResource GroupHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox IsChecked="{Binding Breakable}" 
                                         Content="可损坏" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="4,0">
                                <CheckBox IsChecked="{Binding SoulBind}" 
                                         Content="灵魂绑定" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="8,0,0,0">
                                <CheckBox IsChecked="{Binding RemoveWhenLogout}" 
                                         Content="登出时移除" 
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="性别限制" Style="{StaticResource LabelStyle}"/>
                                <ComboBox SelectedValue="{Binding GenderPermitted}" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                    <ComboBoxItem Content="all"/>
                                    <ComboBoxItem Content="male"/>
                                    <ComboBoxItem Content="female"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="4,0">
                                <TextBlock Text="种族限制" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding RacePermitted}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 职业限制组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="职业限制" Style="{StaticResource GroupHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 基础职业 -->
                            <CheckBox Grid.Row="0" Grid.Column="0" IsChecked="{Binding Warrior, Converter={StaticResource IntToBoolConverter}}" Content="战士" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding Scout, Converter={StaticResource IntToBoolConverter}}" Content="侦察兵" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="0" Grid.Column="2" IsChecked="{Binding Mage, Converter={StaticResource IntToBoolConverter}}" Content="法师" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="0" Grid.Column="3" IsChecked="{Binding Cleric, Converter={StaticResource IntToBoolConverter}}" Content="牧师" Style="{StaticResource CheckBoxStyle}"/>

                            <!-- 进阶职业1 -->
                            <CheckBox Grid.Row="1" Grid.Column="0" IsChecked="{Binding Fighter, Converter={StaticResource IntToBoolConverter}}" Content="格斗家" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="1" Grid.Column="1" IsChecked="{Binding Knight, Converter={StaticResource IntToBoolConverter}}" Content="骑士" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="1" Grid.Column="2" IsChecked="{Binding Assassin, Converter={StaticResource IntToBoolConverter}}" Content="刺客" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="1" Grid.Column="3" IsChecked="{Binding Ranger, Converter={StaticResource IntToBoolConverter}}" Content="游侠" Style="{StaticResource CheckBoxStyle}"/>

                            <!-- 进阶职业2 -->
                            <CheckBox Grid.Row="2" Grid.Column="0" IsChecked="{Binding Wizard, Converter={StaticResource IntToBoolConverter}}" Content="魔导星" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="2" Grid.Column="1" IsChecked="{Binding Elementalist, Converter={StaticResource IntToBoolConverter}}" Content="精灵星" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="2" Grid.Column="2" IsChecked="{Binding Chanter, Converter={StaticResource IntToBoolConverter}}" Content="护法星" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="2" Grid.Column="3" IsChecked="{Binding Priest, Converter={StaticResource IntToBoolConverter}}" Content="治愈星" Style="{StaticResource CheckBoxStyle}"/>

                            <!-- 新职业 -->
                            <CheckBox Grid.Row="3" Grid.Column="0" IsChecked="{Binding Engineer, Converter={StaticResource IntToBoolConverter}}" Content="工程师" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="3" Grid.Column="1" IsChecked="{Binding Artist, Converter={StaticResource IntToBoolConverter}}" Content="艺术家" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="3" Grid.Column="2" IsChecked="{Binding Gunner, Converter={StaticResource IntToBoolConverter}}" Content="枪炮兵" Style="{StaticResource CheckBoxStyle}"/>
                            <CheckBox Grid.Row="3" Grid.Column="3" IsChecked="{Binding Bard, Converter={StaticResource IntToBoolConverter}}" Content="吟游诗人" Style="{StaticResource CheckBoxStyle}"/>

                            <!-- 最新职业 -->
                            <CheckBox Grid.Row="4" Grid.Column="0" IsChecked="{Binding Rider, Converter={StaticResource IntToBoolConverter}}" Content="驭灵师" Style="{StaticResource CheckBoxStyle}"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 强化属性组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="强化属性" Style="{StaticResource GroupHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="选项槽数值" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding OptionSlotValue}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="4,0">
                                <TextBlock Text="特殊槽数值" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding SpecialSlotValue}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="8,0,0,0">
                                <TextBlock Text="选项槽奖励" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding OptionSlotBonus}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="奖励应用方式" Style="{StaticResource LabelStyle}"/>
                                <ComboBox SelectedValue="{Binding BonusApply}" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                    <ComboBoxItem Content="equip"/>
                                    <ComboBoxItem Content="inventory"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="4,0">
                                <TextBlock Text="最大强化值" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding MaxEnchantValue}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox IsChecked="{Binding NoEnchant}"
                                         Content="禁止强化"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="4,0">
                                <CheckBox IsChecked="{Binding CanProcEnchant}"
                                         Content="可触发强化"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="2" Margin="8,0,0,0">
                                <CheckBox IsChecked="{Binding CanCompositeWeapon}"
                                         Content="可合成武器"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 外观和特殊属性组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="外观和特殊属性" Style="{StaticResource GroupHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="UI音效类型" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding UiSoundType}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="4,0">
                                <TextBlock Text="模型" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Mesh}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="8,0,0,0">
                                <TextBlock Text="交易限制类别" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding BmRestrictCategory}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox IsChecked="{Binding CanSplit}"
                                         Content="可分割"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="4,0">
                                <CheckBox IsChecked="{Binding ItemDropPermitted}"
                                         Content="允许掉落"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="8,0,0,0">
                                <CheckBox IsChecked="{Binding CanApExtraction}"
                                         Content="可提取AP"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox IsChecked="{Binding CanPolish}"
                                         Content="可抛光"
                                         Style="{StaticResource CheckBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="4,0">
                                <TextBlock Text="攻击时抛光消耗" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding PolishBurnOnAttack}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="2" Margin="8,0,0,0">
                                <TextBlock Text="防御时抛光消耗" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding PolishBurnOnDefend}" Style="{StaticResource NumberInputStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
