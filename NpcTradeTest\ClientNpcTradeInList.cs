using System.Collections.Generic;
using System.Xml.Serialization;
using System.Linq;

namespace NpcTradeTest;

/// <summary>
/// NPC交易列表数据模型 - 基于真实的client_npc_trade_in_list.xml文件结构
/// </summary>
[XmlRoot("client_npc_trade_in_list")]
public class ClientNpcTradeInList
{
    /// <summary>
    /// 交易列表ID
    /// </summary>
    [XmlElement("id")]
    public int Id { get; set; }

    /// <summary>
    /// 交易列表内部名称
    /// </summary>
    [XmlElement("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 交易列表描述键
    /// </summary>
    [XmlElement("desc")]
    public string Desc { get; set; } = string.Empty;

    /// <summary>
    /// 使用类别
    /// </summary>
    [XmlElement("use_category")]
    public int UseCategory { get; set; } = 0;

    /// <summary>
    /// 商品列表
    /// </summary>
    [XmlArray("goods_list")]
    [XmlArrayItem("data")]
    public List<TradeInGoodsData> GoodsList { get; set; } = new List<TradeInGoodsData>();

    /// <summary>
    /// 显示名称（用于界面显示）
    /// </summary>
    [XmlIgnore]
    public string DisplayName => !string.IsNullOrEmpty(Desc) ? Desc : Name;

    /// <summary>
    /// 交易类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string TradeType
    {
        get
        {
            if (Name.Contains("weapon"))
                return "武器交易";
            else if (Name.Contains("armor"))
                return "防具交易";
            else if (Name.Contains("accessory"))
                return "饰品交易";
            else if (Name.Contains("material"))
                return "材料交易";
            else if (Name.Contains("potion"))
                return "药水交易";
            else if (Name.Contains("food"))
                return "食物交易";
            else if (Name.Contains("arena"))
                return "竞技场交易";
            else if (Name.Contains("test"))
                return "测试交易";
            else if (Name.Contains("coin"))
                return "货币交易";
            else
                return "通用交易";
        }
    }

    /// <summary>
    /// 武器类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string WeaponType
    {
        get
        {
            if (Name.Contains("1hand"))
                return "单手武器";
            else if (Name.Contains("2hand"))
                return "双手武器";
            else if (Name.Contains("range"))
                return "远程武器";
            else if (Name.Contains("magic"))
                return "法系武器";
            else if (Name.Contains("bow"))
                return "弓箭";
            else if (Name.Contains("staff"))
                return "法杖";
            else if (Name.Contains("orb"))
                return "法球";
            else if (Name.Contains("book"))
                return "魔导书";
            else
                return "通用";
        }
    }

    /// <summary>
    /// 防具类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string ArmorType
    {
        get
        {
            if (Name.Contains("robe"))
                return "法袍";
            else if (Name.Contains("leather"))
                return "皮甲";
            else if (Name.Contains("chain"))
                return "锁甲";
            else if (Name.Contains("plate"))
                return "板甲";
            else if (Name.Contains("shield"))
                return "盾牌";
            else
                return "通用";
        }
    }

    /// <summary>
    /// 阵营类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string FactionType
    {
        get
        {
            if (Name.Contains("_l_"))
                return "天族";
            else if (Name.Contains("_d_"))
                return "魔族";
            else
                return "通用";
        }
    }

    /// <summary>
    /// 等级类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string LevelType
    {
        get
        {
            if (Name.Contains("high"))
                return "高级";
            else if (Name.Contains("master"))
                return "大师级";
            else if (Name.Contains("expert"))
                return "专家级";
            else if (Name.Contains("elite"))
                return "精英级";
            else
                return "基础";
        }
    }

    /// <summary>
    /// 商品数量
    /// </summary>
    [XmlIgnore]
    public int ItemCount => GoodsList?.Count ?? 0;

    /// <summary>
    /// 商品摘要
    /// </summary>
    [XmlIgnore]
    public string ItemsSummary
    {
        get
        {
            if (GoodsList == null || GoodsList.Count == 0)
                return "无商品";

            var items = GoodsList.Take(3).Select(g => g.Item).Where(i => !string.IsNullOrEmpty(i));
            var summary = string.Join(", ", items);
            
            if (GoodsList.Count > 3)
                summary += $" 等{GoodsList.Count}件商品";
            
            return !string.IsNullOrEmpty(summary) ? summary : "无商品";
        }
    }

    /// <summary>
    /// 获取本地化显示名称
    /// </summary>
    public string GetLocalizedDisplayName(object? stringService = null)
    {
        return DisplayName; // 简化版本，直接返回显示名称
    }

    /// <summary>
    /// 获取商品类型统计
    /// </summary>
    public Dictionary<string, int> GetItemTypeStatistics()
    {
        var stats = new Dictionary<string, int>();
        
        foreach (var goods in GoodsList)
        {
            if (string.IsNullOrEmpty(goods.Item)) continue;
            
            var itemType = GetItemType(goods.Item);
            if (stats.ContainsKey(itemType))
                stats[itemType]++;
            else
                stats[itemType] = 1;
        }
        
        return stats;
    }

    /// <summary>
    /// 根据物品名称推断物品类型
    /// </summary>
    private string GetItemType(string itemName)
    {
        if (itemName.Contains("sword") || itemName.Contains("mace") || itemName.Contains("dagger"))
            return "近战武器";
        else if (itemName.Contains("bow") || itemName.Contains("gun"))
            return "远程武器";
        else if (itemName.Contains("staff") || itemName.Contains("orb") || itemName.Contains("book"))
            return "法系武器";
        else if (itemName.Contains("torso") || itemName.Contains("leg") || itemName.Contains("head"))
            return "防具";
        else if (itemName.Contains("shoes") || itemName.Contains("glove") || itemName.Contains("shoulder"))
            return "防具配件";
        else if (itemName.Contains("shield"))
            return "盾牌";
        else if (itemName.Contains("ring") || itemName.Contains("earring") || itemName.Contains("necklace"))
            return "饰品";
        else if (itemName.Contains("material"))
            return "材料";
        else if (itemName.Contains("potion"))
            return "药水";
        else if (itemName.Contains("food"))
            return "食物";
        else if (itemName.Contains("coin"))
            return "货币";
        else
            return "其他";
    }
}

/// <summary>
/// 交易商品数据
/// </summary>
public class TradeInGoodsData
{
    /// <summary>
    /// 商品物品ID
    /// </summary>
    [XmlElement("item")]
    public string Item { get; set; } = string.Empty;
}

/// <summary>
/// NPC交易列表统计信息
/// </summary>
public class NpcTradeInListStatistics
{
    public int TotalTradeLists { get; set; }
    public Dictionary<string, int> TradeTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> WeaponTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> ArmorTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> FactionTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> LevelTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> ItemTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<int, int> UseCategoryDistribution { get; set; } = new Dictionary<int, int>();
    public int TotalItems { get; set; }
    public double AverageItemsPerList { get; set; }
    public int MaxItemsInList { get; set; }
    public int MinItemsInList { get; set; }
}

/// <summary>
/// 交易列表搜索筛选条件
/// </summary>
public class TradeInListFilterCriteria
{
    public string? SearchText { get; set; }
    public string? TradeType { get; set; }
    public string? WeaponType { get; set; }
    public string? ArmorType { get; set; }
    public string? FactionType { get; set; }
    public string? LevelType { get; set; }
    public int? UseCategory { get; set; }
    public int? MinItems { get; set; }
    public int? MaxItems { get; set; }
}
