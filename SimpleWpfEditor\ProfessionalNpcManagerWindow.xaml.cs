using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 专业NPC管理器窗口
    /// </summary>
    public partial class ProfessionalNpcManagerWindow : Window
    {
        private NpcXmlService _xmlService = null!;
        private ObservableCollection<NpcClient> _npcs = null!;
        private CollectionViewSource _filteredNpcs = null!;
        private NpcClient? _currentEditingNpc;
        private bool _isUpdating = false;
        private readonly ObservableCollection<ClientItem>? _mainItems;
        private readonly XmlService? _mainXmlService;

        public ProfessionalNpcManagerWindow(ObservableCollection<ClientItem>? mainItems = null, XmlService? xmlService = null)
        {
            try
            {
                InitializeComponent();

                _mainItems = mainItems;
                _mainXmlService = xmlService;

                // 初始化服务
                var loggerFactory = LoggerFactory.Create(builder => { });
                var logger = loggerFactory.CreateLogger<NpcXmlService>();
                _xmlService = new NpcXmlService(logger);

                // 初始化集合
                _npcs = new ObservableCollection<NpcClient>();
                _filteredNpcs = new CollectionViewSource { Source = _npcs };

                // 绑定数据源
                if (NpcListBox != null)
                {
                    NpcListBox.ItemsSource = _filteredNpcs.View;
                }

                // 初始化搜索框
                if (SearchTextBox != null)
                {
                    SearchTextBox.Text = "搜索NPC...";
                    SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
                }

                UpdateCountDisplay();

                // 异步加载NPC数据
                _ = InitializeAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化NPC管理器时出错: {ex.Message}\n\n详细信息: {ex.StackTrace}",
                    "初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 异步初始化，自动加载NPC数据
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                // 首先尝试从主界面的数据中获取NPC
                if (_mainItems != null && _mainItems.Count > 0)
                {
                    StatusTextBlock.Text = "正在从主界面数据中加载NPC...";

                    // 将ClientItem转换为NpcClient（如果是NPC数据）
                    var npcCount = 0;
                    _npcs.Clear();

                    foreach (var item in _mainItems)
                    {
                        // 检查是否是NPC数据（通过某些特征判断）
                        if (IsNpcItem(item))
                        {
                            var npc = ConvertItemToNpc(item);
                            if (npc != null)
                            {
                                _npcs.Add(npc);
                                npcCount++;
                            }
                        }
                    }

                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已从主界面加载 {npcCount} 个NPC";
                    return;
                }

                // 如果主界面没有数据，尝试查找NPC数据文件
                var npcFiles = new[]
                {
                    @"data\static_data\npcs\npc_templates.xml",
                    @"data\npcs\npc_templates.xml",
                    @"npcs\npc_templates.xml",
                    @"npc_templates.xml",
                    @"data\static_data\npcs\npcs.xml",
                    @"data\npcs\npcs.xml",
                    @"npcs\npcs.xml",
                    @"npcs.xml"
                };

                string? foundFile = null;
                foreach (var file in npcFiles)
                {
                    if (System.IO.File.Exists(file))
                    {
                        foundFile = file;
                        break;
                    }
                }

                if (foundFile != null)
                {
                    StatusTextBlock.Text = "正在自动加载NPC数据...";

                    var npcs = await _xmlService.LoadNpcsFromXmlAsync(foundFile);

                    _npcs.Clear();
                    foreach (var npc in npcs)
                    {
                        _npcs.Add(npc);
                    }

                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已自动加载 {npcs.Count} 个NPC (来源: {foundFile})";
                }
                else
                {
                    StatusTextBlock.Text = "未找到NPC数据文件，请手动加载";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"自动加载NPC数据失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 判断ClientItem是否是NPC数据
        /// </summary>
        private bool IsNpcItem(ClientItem item)
        {
            // 通过一些特征判断是否是NPC数据
            // 这里可能需要根据实际的数据结构来调整判断逻辑
            return !string.IsNullOrEmpty(item.Name) &&
                   (item.Name.Contains("npc") ||
                    item.Name.Contains("monster") ||
                    item.Desc.Contains("NPC") ||
                    item.Level > 0); // 简单的判断逻辑
        }

        /// <summary>
        /// 将ClientItem转换为NpcClient
        /// </summary>
        private NpcClient? ConvertItemToNpc(ClientItem item)
        {
            try
            {
                return new NpcClient
                {
                    Id = item.Id,
                    Name = item.Name,
                    Desc = item.Desc,
                    Level = item.Level,
                    MaxHp = Math.Max(100, item.Level * 50), // 根据等级估算HP
                    NpcType = "Monster",
                    AiName = "NPC",
                    IdleName = "NoMove",
                    Scale = 100,
                    WeaponScale = 100
                };
            }
            catch
            {
                return null;
            }
        }

        #region 事件处理

        private async void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    Title = "选择NPC数据文件"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    StatusTextBlock.Text = "正在加载文件...";
                    
                    var npcs = await _xmlService.LoadNpcsFromXmlAsync(openFileDialog.FileName);
                    
                    _npcs.Clear();
                    foreach (var npc in npcs)
                    {
                        _npcs.Add(npc);
                    }
                    
                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已加载 {npcs.Count} 个NPC";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "加载失败";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_npcs.Count == 0)
                {
                    MessageBox.Show("没有数据可保存", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 保存当前编辑的NPC
                SaveCurrentNpc();

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    Title = "保存NPC数据文件"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusTextBlock.Text = "正在保存文件...";
                    
                    await _xmlService.SaveNpcsToXmlAsync(saveFileDialog.FileName, _npcs.ToList());
                    
                    StatusTextBlock.Text = $"已保存 {_npcs.Count} 个NPC";
                    MessageBox.Show("保存成功!", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "保存失败";
            }
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            var newNpc = new NpcClient
            {
                Id = GetNextAvailableId(),
                Name = "新NPC",
                Desc = "新NPC描述",
                Level = 1,
                MaxHp = 100,
                NpcType = "Monster",
                AiName = "NPC",
                IdleName = "NoMove"
            };

            _npcs.Add(newNpc);
            NpcListBox.SelectedItem = newNpc;
            UpdateCountDisplay();
            StatusTextBlock.Text = $"已添加新NPC ID: {newNpc.Id}";
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (NpcListBox.SelectedItem is NpcClient selectedNpc)
            {
                var result = MessageBox.Show($"确定要删除NPC '{selectedNpc.Name}' (ID: {selectedNpc.Id}) 吗？", 
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _npcs.Remove(selectedNpc);
                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已删除NPC: {selectedNpc.Name}";
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的NPC", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void NpcListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (NpcListBox.SelectedItem is NpcClient selectedNpc)
            {
                // 保存之前的NPC
                SaveCurrentNpc();
                
                // 加载新选择的NPC
                LoadNpcToEditor(selectedNpc);
                _currentEditingNpc = selectedNpc;
                DetailsPanel.IsEnabled = true;
            }
            else
            {
                DetailsPanel.IsEnabled = false;
                _currentEditingNpc = null;
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "搜索NPC...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "搜索NPC...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterNpcs();
        }

        private void SaveNpcButton_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentNpc();
            StatusTextBlock.Text = "NPC数据已保存";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingNpc != null)
            {
                LoadNpcToEditor(_currentEditingNpc);
                StatusTextBlock.Text = "已取消修改";
            }
        }

        private void CopyNpcButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingNpc != null)
            {
                var copiedNpc = new NpcClient
                {
                    Id = GetNextAvailableId(),
                    Name = _currentEditingNpc.Name + "_Copy",
                    Desc = _currentEditingNpc.Desc,
                    Level = _currentEditingNpc.Level,
                    MaxHp = _currentEditingNpc.MaxHp,
                    NpcType = _currentEditingNpc.NpcType,
                    AiName = _currentEditingNpc.AiName,
                    IdleName = _currentEditingNpc.IdleName,
                    Mesh = _currentEditingNpc.Mesh,
                    Dir = _currentEditingNpc.Dir,
                    Tribe = _currentEditingNpc.Tribe,
                    RaceType = _currentEditingNpc.RaceType
                };

                _npcs.Add(copiedNpc);
                NpcListBox.SelectedItem = copiedNpc;
                UpdateCountDisplay();
                StatusTextBlock.Text = $"已复制NPC: {copiedNpc.Name}";
            }
        }

        #endregion

        #region 私有方法

        private int GetNextAvailableId()
        {
            if (_npcs.Count == 0) return 1;
            return _npcs.Max(n => n.Id) + 1;
        }

        private void UpdateCountDisplay()
        {
            if (CountTextBlock != null && _npcs != null)
            {
                CountTextBlock.Text = $"NPC数量: {_npcs.Count}";
            }
        }

        private void FilterNpcs()
        {
            if (SearchTextBox == null || _filteredNpcs?.View == null) return;

            var searchText = SearchTextBox.Text;
            if (searchText == "搜索NPC..." || string.IsNullOrWhiteSpace(searchText))
            {
                _filteredNpcs.View.Filter = null;
            }
            else
            {
                _filteredNpcs.View.Filter = obj =>
                {
                    if (obj is NpcClient npc)
                    {
                        return (npc.Name?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               npc.Id.ToString().Contains(searchText) ||
                               (npc.Desc?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               (npc.NpcType?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               (npc.Tribe?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false);
                    }
                    return false;
                };
            }
        }

        private void LoadNpcToEditor(NpcClient npc)
        {
            _isUpdating = true;
            
            try
            {
                // 基础信息
                IdTextBox.Text = npc.Id.ToString();
                NameTextBox.Text = npc.Name;
                DescTextBox.Text = npc.Desc;
                MeshTextBox.Text = npc.Mesh;
                DirTextBox.Text = npc.Dir;
                NpcTitleTextBox.Text = npc.NpcTitle;
                NpcTypeComboBox.Text = npc.NpcType;
                
                // 基础属性
                LevelTextBox.Text = npc.Level.ToString();
                ExpTextBox.Text = npc.Exp.ToString();
                MaxHpTextBox.Text = npc.MaxHp.ToString();
                HpRegenTextBox.Text = npc.HpRegen.ToString();
                ScaleTextBox.Text = npc.Scale.ToString();
                WeaponScaleTextBox.Text = npc.WeaponScale.ToString();
                
                // 战斗属性
                PhysicalAttackTextBox.Text = npc.PhysicalAttack.ToString();
                PhysicalDefendTextBox.Text = npc.PhysicalDefend.ToString();
                MagicalAttackTextBox.Text = npc.MagicalAttack.ToString();
                MagicalDefendTextBox.Text = npc.MagicalDefend.ToString();
                MinDamageTextBox.Text = npc.MinDamage.ToString();
                MaxDamageTextBox.Text = npc.MaxDamage.ToString();
                HitAccuracyTextBox.Text = npc.HitAccuracy.ToString();
                DodgeTextBox.Text = npc.Dodge.ToString();
                
                // AI和行为
                AiNameTextBox.Text = npc.AiName;
                IdleNameComboBox.Text = npc.IdleName;
                TribeTextBox.Text = npc.Tribe;
                RaceTypeTextBox.Text = npc.RaceType;
                SensoryRangeTextBox.Text = npc.SensoryRange.ToString("F1");
                SensoryAngleTextBox.Text = npc.SensoryAngle.ToString();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void SaveCurrentNpc()
        {
            if (_currentEditingNpc == null || _isUpdating) return;
            
            try
            {
                // 基础信息
                if (int.TryParse(IdTextBox.Text, out int id)) _currentEditingNpc.Id = id;
                _currentEditingNpc.Name = NameTextBox.Text ?? "";
                _currentEditingNpc.Desc = DescTextBox.Text ?? "";
                _currentEditingNpc.Mesh = MeshTextBox.Text ?? "";
                _currentEditingNpc.Dir = DirTextBox.Text ?? "";
                _currentEditingNpc.NpcTitle = NpcTitleTextBox.Text ?? "";
                _currentEditingNpc.NpcType = NpcTypeComboBox.Text ?? "Monster";
                
                // 基础属性
                if (int.TryParse(LevelTextBox.Text, out int level)) _currentEditingNpc.Level = level;
                if (int.TryParse(ExpTextBox.Text, out int exp)) _currentEditingNpc.Exp = exp;
                if (int.TryParse(MaxHpTextBox.Text, out int maxHp)) _currentEditingNpc.MaxHp = maxHp;
                if (int.TryParse(HpRegenTextBox.Text, out int hpRegen)) _currentEditingNpc.HpRegen = hpRegen;
                if (int.TryParse(ScaleTextBox.Text, out int scale)) _currentEditingNpc.Scale = scale;
                if (int.TryParse(WeaponScaleTextBox.Text, out int weaponScale)) _currentEditingNpc.WeaponScale = weaponScale;
                
                // 战斗属性
                if (int.TryParse(PhysicalAttackTextBox.Text, out int physicalAttack)) _currentEditingNpc.PhysicalAttack = physicalAttack;
                if (int.TryParse(PhysicalDefendTextBox.Text, out int physicalDefend)) _currentEditingNpc.PhysicalDefend = physicalDefend;
                if (int.TryParse(MagicalAttackTextBox.Text, out int magicalAttack)) _currentEditingNpc.MagicalAttack = magicalAttack;
                if (int.TryParse(MagicalDefendTextBox.Text, out int magicalDefend)) _currentEditingNpc.MagicalDefend = magicalDefend;
                if (int.TryParse(MinDamageTextBox.Text, out int minDamage)) _currentEditingNpc.MinDamage = minDamage;
                if (int.TryParse(MaxDamageTextBox.Text, out int maxDamage)) _currentEditingNpc.MaxDamage = maxDamage;
                if (int.TryParse(HitAccuracyTextBox.Text, out int hitAccuracy)) _currentEditingNpc.HitAccuracy = hitAccuracy;
                if (int.TryParse(DodgeTextBox.Text, out int dodge)) _currentEditingNpc.Dodge = dodge;
                
                // AI和行为
                _currentEditingNpc.AiName = AiNameTextBox.Text ?? "NPC";
                _currentEditingNpc.IdleName = IdleNameComboBox.Text ?? "NoMove";
                _currentEditingNpc.Tribe = TribeTextBox.Text ?? "";
                _currentEditingNpc.RaceType = RaceTypeTextBox.Text ?? "";
                if (float.TryParse(SensoryRangeTextBox.Text, out float sensoryRange)) _currentEditingNpc.SensoryRange = sensoryRange;
                if (int.TryParse(SensoryAngleTextBox.Text, out int sensoryAngle)) _currentEditingNpc.SensoryAngle = sensoryAngle;
                
                // 刷新列表显示
                _filteredNpcs.View.Refresh();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"保存NPC数据时出错: {ex.Message}";
            }
        }

        #endregion
    }
}
