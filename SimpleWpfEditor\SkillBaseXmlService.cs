using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor;

/// <summary>
/// 专业技能基础数据XML服务 - 处理 skill_base_client 格式
/// </summary>
public class SkillBaseXmlService
{
    private readonly ILogger<SkillBaseXmlService> _logger;

    public SkillBaseXmlService(ILogger<SkillBaseXmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 从XML文件加载技能基础数据
    /// </summary>
    public async Task<List<SkillBaseClient>> LoadSkillsFromXmlAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载技能基础文件: {FilePath}", filePath);

            if (!System.IO.File.Exists(filePath))
            {
                throw new System.IO.FileNotFoundException($"技能文件不存在: {filePath}");
            }

            var skills = new List<SkillBaseClient>();
            var xmlDoc = new XmlDocument();
            
            // 读取XML文件
            var xmlContent = await System.IO.File.ReadAllTextAsync(filePath);
            xmlDoc.LoadXml(xmlContent);

            // 查找所有 skill_base_client 节点
            var skillNodes = xmlDoc.SelectNodes("//skill_base_client");
            if (skillNodes == null)
            {
                _logger.LogWarning("未找到 skill_base_client 节点");
                return skills;
            }

            _logger.LogInformation("找到 {Count} 个技能节点", skillNodes.Count);

            foreach (XmlNode skillNode in skillNodes)
            {
                try
                {
                    var skill = ParseSkillBaseClient(skillNode);
                    skills.Add(skill);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析技能时出错: {SkillNode}", skillNode?.OuterXml);
                }
            }

            _logger.LogInformation("成功加载 {Count} 个技能", skills.Count);
            return skills.OrderBy(s => s.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载技能文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 解析技能基础数据节点 - 完整版本
    /// </summary>
    private SkillBaseClient ParseSkillBaseClient(XmlNode skillNode)
    {
        var skill = new SkillBaseClient
        {
            // 基础信息
            Id = GetIntValue(skillNode, "id"),
            Name = GetStringValue(skillNode, "name"),
            Desc = GetStringValue(skillNode, "desc"),
            DescLong = GetStringValue(skillNode, "desc_long"),
            DescAbnormal = GetStringValue(skillNode, "desc_abnormal"),
            DescPrechainCategoryName = GetStringValue(skillNode, "desc_prechain_category_name"),
            Type = GetStringValue(skillNode, "type", "Physical"),
            SubType = GetStringValue(skillNode, "sub_type", "Attack"),
            SkillCategory = GetStringValue(skillNode, "skill_category", "SKILLCTG_NONE"),
            UltraSkill = GetIntValue(skillNode, "ultra_skill"),
            UltraTransfer = GetIntValue(skillNode, "ultra_transfer"),
            ChainCategoryLevel = GetIntValue(skillNode, "chain_category_level"),
            SkillGroupName = GetStringValue(skillNode, "skill_group_name"),
            SkillIconName = GetStringValue(skillNode, "skillicon_name"),
            DelayId = GetIntValue(skillNode, "delay_id"),
            ActivationAttribute = GetStringValue(skillNode, "activation_attribute", "Active"),

            // 消耗相关
            CostParameter = GetStringValue(skillNode, "cost_parameter", "MP"),
            CostEnd = GetIntValue(skillNode, "cost_end"),
            CostChargeWeapon = GetIntValue(skillNode, "cost_charge_weapon"),
            CostChargeArmor = GetIntValue(skillNode, "cost_charge_armor"),
            PolishChargeWeapon = GetIntValue(skillNode, "polish_charge_weapon"),
            PenaltyNoCastingTimeSucc = GetIntValue(skillNode, "penalty_no_casting_time_succ"),

            // 驱散相关
            DispelCategory = GetStringValue(skillNode, "dispel_category"),
            RequiredDispelLevel = GetIntValue(skillNode, "required_dispel_level"),

            // 目标相关
            TargetSlot = GetStringValue(skillNode, "target_slot", "none"),
            TargetStop = GetIntValue(skillNode, "target_stop"),
            HostileType = GetStringValue(skillNode, "hostile_type", "none"),
            DelayType = GetIntValue(skillNode, "delay_type"),
            DelayTime = GetIntValue(skillNode, "delay_time"),
            TargetMaxcount = GetIntValue(skillNode, "target_maxcount", 4),
            FirstTarget = GetStringValue(skillNode, "first_target", "Me"),
            FirstTargetValidDistance = GetIntValue(skillNode, "first_target_valid_distance", 6),
            TargetRange = GetStringValue(skillNode, "target_range"),
            TargetRangeAreaType = GetStringValue(skillNode, "target_range_area_type", "FireBall"),
            TargetRangeOpt1 = GetIntValue(skillNode, "target_range_opt1", 8),
            TargetRangeOpt3 = GetIntValue(skillNode, "target_range_opt3", 4),
            TargetSpeciesRestriction = GetStringValue(skillNode, "target_species_restriction", "All"),
            TargetRelationRestriction = GetStringValue(skillNode, "target_relation_restriction", "Enemy"),
            TargetMarkerRadius = GetIntValue(skillNode, "target_marker_radius"),

            // 行为控制
            CancelRate = GetIntValue(skillNode, "cancel_rate"),
            Obstacle = GetIntValue(skillNode, "obstacle", 4),
            AddWpnRange = GetIntValue(skillNode, "add_wpn_range"),
            CastingDelay = GetIntValue(skillNode, "casting_delay"),
            MoveCasting = GetIntValue(skillNode, "move_casting", 1),
            InstantSkill = GetIntValue(skillNode, "instant_skill"),
            AutoAttack = GetStringValue(skillNode, "auto_attack", "stop"),
            PeaceSkill = GetIntValue(skillNode, "peace_skill"),
            ShowWeapon = GetIntValue(skillNode, "show_weapon"),

            // 动作和动画
            MotionName = GetStringValue(skillNode, "motion_name"),
            MotionPlaySpeed = GetIntValue(skillNode, "motion_play_speed", 50),

            // 特效相关
            CastFx = GetStringValue(skillNode, "cast_fx"),
            CastFxBone = GetStringValue(skillNode, "cast_fx_bone"),
            CastcancelFx = GetStringValue(skillNode, "castcancel_fx"),
            CastcancelFxBone = GetStringValue(skillNode, "castcancel_fx_bone"),
            FireFx = GetStringValue(skillNode, "fire_fx"),
            FireFxBone = GetStringValue(skillNode, "fire_fx_bone"),
            HitFx = GetStringValue(skillNode, "hit_fx"),
            HitFxEx1 = GetStringValue(skillNode, "hit_fx_ex1"),
            HitFxBone = GetStringValue(skillNode, "hit_fx_bone"),
            StatusFx = GetStringValue(skillNode, "status_fx"),
            StatusFxBone = GetStringValue(skillNode, "status_fx_bone"),
            CastFxc = GetStringValue(skillNode, "cast_fxc"),
            FireFxc = GetStringValue(skillNode, "fire_fxc"),
            HitFxc = GetStringValue(skillNode, "hit_fxc"),

            // 效果1 - 完整属性
            Effect1Type = GetStringValue(skillNode, "effect1_type"),
            Effect1NoResist = GetIntValue(skillNode, "effect1_noresist"),
            Effect1Remain1 = GetIntValue(skillNode, "effect1_remain1"),
            Effect1Remain2 = GetIntValue(skillNode, "effect1_remain2"),
            Effect1EffectId = GetIntValue(skillNode, "effect1_effectid"),
            Effect1HideMsg = GetIntValue(skillNode, "effect1_hidemsg"),
            Effect1TargetType = GetStringValue(skillNode, "effect1_target_type"),
            Effect1Reserved1 = GetIntValue(skillNode, "effect1_reserved1"),
            Effect1Reserved2 = GetIntValue(skillNode, "effect1_reserved2"),
            Effect1Reserved3 = GetIntValue(skillNode, "effect1_reserved3"),
            Effect1Reserved4 = GetIntValue(skillNode, "effect1_reserved4"),
            Effect1Reserved6 = GetIntValue(skillNode, "effect1_reserved6"),
            Effect1Reserved7 = GetIntValue(skillNode, "effect1_reserved7"),
            Effect1Reserved9 = GetIntValue(skillNode, "effect1_reserved9"),
            Effect1Reserved10 = GetStringValue(skillNode, "effect1_reserved10"),
            Effect1Reserved11 = GetIntValue(skillNode, "effect1_reserved11"),
            Effect1Reserved13 = GetStringValue(skillNode, "effect1_reserved13"),
            Effect1Reserved14 = GetStringValue(skillNode, "effect1_reserved14"),
            Effect1CriticalProbMod1 = GetIntValue(skillNode, "effect1_critical_prob_mod1", 100),

            // 效果2 - 完整属性
            Effect2Type = GetStringValue(skillNode, "effect2_type"),
            Effect2NoResist = GetIntValue(skillNode, "effect2_noresist"),
            Effect2TargetType = GetStringValue(skillNode, "effect2_target_type"),
            Effect2CondPreeffect = GetStringValue(skillNode, "effect2_cond_preeffect"),
            Effect2Reserved1 = GetIntValue(skillNode, "effect2_reserved1"),
            Effect2Reserved2 = GetIntValue(skillNode, "effect2_reserved2"),
            Effect2Reserved10 = GetStringValue(skillNode, "effect2_reserved10"),
            Effect2Reserved11 = GetIntValue(skillNode, "effect2_reserved11"),
            Effect2Reserved12 = GetIntValue(skillNode, "effect2_reserved12"),
            Effect2Reserved15 = GetIntValue(skillNode, "effect2_reserved15"),
            Effect2Reserved16 = GetIntValue(skillNode, "effect2_reserved16"),
            Effect2Reserved17 = GetIntValue(skillNode, "effect2_reserved17"),
            Effect2Reserved18 = GetIntValue(skillNode, "effect2_reserved18"),
            Effect2CriticalProbMod2 = GetIntValue(skillNode, "effect2_critical_prob_mod2", 100)
        };

        return skill;
    }

    /// <summary>
    /// 获取字符串值
    /// </summary>
    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText ?? defaultValue;
    }

    /// <summary>
    /// 获取整数值
    /// </summary>
    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText, out var value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 保存技能到XML文件
    /// </summary>
    public async Task SaveSkillsToXmlAsync(string filePath, List<SkillBaseClient> skills)
    {
        try
        {
            _logger.LogInformation("开始保存技能文件: {FilePath}, 技能数量: {Count}", filePath, skills.Count);

            var xmlDoc = new XmlDocument();
            var declaration = xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmlDoc.AppendChild(declaration);

            var rootElement = xmlDoc.CreateElement("skill_base_clients");
            xmlDoc.AppendChild(rootElement);

            // 添加所有技能（按ID排序）
            var sortedSkills = skills.OrderBy(s => s.Id).ToList();
            foreach (var skill in sortedSkills)
            {
                var skillElement = CreateSkillBaseClientElement(xmlDoc, skill);
                rootElement.AppendChild(skillElement);
            }

            // 保存文件
            xmlDoc.Save(filePath);
            _logger.LogInformation("技能文件保存成功: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存技能文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 创建技能基础数据XML元素 - 完整版本
    /// </summary>
    private XmlElement CreateSkillBaseClientElement(XmlDocument xmlDoc, SkillBaseClient skill)
    {
        var skillElement = xmlDoc.CreateElement("skill_base_client");

        // 基础信息
        AppendElement(xmlDoc, skillElement, "id", skill.Id.ToString());
        AppendElement(xmlDoc, skillElement, "name", skill.Name);
        AppendElement(xmlDoc, skillElement, "desc", skill.Desc);
        if (!string.IsNullOrEmpty(skill.DescLong))
            AppendElement(xmlDoc, skillElement, "desc_long", skill.DescLong);
        if (!string.IsNullOrEmpty(skill.DescAbnormal))
            AppendElement(xmlDoc, skillElement, "desc_abnormal", skill.DescAbnormal);
        if (!string.IsNullOrEmpty(skill.DescPrechainCategoryName))
            AppendElement(xmlDoc, skillElement, "desc_prechain_category_name", skill.DescPrechainCategoryName);
        AppendElement(xmlDoc, skillElement, "type", skill.Type);
        AppendElement(xmlDoc, skillElement, "sub_type", skill.SubType);
        AppendElement(xmlDoc, skillElement, "skill_category", skill.SkillCategory);
        if (skill.UltraSkill != 0)
            AppendElement(xmlDoc, skillElement, "ultra_skill", skill.UltraSkill.ToString());
        if (skill.UltraTransfer != 0)
            AppendElement(xmlDoc, skillElement, "ultra_transfer", skill.UltraTransfer.ToString());
        if (skill.ChainCategoryLevel != 0)
            AppendElement(xmlDoc, skillElement, "chain_category_level", skill.ChainCategoryLevel.ToString());
        if (!string.IsNullOrEmpty(skill.SkillGroupName))
            AppendElement(xmlDoc, skillElement, "skill_group_name", skill.SkillGroupName);
        if (!string.IsNullOrEmpty(skill.SkillIconName))
            AppendElement(xmlDoc, skillElement, "skillicon_name", skill.SkillIconName);
        if (skill.DelayId != 0)
            AppendElement(xmlDoc, skillElement, "delay_id", skill.DelayId.ToString());
        AppendElement(xmlDoc, skillElement, "activation_attribute", skill.ActivationAttribute);

        // 消耗相关
        AppendElement(xmlDoc, skillElement, "cost_parameter", skill.CostParameter);
        AppendElement(xmlDoc, skillElement, "cost_end", skill.CostEnd.ToString());
        if (skill.CostChargeWeapon != 0)
            AppendElement(xmlDoc, skillElement, "cost_charge_weapon", skill.CostChargeWeapon.ToString());
        if (skill.CostChargeArmor != 0)
            AppendElement(xmlDoc, skillElement, "cost_charge_armor", skill.CostChargeArmor.ToString());
        if (skill.PolishChargeWeapon != 0)
            AppendElement(xmlDoc, skillElement, "polish_charge_weapon", skill.PolishChargeWeapon.ToString());
        if (skill.PenaltyNoCastingTimeSucc != 0)
            AppendElement(xmlDoc, skillElement, "penalty_no_casting_time_succ", skill.PenaltyNoCastingTimeSucc.ToString());

        // 驱散相关
        if (!string.IsNullOrEmpty(skill.DispelCategory))
            AppendElement(xmlDoc, skillElement, "dispel_category", skill.DispelCategory);
        if (skill.RequiredDispelLevel != 0)
            AppendElement(xmlDoc, skillElement, "required_dispel_level", skill.RequiredDispelLevel.ToString());

        // 目标相关
        AppendElement(xmlDoc, skillElement, "target_slot", skill.TargetSlot);
        AppendElement(xmlDoc, skillElement, "hostile_type", skill.HostileType);
        AppendElement(xmlDoc, skillElement, "delay_type", skill.DelayType.ToString());
        AppendElement(xmlDoc, skillElement, "delay_time", skill.DelayTime.ToString());
        AppendElement(xmlDoc, skillElement, "target_maxcount", skill.TargetMaxcount.ToString());
        AppendElement(xmlDoc, skillElement, "first_target", skill.FirstTarget);
        AppendElement(xmlDoc, skillElement, "first_target_valid_distance", skill.FirstTargetValidDistance.ToString());
        if (!string.IsNullOrEmpty(skill.TargetRange))
            AppendElement(xmlDoc, skillElement, "target_range", skill.TargetRange);
        AppendElement(xmlDoc, skillElement, "target_range_area_type", skill.TargetRangeAreaType);
        AppendElement(xmlDoc, skillElement, "target_range_opt1", skill.TargetRangeOpt1.ToString());
        AppendElement(xmlDoc, skillElement, "target_range_opt3", skill.TargetRangeOpt3.ToString());
        AppendElement(xmlDoc, skillElement, "target_species_restriction", skill.TargetSpeciesRestriction);
        AppendElement(xmlDoc, skillElement, "target_relation_restriction", skill.TargetRelationRestriction);

        // 行为控制
        AppendElement(xmlDoc, skillElement, "cancel_rate", skill.CancelRate.ToString());
        AppendElement(xmlDoc, skillElement, "obstacle", skill.Obstacle.ToString());
        AppendElement(xmlDoc, skillElement, "casting_delay", skill.CastingDelay.ToString());
        AppendElement(xmlDoc, skillElement, "move_casting", skill.MoveCasting.ToString());
        AppendElement(xmlDoc, skillElement, "auto_attack", skill.AutoAttack);

        // 动作和动画
        if (!string.IsNullOrEmpty(skill.MotionName))
            AppendElement(xmlDoc, skillElement, "motion_name", skill.MotionName);
        AppendElement(xmlDoc, skillElement, "motion_play_speed", skill.MotionPlaySpeed.ToString());

        // 效果1
        if (!string.IsNullOrEmpty(skill.Effect1Type))
        {
            AppendElement(xmlDoc, skillElement, "effect1_type", skill.Effect1Type);
            AppendElement(xmlDoc, skillElement, "effect1_noresist", skill.Effect1NoResist.ToString());
            if (!string.IsNullOrEmpty(skill.Effect1TargetType))
                AppendElement(xmlDoc, skillElement, "effect1_target_type", skill.Effect1TargetType);
            AppendElement(xmlDoc, skillElement, "effect1_reserved1", skill.Effect1Reserved1.ToString());
            AppendElement(xmlDoc, skillElement, "effect1_reserved2", skill.Effect1Reserved2.ToString());
            AppendElement(xmlDoc, skillElement, "effect1_reserved3", skill.Effect1Reserved3.ToString());
            AppendElement(xmlDoc, skillElement, "effect1_reserved4", skill.Effect1Reserved4.ToString());
            AppendElement(xmlDoc, skillElement, "effect1_reserved6", skill.Effect1Reserved6.ToString());
            if (!string.IsNullOrEmpty(skill.Effect1Reserved10))
                AppendElement(xmlDoc, skillElement, "effect1_reserved10", skill.Effect1Reserved10);
            AppendElement(xmlDoc, skillElement, "effect1_reserved11", skill.Effect1Reserved11.ToString());
            AppendElement(xmlDoc, skillElement, "effect1_critical_prob_mod1", skill.Effect1CriticalProbMod1.ToString());
        }

        // 效果2
        if (!string.IsNullOrEmpty(skill.Effect2Type))
        {
            AppendElement(xmlDoc, skillElement, "effect2_type", skill.Effect2Type);
            AppendElement(xmlDoc, skillElement, "effect2_noresist", skill.Effect2NoResist.ToString());
            if (!string.IsNullOrEmpty(skill.Effect2TargetType))
                AppendElement(xmlDoc, skillElement, "effect2_target_type", skill.Effect2TargetType);
            if (!string.IsNullOrEmpty(skill.Effect2CondPreeffect))
                AppendElement(xmlDoc, skillElement, "effect2_cond_preeffect", skill.Effect2CondPreeffect);
            AppendElement(xmlDoc, skillElement, "effect2_reserved1", skill.Effect2Reserved1.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved2", skill.Effect2Reserved2.ToString());
            if (!string.IsNullOrEmpty(skill.Effect2Reserved10))
                AppendElement(xmlDoc, skillElement, "effect2_reserved10", skill.Effect2Reserved10);
            AppendElement(xmlDoc, skillElement, "effect2_reserved11", skill.Effect2Reserved11.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved12", skill.Effect2Reserved12.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved15", skill.Effect2Reserved15.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved16", skill.Effect2Reserved16.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved17", skill.Effect2Reserved17.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_reserved18", skill.Effect2Reserved18.ToString());
            AppendElement(xmlDoc, skillElement, "effect2_critical_prob_mod2", skill.Effect2CriticalProbMod2.ToString());
        }

        return skillElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AppendElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value;
        parentElement.AppendChild(element);
    }
}
