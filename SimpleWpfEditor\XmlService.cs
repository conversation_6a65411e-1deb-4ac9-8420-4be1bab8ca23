using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Text;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor;

/// <summary>
/// 简化的XML数据服务
/// </summary>
public class XmlService
{
    private readonly ILogger<XmlService> _logger;

    public XmlService(ILogger<XmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加载XML文件中的所有物品
    /// </summary>
    public async Task<List<ClientItem>> LoadItemsAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载XML文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            var items = new List<ClientItem>();
            var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);

            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);

            // 检测文件类型
            var rootNode = xmlDoc.DocumentElement;
            if (rootNode == null)
            {
                throw new InvalidOperationException("XML文件格式无效：缺少根节点");
            }

            // 根据根节点类型处理不同的文件
            switch (rootNode.Name)
            {
                case "client_items":
                    items = await LoadClientItemsAsync(xmlDoc);
                    break;
                case "npc_clients":
                    items = await LoadNpcAsItemsAsync(xmlDoc);
                    break;
                case "skill_base_clients":
                case "client_skills":  // 支持新的技能文件格式
                    items = await LoadSkillsAsItemsAsync(xmlDoc);
                    break;
                case "client_titles":
                    items = await LoadTitlesAsItemsAsync(xmlDoc);
                    break;
                default:
                    _logger.LogWarning("不支持的XML文件类型: {RootNodeName}", rootNode.Name);
                    throw new NotSupportedException($"不支持的XML文件类型: {rootNode.Name}");
            }

            _logger.LogInformation("成功加载 {Count} 个条目", items.Count);
            return items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 加载标准物品文件
    /// </summary>
    private async Task<List<ClientItem>> LoadClientItemsAsync(XmlDocument xmlDoc)
    {
        var items = new List<ClientItem>();
        var itemNodes = xmlDoc.SelectNodes("//client_item");

        if (itemNodes != null)
        {
            foreach (XmlNode itemNode in itemNodes)
            {
                try
                {
                    var item = ParseClientItem(itemNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析物品节点时出错");
                }
            }
        }

        return items;
    }

    /// <summary>
    /// 加载NPC文件并转换为物品格式（用于查看）
    /// </summary>
    private async Task<List<ClientItem>> LoadNpcAsItemsAsync(XmlDocument xmlDoc)
    {
        var items = new List<ClientItem>();
        var npcNodes = xmlDoc.SelectNodes("//npc_client");

        if (npcNodes != null)
        {
            foreach (XmlNode npcNode in npcNodes)
            {
                try
                {
                    var item = ConvertNpcToItem(npcNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析NPC节点时出错");
                }
            }
        }

        return items;
    }

    /// <summary>
    /// 加载技能文件并转换为物品格式（用于查看）
    /// </summary>
    private async Task<List<ClientItem>> LoadSkillsAsItemsAsync(XmlDocument xmlDoc)
    {
        var items = new List<ClientItem>();

        // 尝试两种节点格式
        var skillNodes = xmlDoc.SelectNodes("//skill_base_client");
        if (skillNodes == null || skillNodes.Count == 0)
        {
            skillNodes = xmlDoc.SelectNodes("//client_skill");
        }

        if (skillNodes != null)
        {
            foreach (XmlNode skillNode in skillNodes)
            {
                try
                {
                    var item = ConvertSkillToItem(skillNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析技能节点时出错");
                }
            }
        }

        return items;
    }

    /// <summary>
    /// 保存物品列表到XML文件
    /// </summary>
    public async Task SaveItemsAsync(string filePath, List<ClientItem> items)
    {
        try
        {
            _logger.LogInformation("开始保存XML文件: {FilePath}, 物品数量: {Count}", filePath, items.Count);

            var xmlDoc = new XmlDocument();
            var declaration = xmlDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            xmlDoc.AppendChild(declaration);

            var rootElement = xmlDoc.CreateElement("client_items");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 按ID排序后添加所有物品
            var sortedItems = items.OrderBy(item => item.Id).ToList();
            foreach (var item in sortedItems)
            {
                var itemElement = CreateItemElement(xmlDoc, item);
                rootElement.AppendChild(itemElement);
            }

            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                NewLineChars = "\r\n"
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存XML文件: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 验证物品数据
    /// </summary>
    public (bool IsValid, List<string> Errors) ValidateItem(ClientItem item)
    {
        var errors = new List<string>();
        var context = new ValidationContext(item);
        var results = new List<ValidationResult>();

        if (!Validator.TryValidateObject(item, context, results, true))
        {
            errors.AddRange(results.Select(r => r.ErrorMessage ?? "未知验证错误"));
        }

        if (item.Id <= 0)
            errors.Add("物品ID必须大于0");

        if (string.IsNullOrWhiteSpace(item.Name))
            errors.Add("物品名称不能为空");

        if (item.MaxStackCount <= 0)
            errors.Add("最大堆叠数量必须大于0");

        return (errors.Count == 0, errors);
    }

    #region 私有方法

    /// <summary>
    /// 解析client_item XML节点
    /// </summary>
    private ClientItem? ParseClientItem(XmlNode itemNode)
    {
        try
        {
            var item = new ClientItem();

            item.Id = GetIntValue(itemNode, "id");
            item.Name = GetStringValue(itemNode, "name");
            item.Desc = GetStringValue(itemNode, "desc");
            item.ArmorType = GetStringValue(itemNode, "armor_type");
            item.ItemType = GetStringValue(itemNode, "item_type");
            item.Material = GetStringValue(itemNode, "material");
            item.IconName = GetStringValue(itemNode, "icon_name");
            item.Price = GetIntValue(itemNode, "price");
            item.MaxStackCount = GetIntValue(itemNode, "max_stack_count", 1);
            item.CanSellToNpc = GetBoolValue(itemNode, "can_sell_to_npc", true);
            item.CanDepositToCharacterWarehouse = GetBoolValue(itemNode, "can_deposit_to_character_warehouse", true);
            item.CanDepositToAccountWarehouse = GetBoolValue(itemNode, "can_deposit_to_account_warehouse", true);
            item.CanDepositToGuildWarehouse = GetBoolValue(itemNode, "can_deposit_to_guild_warehouse", true);
            item.EquipmentSlots = GetStringValue(itemNode, "equipment_slots");
            item.Dodge = GetIntValue(itemNode, "dodge", 0);
            item.Quality = GetStringValue(itemNode, "quality", "common");
            item.Level = GetIntValue(itemNode, "level", 1);
            item.MagicalResist = GetIntValue(itemNode, "magical_resist", 0);
            item.PhysicalDefend = GetIntValue(itemNode, "physical_defend", 0);
            item.Lore = GetBoolValue(itemNode, "lore", false);
            item.CanExchange = GetBoolValue(itemNode, "can_exchange", true);
            item.Breakable = GetBoolValue(itemNode, "breakable", true);
            item.SoulBind = GetBoolValue(itemNode, "soul_bind", false);
            item.RemoveWhenLogout = GetBoolValue(itemNode, "remove_when_logout", false);
            item.GenderPermitted = GetStringValue(itemNode, "gender_permitted", "all");

            // 职业限制
            item.Warrior = GetIntValue(itemNode, "warrior", 1);
            item.Scout = GetIntValue(itemNode, "scout", 1);
            item.Mage = GetIntValue(itemNode, "mage", 1);
            item.Cleric = GetIntValue(itemNode, "cleric", 1);
            item.Engineer = GetIntValue(itemNode, "engineer", 1);
            item.Artist = GetIntValue(itemNode, "artist", 1);
            item.Fighter = GetIntValue(itemNode, "fighter", 1);
            item.Knight = GetIntValue(itemNode, "knight", 1);
            item.Assassin = GetIntValue(itemNode, "assassin", 1);
            item.Ranger = GetIntValue(itemNode, "ranger", 1);
            item.Wizard = GetIntValue(itemNode, "wizard", 1);
            item.Elementalist = GetIntValue(itemNode, "elementalist", 1);
            item.Chanter = GetIntValue(itemNode, "chanter", 1);
            item.Priest = GetIntValue(itemNode, "priest", 1);
            item.Gunner = GetIntValue(itemNode, "gunner", 1);
            item.Bard = GetIntValue(itemNode, "bard", 1);
            item.Rider = GetIntValue(itemNode, "rider", 1);

            // 强化属性
            item.OptionSlotValue = GetIntValue(itemNode, "option_slot_value");
            item.SpecialSlotValue = GetIntValue(itemNode, "special_slot_value");
            item.OptionSlotBonus = GetIntValue(itemNode, "option_slot_bonus");
            item.BonusApply = GetStringValue(itemNode, "bonus_apply", "equip");
            item.NoEnchant = GetBoolValue(itemNode, "no_enchant", false);
            item.MaxEnchantValue = GetIntValue(itemNode, "max_enchant_value", 15);
            item.CanProcEnchant = GetBoolValue(itemNode, "can_proc_enchant", true);
            item.CanCompositeWeapon = GetBoolValue(itemNode, "can_composite_weapon", true);

            // 外观和音效
            item.CannotChangeskin = GetIntValue(itemNode, "cannot_changeskin");
            item.UiSoundType = GetStringValue(itemNode, "ui_sound_type");
            item.Mesh = GetStringValue(itemNode, "mesh");
            item.MeshChange = GetIntValue(itemNode, "mesh_change");
            item.CanDye = GetIntValue(itemNode, "can_dye");

            // 特殊属性
            item.CashItem = GetIntValue(itemNode, "cash_item");
            item.CanSplit = GetBoolValue(itemNode, "can_split", false);
            item.ItemDropPermitted = GetBoolValue(itemNode, "item_drop_permitted", false);
            item.BmRestrictCategory = GetIntValue(itemNode, "bm_restrict_category", 1);
            item.CanApExtraction = GetBoolValue(itemNode, "can_ap_extraction", false);
            item.CanPolish = GetBoolValue(itemNode, "can_polish", true);
            item.PolishBurnOnAttack = GetIntValue(itemNode, "polish_burn_on_attack", 29);
            item.PolishBurnOnDefend = GetIntValue(itemNode, "polish_burn_on_defend", 12);
            item.RacePermitted = GetStringValue(itemNode, "race_permitted", "pc_light pc_dark");

            // 武器装备属性
            item.BonusAttr1 = GetStringValue(itemNode, "bonus_attr1");
            item.BonusAttr2 = GetStringValue(itemNode, "bonus_attr2");
            item.BonusAttr3 = GetStringValue(itemNode, "bonus_attr3");
            item.BonusAttr4 = GetStringValue(itemNode, "bonus_attr4");
            item.BonusAttrA1 = GetStringValue(itemNode, "bonus_attr_a1");
            item.BonusAttrA2 = GetStringValue(itemNode, "bonus_attr_a2");
            item.BonusAttrA3 = GetStringValue(itemNode, "bonus_attr_a3");
            item.BonusAttrA4 = GetStringValue(itemNode, "bonus_attr_a4");
            item.BurnOnAttack = GetIntValue(itemNode, "burn_on_attack", 0);
            item.BurnOnDefend = GetIntValue(itemNode, "burn_on_defend", 0);
            item.BmRestrictCategory = GetIntValue(itemNode, "bm_restrict_category", 0);
            item.DisposableTradeItem = GetStringValue(itemNode, "disposable_trade_item");
            item.DisposableTradeItemCount = GetIntValue(itemNode, "disposable_trade_item_count", 0);
            item.CanPackCount = GetIntValue(itemNode, "can_pack_count", 0);
            item.VisualSlot = GetStringValue(itemNode, "visual_slot");
            item.DefaultColorM = GetStringValue(itemNode, "default_color_m", "255,255,255");
            item.DefaultColorF = GetStringValue(itemNode, "default_color_f", "255,255,255");

            // 武器专用属性
            item.WeaponType = GetStringValue(itemNode, "weapon_type");
            item.DmgDecal = GetIntValue(itemNode, "dmg_decal", 0);
            item.EquipBone = GetStringValue(itemNode, "equip_bone");
            item.CombatEquipBone = GetStringValue(itemNode, "combat_equip_bone");

            // 强化和抛光属性
            item.CanProcEnchant = GetBoolValue(itemNode, "can_proc_enchant", true);
            item.CanCompositeWeapon = GetBoolValue(itemNode, "can_composite_weapon", true);
            item.CanPolish = GetBoolValue(itemNode, "can_polish", false);
            item.PolishBurnOnAttack = GetIntValue(itemNode, "polish_burn_on_attack", 0);
            item.PolishBurnOnDefend = GetIntValue(itemNode, "polish_burn_on_defend", 0);

            // 描述和说明
            item.DescLong = GetStringValue(itemNode, "desc_long");

            // 分解属性
            item.DisassemblyItem = GetIntValue(itemNode, "disassembly_item", 0);

            // 额外交易字段
            item.ExtraCurrencyItem = GetStringValue(itemNode, "extra_currency_item");
            item.ExtraCurrencyItemCount = GetIntValue(itemNode, "extra_currency_item_count", 0);
            item.ActivationSkill = GetStringValue(itemNode, "activation_skill");
            item.ActivationMode = GetStringValue(itemNode, "activation_mode");
            item.ExtraInventory = GetIntValue(itemNode, "extra_inventory", 0);

            // 解析兑换物品列表
            var tradeInListNode = itemNode.SelectSingleNode("trade_in_item_list");
            if (tradeInListNode != null)
            {
                var dataNodes = tradeInListNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var tradeInItem = GetStringValue(dataNode, "trade_in_item");
                        var tradeInItemCount = GetIntValue(dataNode, "trade_in_item_count", 1);

                        if (!string.IsNullOrEmpty(tradeInItem))
                        {
                            item.TradeInItemList.Add(new TradeInItemData
                            {
                                TradeInItem = tradeInItem,
                                TradeInItemCount = tradeInItemCount
                            });
                        }
                    }
                }
            }

            // 额外的奖励属性
            item.BonusAttr5 = GetStringValue(itemNode, "bonus_attr5");
            item.BonusAttr6 = GetStringValue(itemNode, "bonus_attr6");
            item.BonusAttr7 = GetStringValue(itemNode, "bonus_attr7");
            item.BonusAttr8 = GetStringValue(itemNode, "bonus_attr8");
            item.BonusAttrA5 = GetStringValue(itemNode, "bonus_attr_a5");
            item.BonusAttrA6 = GetStringValue(itemNode, "bonus_attr_a6");
            item.BonusAttrA7 = GetStringValue(itemNode, "bonus_attr_a7");
            item.BonusAttrA8 = GetStringValue(itemNode, "bonus_attr_a8");

            // 特殊效果属性
            item.BonusAttrB1 = GetStringValue(itemNode, "bonus_attr_b1");
            item.BonusAttrB2 = GetStringValue(itemNode, "bonus_attr_b2");
            item.BonusAttrB3 = GetStringValue(itemNode, "bonus_attr_b3");
            item.BonusAttrB4 = GetStringValue(itemNode, "bonus_attr_b4");

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析物品节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 创建物品的XML元素 - 使用StringBuilder确保正确的字段顺序
    /// </summary>
    private XmlElement CreateItemElement(XmlDocument xmlDoc, ClientItem item)
    {
        var itemElement = xmlDoc.CreateElement("client_item");

        // 按照您要求的严格顺序添加字段

        // 基础信息
        AddElement(xmlDoc, itemElement, "id", item.Id.ToString());
        AddElement(xmlDoc, itemElement, "name", item.Name);
        AddElement(xmlDoc, itemElement, "desc", item.Desc);

        // 武器/防具类型
        AddElement(xmlDoc, itemElement, "weapon_type", item.WeaponType);
        AddElement(xmlDoc, itemElement, "armor_type", item.ArmorType);
        AddElement(xmlDoc, itemElement, "item_type", item.ItemType);

        // 外观相关
        AddElement(xmlDoc, itemElement, "mesh", item.Mesh);
        if (item.MeshChange > 0)
            AddElement(xmlDoc, itemElement, "mesh_change", item.MeshChange.ToString());
        AddElement(xmlDoc, itemElement, "material", item.Material);
        if (item.DmgDecal > 0)
            AddElement(xmlDoc, itemElement, "dmg_decal", item.DmgDecal.ToString());
        AddElement(xmlDoc, itemElement, "icon_name", item.IconName);
        AddElement(xmlDoc, itemElement, "equip_bone", item.EquipBone);

        // *** 交易相关字段 - 根据交易模式生成正确的字段组合 ***
        var tradeMode = item.GetTradeMode();

        switch (tradeMode)
        {
            case "basic": // 基础基纳购买：只有price
                AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
                break;

            case "mixed": // 混合货币购买：price + extra_currency
                AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
                if (!string.IsNullOrEmpty(item.ExtraCurrencyItem))
                {
                    AddElement(xmlDoc, itemElement, "extra_currency_item", item.ExtraCurrencyItem);
                    if (item.ExtraCurrencyItemCount > 0)
                        AddElement(xmlDoc, itemElement, "extra_currency_item_count", item.ExtraCurrencyItemCount.ToString());
                }
                break;

            case "exchange": // 兑换购买：只有trade_in_item_list
                AddElement(xmlDoc, itemElement, "price", "0"); // 兑换模式price为0
                if (item.TradeInItemList != null && item.TradeInItemList.Count > 0)
                {
                    var tradeInListElement = xmlDoc.CreateElement("trade_in_item_list");
                    foreach (var tradeInItem in item.TradeInItemList)
                    {
                        if (!string.IsNullOrEmpty(tradeInItem.TradeInItem))
                        {
                            var dataElement = xmlDoc.CreateElement("data");
                            AddElement(xmlDoc, dataElement, "trade_in_item", tradeInItem.TradeInItem);
                            AddElement(xmlDoc, dataElement, "trade_in_item_count", tradeInItem.TradeInItemCount.ToString());
                            tradeInListElement.AppendChild(dataElement);
                        }
                    }
                    if (tradeInListElement.HasChildNodes)
                    {
                        itemElement.AppendChild(tradeInListElement);
                    }
                }
                break;

            case "complex": // 复杂兑换购买：price + extra_currency + trade_in_item_list
                AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
                if (!string.IsNullOrEmpty(item.ExtraCurrencyItem))
                {
                    AddElement(xmlDoc, itemElement, "extra_currency_item", item.ExtraCurrencyItem);
                    if (item.ExtraCurrencyItemCount > 0)
                        AddElement(xmlDoc, itemElement, "extra_currency_item_count", item.ExtraCurrencyItemCount.ToString());
                }
                if (item.TradeInItemList != null && item.TradeInItemList.Count > 0)
                {
                    var tradeInListElement = xmlDoc.CreateElement("trade_in_item_list");
                    foreach (var tradeInItem in item.TradeInItemList)
                    {
                        if (!string.IsNullOrEmpty(tradeInItem.TradeInItem))
                        {
                            var dataElement = xmlDoc.CreateElement("data");
                            AddElement(xmlDoc, dataElement, "trade_in_item", tradeInItem.TradeInItem);
                            AddElement(xmlDoc, dataElement, "trade_in_item_count", tradeInItem.TradeInItemCount.ToString());
                            tradeInListElement.AppendChild(dataElement);
                        }
                    }
                    if (tradeInListElement.HasChildNodes)
                    {
                        itemElement.AppendChild(tradeInListElement);
                    }
                }
                break;

            default: // 默认为基础模式
                AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
                break;
        }

        // 一次性交易物品 - 也在price之后
        if (!string.IsNullOrEmpty(item.DisposableTradeItem))
        {
            AddElement(xmlDoc, itemElement, "disposable_trade_item", item.DisposableTradeItem);
            if (item.DisposableTradeItemCount > 0)
                AddElement(xmlDoc, itemElement, "disposable_trade_item_count", item.DisposableTradeItemCount.ToString());
        }

        // *** 交易字段结束 ***

        // 堆叠数量 - 在所有交易字段之后
        AddElement(xmlDoc, itemElement, "max_stack_count", item.MaxStackCount.ToString());

        // 装备相关
        AddElement(xmlDoc, itemElement, "equipment_slots", item.EquipmentSlots);

        // 品质和等级
        AddElement(xmlDoc, itemElement, "quality", item.Quality);
        AddElement(xmlDoc, itemElement, "level", item.Level.ToString());
        AddElement(xmlDoc, itemElement, "lore", item.Lore ? "TRUE" : "FALSE");

        // 交易权限
        AddElement(xmlDoc, itemElement, "can_exchange", item.CanExchange ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_sell_to_npc", item.CanSellToNpc ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_deposit_to_character_warehouse", item.CanDepositToCharacterWarehouse ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_deposit_to_account_warehouse", item.CanDepositToAccountWarehouse ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_deposit_to_guild_warehouse", item.CanDepositToGuildWarehouse ? "TRUE" : "FALSE");

        // 绑定属性
        AddElement(xmlDoc, itemElement, "breakable", item.Breakable ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "soul_bind", item.SoulBind ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "remove_when_logout", item.RemoveWhenLogout ? "TRUE" : "FALSE");

        // 性别限制
        AddElement(xmlDoc, itemElement, "gender_permitted", item.GenderPermitted);

        // 职业限制 - 按照标准顺序
        if (item.Warrior > 0)
            AddElement(xmlDoc, itemElement, "warrior", item.Warrior.ToString());
        if (item.Scout > 0)
            AddElement(xmlDoc, itemElement, "scout", item.Scout.ToString());
        if (item.Mage > 0)
            AddElement(xmlDoc, itemElement, "mage", item.Mage.ToString());
        if (item.Cleric > 0)
            AddElement(xmlDoc, itemElement, "cleric", item.Cleric.ToString());
        if (item.Engineer > 0)
            AddElement(xmlDoc, itemElement, "engineer", item.Engineer.ToString());
        if (item.Artist > 0)
            AddElement(xmlDoc, itemElement, "artist", item.Artist.ToString());
        if (item.Fighter > 0)
            AddElement(xmlDoc, itemElement, "fighter", item.Fighter.ToString());
        if (item.Knight > 0)
            AddElement(xmlDoc, itemElement, "knight", item.Knight.ToString());
        if (item.Assassin > 0)
            AddElement(xmlDoc, itemElement, "assassin", item.Assassin.ToString());
        if (item.Ranger > 0)
            AddElement(xmlDoc, itemElement, "ranger", item.Ranger.ToString());
        if (item.Wizard > 0)
            AddElement(xmlDoc, itemElement, "wizard", item.Wizard.ToString());
        if (item.Elementalist > 0)
            AddElement(xmlDoc, itemElement, "elementalist", item.Elementalist.ToString());
        if (item.Chanter > 0)
            AddElement(xmlDoc, itemElement, "chanter", item.Chanter.ToString());
        if (item.Priest > 0)
            AddElement(xmlDoc, itemElement, "priest", item.Priest.ToString());
        if (item.Gunner > 0)
            AddElement(xmlDoc, itemElement, "gunner", item.Gunner.ToString());
        if (item.Bard > 0)
            AddElement(xmlDoc, itemElement, "bard", item.Bard.ToString());
        if (item.Rider > 0)
            AddElement(xmlDoc, itemElement, "rider", item.Rider.ToString());

        // 选项槽
        if (item.OptionSlotValue > 0)
            AddElement(xmlDoc, itemElement, "option_slot_value", item.OptionSlotValue.ToString());
        if (item.SpecialSlotValue > 0)
            AddElement(xmlDoc, itemElement, "special_slot_value", item.SpecialSlotValue.ToString());
        if (item.OptionSlotBonus > 0)
            AddElement(xmlDoc, itemElement, "option_slot_bonus", item.OptionSlotBonus.ToString());

        // 属性加成
        AddElement(xmlDoc, itemElement, "bonus_attr1", item.BonusAttr1);
        AddElement(xmlDoc, itemElement, "bonus_attr2", item.BonusAttr2);
        AddElement(xmlDoc, itemElement, "bonus_attr3", item.BonusAttr3);
        AddElement(xmlDoc, itemElement, "bonus_attr4", item.BonusAttr4);
        AddElement(xmlDoc, itemElement, "bonus_apply", item.BonusApply);

        // 强化相关
        AddElement(xmlDoc, itemElement, "no_enchant", item.NoEnchant ? "TRUE" : "FALSE");
        if (item.MaxEnchantValue > 0)
            AddElement(xmlDoc, itemElement, "max_enchant_value", item.MaxEnchantValue.ToString());
        AddElement(xmlDoc, itemElement, "can_proc_enchant", item.CanProcEnchant ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_composite_weapon", item.CanCompositeWeapon ? "TRUE" : "FALSE");

        // 外观相关
        if (item.CannotChangeskin > 0)
            AddElement(xmlDoc, itemElement, "cannot_changeskin", item.CannotChangeskin.ToString());
        AddElement(xmlDoc, itemElement, "ui_sound_type", item.UiSoundType);

        // 现金物品
        if (item.CashItem > 0)
            AddElement(xmlDoc, itemElement, "cash_item", item.CashItem.ToString());

        // 其他属性
        AddElement(xmlDoc, itemElement, "can_split", item.CanSplit ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "item_drop_permitted", item.ItemDropPermitted ? "TRUE" : "FALSE");

        // BM限制
        if (item.BmRestrictCategory > 0)
            AddElement(xmlDoc, itemElement, "bm_restrict_category", item.BmRestrictCategory.ToString());

        // AP提取和抛光
        AddElement(xmlDoc, itemElement, "can_ap_extraction", item.CanApExtraction ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_polish", item.CanPolish ? "TRUE" : "FALSE");
        if (item.PolishBurnOnAttack > 0)
            AddElement(xmlDoc, itemElement, "polish_burn_on_attack", item.PolishBurnOnAttack.ToString());
        if (item.PolishBurnOnDefend > 0)
            AddElement(xmlDoc, itemElement, "polish_burn_on_defend", item.PolishBurnOnDefend.ToString());

        // 种族限制 - 放在最后
        AddElement(xmlDoc, itemElement, "race_permitted", item.RacePermitted);

        // 特殊字段
        if (!string.IsNullOrEmpty(item.ActivationSkill))
            AddElement(xmlDoc, itemElement, "activation_skill", item.ActivationSkill);
        if (!string.IsNullOrEmpty(item.ActivationMode))
            AddElement(xmlDoc, itemElement, "activation_mode", item.ActivationMode);
        if (item.ExtraInventory > 0)
            AddElement(xmlDoc, itemElement, "extra_inventory", item.ExtraInventory.ToString());
        if (item.DisassemblyItem > 0)
            AddElement(xmlDoc, itemElement, "disassembly_item", item.DisassemblyItem.ToString());

        return itemElement;
    }

    /// <summary>
    /// 公共方法：创建物品的XML元素（供ServerItemConversionService使用）
    /// </summary>
    public XmlElement CreateItemElementPublic(XmlDocument xmlDoc, ClientItem item)
    {
        return CreateItemElement(xmlDoc, item);
    }

    private void AddElement(XmlDocument xmlDoc, XmlElement parent, string name, string value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            var element = xmlDoc.CreateElement(name);
            element.InnerText = value;
            parent.AppendChild(element);
        }
    }

    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText ?? defaultValue;
    }

    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText, out var value))
            return value;
        return defaultValue;
    }

    private bool GetBoolValue(XmlNode parentNode, string elementName, bool defaultValue = false)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null)
        {
            var text = node.InnerText.ToUpperInvariant();
            return text == "TRUE" || text == "1";
        }
        return defaultValue;
    }

    #endregion

    #region 数据转换方法

    /// <summary>
    /// 将NPC节点转换为ClientItem格式（用于查看）
    /// </summary>
    private ClientItem? ConvertNpcToItem(XmlNode npcNode)
    {
        try
        {
            var item = new ClientItem
            {
                Id = GetIntValue(npcNode, "id"),
                Name = GetStringValue(npcNode, "name"),
                Desc = GetStringValue(npcNode, "desc"),
                ArmorType = "NPC", // 标记为NPC类型
                ItemType = GetStringValue(npcNode, "ui_type", "npc"),
                Material = GetStringValue(npcNode, "material"),
                IconName = GetStringValue(npcNode, "mesh"),
                Price = 0, // NPC没有价格
                MaxStackCount = 1,

                // 设置为只读状态
                CanSellToNpc = false,
                CanDepositToCharacterWarehouse = false,
                CanDepositToAccountWarehouse = false,
                CanDepositToGuildWarehouse = false,

                // 所有职业都可以查看
                Warrior = 1, Scout = 1, Mage = 1, Cleric = 1,
                Engineer = 1, Artist = 1, Fighter = 1, Knight = 1,
                Assassin = 1, Ranger = 1, Wizard = 1, Elementalist = 1,
                Chanter = 1, Priest = 1, Gunner = 1, Bard = 1, Rider = 1
            };

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换NPC节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 将技能节点转换为ClientItem格式（用于查看）
    /// </summary>
    private ClientItem? ConvertSkillToItem(XmlNode skillNode)
    {
        try
        {
            var item = new ClientItem
            {
                Id = GetIntValue(skillNode, "id"),
                Name = GetStringValue(skillNode, "name"),
                Desc = GetStringValue(skillNode, "desc"),
                ArmorType = "技能", // 标记为技能类型
                ItemType = GetStringValue(skillNode, "type", "skill"),
                Material = GetStringValue(skillNode, "sub_type"),
                IconName = GetStringValue(skillNode, "skillicon_name"),
                Price = 0, // 技能没有价格
                MaxStackCount = 1,

                // 设置为只读状态
                CanSellToNpc = false,
                CanDepositToCharacterWarehouse = false,
                CanDepositToAccountWarehouse = false,
                CanDepositToGuildWarehouse = false,

                // 所有职业都可以查看
                Warrior = 1, Scout = 1, Mage = 1, Cleric = 1,
                Engineer = 1, Artist = 1, Fighter = 1, Knight = 1,
                Assassin = 1, Ranger = 1, Wizard = 1, Elementalist = 1,
                Chanter = 1, Priest = 1, Gunner = 1, Bard = 1, Rider = 1
            };

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换技能节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 将称号文件转换为物品格式
    /// </summary>
    private async Task<List<ClientItem>> LoadTitlesAsItemsAsync(XmlDocument xmlDoc)
    {
        var items = new List<ClientItem>();
        var titleNodes = xmlDoc.SelectNodes("//client_title");

        if (titleNodes != null)
        {
            foreach (XmlNode titleNode in titleNodes)
            {
                try
                {
                    var item = ConvertTitleToItem(titleNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析称号节点时出错");
                }
            }
        }

        return items;
    }

    /// <summary>
    /// 将称号节点转换为ClientItem
    /// </summary>
    private ClientItem? ConvertTitleToItem(XmlNode titleNode)
    {
        try
        {
            var idNode = titleNode.SelectSingleNode("id");
            var nameNode = titleNode.SelectSingleNode("name");
            var descNode = titleNode.SelectSingleNode("desc");
            var titleDescNode = titleNode.SelectSingleNode("title_desc");
            var titleRaceNode = titleNode.SelectSingleNode("title_race");
            var titlePriorityNode = titleNode.SelectSingleNode("title_priority");
            var titleTypeNode = titleNode.SelectSingleNode("title_type");
            var titleLocationNode = titleNode.SelectSingleNode("title_location");

            if (idNode == null || nameNode == null)
            {
                _logger.LogWarning("称号节点缺少必需的id或name字段");
                return null;
            }

            if (!int.TryParse(idNode.InnerText, out var id))
            {
                _logger.LogWarning("称号ID格式无效: {Id}", idNode.InnerText);
                return null;
            }

            // 获取奖励属性文本
            var bonusAttrsText = GetTitleBonusAttributes(titleNode);

            var item = new ClientItem
            {
                Id = id,
                Name = nameNode.InnerText,
                Desc = descNode?.InnerText ?? "",
                ArmorType = "称号", // 标识这是称号类型
                ItemType = titleTypeNode?.InnerText ?? "0",
                Material = titleLocationNode?.InnerText ?? "",
                IconName = $"title_icon_{id}",
                Price = int.TryParse(titlePriorityNode?.InnerText, out var priority) ? priority : 0,
                MaxStackCount = int.TryParse(titleRaceNode?.InnerText, out var race) ? race : 0,

                // 设置一些默认值
                CanSellToNpc = false,
                CanDepositToCharacterWarehouse = true,
                CanDepositToAccountWarehouse = true,
                CanDepositToGuildWarehouse = false,

                // 称号通常对所有职业开放
                Warrior = 1, Scout = 1, Mage = 1, Cleric = 1,
                Engineer = 1, Artist = 1, Fighter = 1, Knight = 1,
                Assassin = 1, Ranger = 1, Wizard = 1, Elementalist = 1,
                Chanter = 1, Priest = 1, Gunner = 1, Bard = 1, Rider = 1
            };

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换称号节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 获取称号的奖励属性文本
    /// </summary>
    private string GetTitleBonusAttributes(XmlNode titleNode)
    {
        try
        {
            var bonusAttrsNode = titleNode.SelectSingleNode("bonus_attrs");
            if (bonusAttrsNode == null) return "";

            var dataNodes = bonusAttrsNode.SelectNodes("data");
            if (dataNodes == null || dataNodes.Count == 0) return "";

            var attributes = new List<string>();
            foreach (XmlNode dataNode in dataNodes)
            {
                var bonusAttrNode = dataNode.SelectSingleNode("bonus_attr");
                if (bonusAttrNode != null && !string.IsNullOrEmpty(bonusAttrNode.InnerText))
                {
                    attributes.Add(bonusAttrNode.InnerText);
                }
            }

            return string.Join(", ", attributes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析称号奖励属性时出错");
            return "";
        }
    }

    #endregion

    /// <summary>
    /// 只转换修改过的物品到服务端格式（不修改客户端文件）
    /// </summary>
    public async Task<ConversionResult> SaveItemsWithServerConversionAsync(string clientFilePath, List<ClientItem> modifiedItems, string serverBasePath)
    {
        try
        {
            _logger.LogInformation($"开始转换 {modifiedItems.Count} 个修改过的物品到服务端格式（不修改客户端文件）");

            // 创建服务端转换服务
            var conversionService = new ServerItemConversionService(_logger, this);

            // 只转换并保存到服务端格式 - 不修改客户端文件
            var result = await conversionService.ConvertAndSaveToServerAsync(modifiedItems, serverBasePath);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换到服务端格式时出错");
            return new ConversionResult { Success = false, ErrorMessage = ex.Message };
        }
    }
}
