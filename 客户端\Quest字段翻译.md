# Quest相关字段翻译

## 1. 任务物品类型 (Quest Item Types)

### 基础任务物品字段
```xml
<item_type>quest</item_type>
```
- **中文翻译**: 任务物品类型
- **说明**: 标识该物品为任务相关物品

### 任务材料物品
```xml
<name>quest_material_id_S_PvE_M_61a</name>
<desc>STR_quest_material_id_S_PvE_M_61a</desc>
<desc_long>STR_quest_material_id_S_PvE_M_61a_DESC</desc_long>
```
- **中文翻译**: 
  - `quest_material_id_S_PvE_M_61a` → 任务材料ID_S_PvE_M_61a
  - `STR_quest_material_id_S_PvE_M_61a` → 任务材料描述
  - `STR_quest_material_id_S_PvE_M_61a_DESC` → 任务材料详细描述

### 任务材料图标
```xml
<icon_name>Icon_Item_quest_material_id_S_PvE_M_60a</icon_name>
```
- **中文翻译**: 任务材料图标
- **说明**: 任务材料在游戏中的显示图标

## 2. 职业任务材料 (Class Quest Materials)

### 炼金术士任务材料
```xml
<name>quest_al_material_450</name>
<desc>STR_QUEST_AL_MATERIAL_450</desc>
<desc_long>STR_QUEST_AL_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_al_material_450` → 炼金术士任务材料450
  - `STR_QUEST_AL_MATERIAL_450` → 炼金术士任务材料450
  - `STR_QUEST_AL_MATERIAL_450_DESC` → 炼金术士任务材料450详细说明

### 手工匠任务材料
```xml
<name>quest_ha_material_450</name>
<desc>STR_QUEST_HA_MATERIAL_450</desc>
<desc_long>STR_QUEST_HA_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_ha_material_450` → 手工匠任务材料450
  - `STR_QUEST_HA_MATERIAL_450` → 手工匠任务材料450
  - `STR_QUEST_HA_MATERIAL_450_DESC` → 手工匠任务材料450详细说明

### 武器匠任务材料
```xml
<name>quest_we_material_450</name>
<desc>STR_QUEST_WE_MATERIAL_450</desc>
<desc_long>STR_QUEST_WE_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_we_material_450` → 武器匠任务材料450
  - `STR_QUEST_WE_MATERIAL_450` → 武器匠任务材料450
  - `STR_QUEST_WE_MATERIAL_450_DESC` → 武器匠任务材料450详细说明

## 3. 黑暗阵营任务材料 (Dark Side Quest Materials)

### 黑暗炼金术士任务材料
```xml
<name>quest_d_al_material_450</name>
<desc>STR_QUEST_D_AL_MATERIAL_450</desc>
<desc_long>STR_QUEST_D_AL_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_d_al_material_450` → 黑暗炼金术士任务材料450
  - `STR_QUEST_D_AL_MATERIAL_450` → 黑暗炼金术士任务材料450
  - `STR_QUEST_D_AL_MATERIAL_450_DESC` → 黑暗炼金术士任务材料450详细说明

### 黑暗手工匠任务材料
```xml
<name>quest_d_ha_material_450</name>
<desc>STR_QUEST_D_HA_MATERIAL_450</desc>
<desc_long>STR_QUEST_D_HA_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_d_ha_material_450` → 黑暗手工匠任务材料450
  - `STR_QUEST_D_HA_MATERIAL_450` → 黑暗手工匠任务材料450
  - `STR_QUEST_D_HA_MATERIAL_450_DESC` → 黑暗手工匠任务材料450详细说明

### 黑暗武器匠任务材料
```xml
<name>quest_d_we_material_450</name>
<desc>STR_QUEST_D_WE_MATERIAL_450</desc>
<desc_long>STR_QUEST_D_WE_MATERIAL_450_DESC</desc_long>
```
- **中文翻译**:
  - `quest_d_we_material_450` → 黑暗武器匠任务材料450
  - `STR_QUEST_D_WE_MATERIAL_450` → 黑暗武器匠任务材料450
  - `STR_QUEST_D_WE_MATERIAL_450_DESC` → 黑暗武器匠任务材料450详细说明

## 4. 特殊任务材料 (Special Quest Materials)

### 烙印任务材料
```xml
<name>material_stigma_quest_01</name>
<desc>STR_MATERIAL_STIGMA_QUEST_01</desc>
<desc_long>STR_MATERIAL_STIGMA_QUEST_01_DESC</desc_long>
```
- **中文翻译**:
  - `material_stigma_quest_01` → 烙印任务材料01
  - `STR_MATERIAL_STIGMA_QUEST_01` → 烙印任务材料01
  - `STR_MATERIAL_STIGMA_QUEST_01_DESC` → 烙印任务材料01详细说明

### 黑暗烙印任务材料
```xml
<name>material_d_stigma_quest_01</name>
<desc>STR_MATERIAL_D_STIGMA_QUEST_01</desc>
<desc_long>STR_MATERIAL_D_STIGMA_QUEST_01_DESC</desc_long>
```
- **中文翻译**:
  - `material_d_stigma_quest_01` → 黑暗烙印任务材料01
  - `STR_MATERIAL_D_STIGMA_QUEST_01` → 黑暗烙印任务材料01
  - `STR_MATERIAL_D_STIGMA_QUEST_01_DESC` → 黑暗烙印任务材料01详细说明

### 任务材料币
```xml
<name>junk_coin_quest_material</name>
<desc>STR_JUNK_COIN_QUEST_MATERIAL</desc>
<desc_long>STR_JUNK_COIN_QUEST_MATERIAL_DESC</desc_long>
```
- **中文翻译**:
  - `junk_coin_quest_material` → 任务材料币
  - `STR_JUNK_COIN_QUEST_MATERIAL` → 任务材料币
  - `STR_JUNK_COIN_QUEST_MATERIAL_DESC` → 任务材料币详细说明

### 黑暗任务材料币
```xml
<name>junk_coin_d_quest_material</name>
<desc>STR_JUNK_COIN_D_QUEST_MATERIAL</desc>
<desc_long>STR_JUNK_COIN_D_QUEST_MATERIAL_DESC</desc_long>
```
- **中文翻译**:
  - `junk_coin_d_quest_material` → 黑暗任务材料币
  - `STR_JUNK_COIN_D_QUEST_MATERIAL` → 黑暗任务材料币
  - `STR_JUNK_COIN_D_QUEST_MATERIAL_DESC` → 黑暗任务材料币详细说明

### 卡伦任务材料
```xml
<name>junk_Kahrun_quest_material</name>
<desc>STR_JUNK_KAHRUN_QUEST_MATERIAL</desc>
<desc_long>STR_JUNK_KAHRUN_QUEST_MATERIAL_DESC</desc_long>
```
- **中文翻译**:
  - `junk_Kahrun_quest_material` → 卡伦任务材料
  - `STR_JUNK_KAHRUN_QUEST_MATERIAL` → 卡伦任务材料
  - `STR_JUNK_KAHRUN_QUEST_MATERIAL_DESC` → 卡伦任务材料详细说明

## 5. 任务配方材料 (Quest Recipe Materials)

### 40级任务配方材料
```xml
<extra_currency_item>junk_master_recipe_quest_40a</extra_currency_item>
<extra_currency_item_count>60</extra_currency_item_count>
```
- **中文翻译**:
  - `junk_master_recipe_quest_40a` → 40级大师配方任务材料
  - **说明**: 用于40级任务相关的大师级配方制作

### 50级任务配方材料
```xml
<extra_currency_item>junk_master_recipe_quest_50a</extra_currency_item>
<extra_currency_item_count>180</extra_currency_item_count>
```
- **中文翻译**:
  - `junk_master_recipe_quest_50a` → 50级大师配方任务材料
  - **说明**: 用于50级任务相关的大师级配方制作

## 6. 任务激活技能 (Quest Activation Skills)

### 任务驱散技能
```xml
<activation_skill>item_quest_dispell</activation_skill>
```
- **中文翻译**: 任务驱散技能
- **说明**: 用于任务中的驱散效果

### 特定任务技能
```xml
<activation_skill>ITEM_QUEST_Q41530a</activation_skill>
<activation_skill>ITEM_QUEST_Q41545a</activation_skill>
<activation_skill>ITEM_QUEST_Q41551a</activation_skill>
<activation_skill>ITEM_QUEST_Q41552a</activation_skill>
<activation_skill>ITEM_QUEST_Q41526b</activation_skill>
<activation_skill>item_quest_q30407</activation_skill>
```
- **中文翻译**:
  - `ITEM_QUEST_Q41530a` → 任务Q41530a激活技能
  - `ITEM_QUEST_Q41545a` → 任务Q41545a激活技能
  - `ITEM_QUEST_Q41551a` → 任务Q41551a激活技能
  - `ITEM_QUEST_Q41552a` → 任务Q41552a激活技能
  - `ITEM_QUEST_Q41526b` → 任务Q41526b激活技能
  - `item_quest_q30407` → 任务Q30407激活技能

## 7. 任务物品图标 (Quest Item Icons)

### 任务材料图标
```xml
<icon_name>Icon_Item_quest_material_id_S_PvE_M_60a</icon_name>
```
- **中文翻译**: 任务材料图标
- **说明**: PvE任务材料60级的图标

### 任务卷轴图标
```xml
<icon_name>icon_item_quest_scroll</icon_name>
```
- **中文翻译**: 任务卷轴图标
- **说明**: 任务卷轴类物品的图标

## 8. 字段命名规则说明

### 命名模式分析
- `quest_*_material_*` → 任务材料
- `quest_d_*_material_*` → 黑暗阵营任务材料
- `junk_*_quest_*` → 任务相关垃圾物品
- `material_*_quest_*` → 任务相关材料
- `ITEM_QUEST_Q*` → 特定任务ID的激活技能

### 等级标识
- `450` → 450级相关
- `40a` → 40级A类型
- `50a` → 50级A类型
- `60a` → 60级A类型
- `61a` → 61级A类型

### 阵营标识
- `d_` → 黑暗阵营 (Dark)
- 无前缀 → 光明阵营 (Light)
- `S_PvE_M` → 单人PvE男性 