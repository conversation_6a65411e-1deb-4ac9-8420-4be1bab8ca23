using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace SimpleWpfEditor
{
    /// <summary>
    /// NPC客户端数据模型
    /// </summary>
    [XmlRoot("npc")]
    public class NpcClient
    {
        // 基础信息
        [XmlElement("id")]
        public int Id { get; set; }

        [XmlElement("name")]
        public string Name { get; set; } = string.Empty;

        [XmlElement("desc")]
        public string Desc { get; set; } = string.Empty;

        [XmlElement("__spawn_zonename__")]
        public string SpawnZoneName { get; set; } = string.Empty;

        [XmlElement("dir")]
        public string Dir { get; set; } = string.Empty;

        [XmlElement("mesh")]
        public string Mesh { get; set; } = string.Empty;

        [XmlElement("npc_title")]
        public string NpcTitle { get; set; } = string.Empty;

        [XmlElement("disk_type")]
        public string DiskType { get; set; } = "D3";

        // 外观和显示
        [XmlElement("visible_equipments")]
        public VisibleEquipments VisibleEquipments { get; set; } = new VisibleEquipments();

        [XmlElement("bound_radius")]
        public BoundRadius BoundRadius { get; set; } = new BoundRadius();

        [XmlElement("scale")]
        public int Scale { get; set; } = 100;

        [XmlElement("weapon_scale")]
        public int WeaponScale { get; set; } = 100;

        [XmlElement("altitude")]
        public float Altitude { get; set; } = 0.0f;

        [XmlElement("no_check_animation")]
        public int NoCheckAnimation { get; set; } = 0;

        [XmlElement("dmg_texture")]
        public int DmgTexture { get; set; } = 0;

        [XmlElement("ui_type")]
        public string UiType { get; set; } = string.Empty;

        [XmlElement("hide_path")]
        public int HidePath { get; set; } = 0;

        [XmlElement("hide_map")]
        public int HideMap { get; set; } = 0;

        // 移动属性
        [XmlElement("move_speed_normal_walk")]
        public float MoveSpeedNormalWalk { get; set; } = 1.38f;

        [XmlElement("move_speed_normal_run")]
        public float MoveSpeedNormalRun { get; set; } = 6.0f;

        [XmlElement("move_speed_combat_run")]
        public float MoveSpeedCombatRun { get; set; } = 7.0f;

        [XmlElement("ammo_speed")]
        public int AmmoSpeed { get; set; } = 0;

        [XmlElement("pushed_range")]
        public float PushedRange { get; set; } = 2.0f;

        [XmlElement("dual")]
        public int Dual { get; set; } = 0;

        // 设计参考
        [XmlElement("__design_reference__")]
        public string DesignReference { get; set; } = string.Empty;

        // 基础属性
        [XmlElement("level")]
        public int Level { get; set; } = 1;

        [XmlElement("exp")]
        public int Exp { get; set; } = 0;

        [XmlElement("dp")]
        public int Dp { get; set; } = 100;

        [XmlElement("max_hp")]
        public int MaxHp { get; set; } = 100;

        [XmlElement("hpgauge_level")]
        public int HpgaugeLevel { get; set; } = 1;

        [XmlElement("hp_regen")]
        public int HpRegen { get; set; } = 0;

        // 攻击属性
        [XmlElement("physical_attack")]
        public int PhysicalAttack { get; set; } = 0;

        [XmlElement("physical_defend")]
        public int PhysicalDefend { get; set; } = 0;

        [XmlElement("magical_attack")]
        public int MagicalAttack { get; set; } = 0;

        [XmlElement("magical_defend")]
        public int MagicalDefend { get; set; } = 0;

        [XmlElement("magical_resist")]
        public int MagicalResist { get; set; } = 0;

        [XmlElement("min_damage")]
        public int MinDamage { get; set; } = 0;

        [XmlElement("max_damage")]
        public int MaxDamage { get; set; } = 0;

        [XmlElement("magical_skill_boost")]
        public int MagicalSkillBoost { get; set; } = 0;

        [XmlElement("magical_skill_boost_resist")]
        public int MagicalSkillBoostResist { get; set; } = 0;

        [XmlElement("hit_count")]
        public int HitCount { get; set; } = 1;

        [XmlElement("attack_delay")]
        public int AttackDelay { get; set; } = 2000;

        [XmlElement("hit_accuracy")]
        public int HitAccuracy { get; set; } = 0;

        [XmlElement("magical_hit_accuracy")]
        public int MagicalHitAccuracy { get; set; } = 0;

        // 暴击属性
        [XmlElement("critical")]
        public int Critical { get; set; } = 0;

        [XmlElement("physical_critical_reduce_rate")]
        public int PhysicalCriticalReduceRate { get; set; } = 0;

        [XmlElement("physical_critical_damage_reduce")]
        public int PhysicalCriticalDamageReduce { get; set; } = 0;

        [XmlElement("magical_critical")]
        public int MagicalCritical { get; set; } = 0;

        [XmlElement("magical_critical_reduce_rate")]
        public int MagicalCriticalReduceRate { get; set; } = 0;

        [XmlElement("magical_critical_damage_reduce")]
        public int MagicalCriticalDamageReduce { get; set; } = 0;

        // 防御属性
        [XmlElement("concentration")]
        public int Concentration { get; set; } = 0;

        [XmlElement("dodge")]
        public int Dodge { get; set; } = 0;

        [XmlElement("parry")]
        public int Parry { get; set; } = 0;

        [XmlElement("block")]
        public int Block { get; set; } = 0;

        [XmlElement("dp_reduce")]
        public int DpReduce { get; set; } = 0;

        // 深渊属性
        [XmlElement("abyss_point")]
        public int AbyssPoint { get; set; } = 0;

        [XmlElement("abyss_rank")]
        public int AbyssRank { get; set; } = 1;

        [XmlElement("cancel_level")]
        public int CancelLevel { get; set; } = 50;

        // 元素防御
        [XmlElement("first_level_attr")]
        public FirstLevelAttr FirstLevelAttr { get; set; } = new FirstLevelAttr();

        // 异常状态抗性
        [XmlArray("abnormal_resists")]
        [XmlArrayItem("data")]
        public List<AbnormalResist> AbnormalResists { get; set; } = new List<AbnormalResist>();

        // 异常状态穿透
        [XmlArray("abnormal_penetrations")]
        [XmlArrayItem("data")]
        public List<AbnormalPenetration> AbnormalPenetrations { get; set; } = new List<AbnormalPenetration>();

        // 伤害修正
        [XmlElement("physical_damage_trim")]
        public int PhysicalDamageTrim { get; set; } = 0;

        [XmlElement("magical_damage_trim")]
        public int MagicalDamageTrim { get; set; } = 0;

        [XmlElement("noreducespell_immune")]
        public int NoreducespellImmune { get; set; } = 0;

        [XmlElement("first_attack_split_rate")]
        public string FirstAttackSplitRate { get; set; } = "Split_Default";

        // AI和行为
        [XmlElement("game_lang")]
        public string GameLang { get; set; } = string.Empty;

        [XmlElement("ai_name")]
        public string AiName { get; set; } = "NPC";

        [XmlElement("idle_name")]
        public string IdleName { get; set; } = "NoMove";

        [XmlElement("tribe")]
        public string Tribe { get; set; } = string.Empty;

        [XmlElement("race_type")]
        public string RaceType { get; set; } = string.Empty;

        [XmlElement("pet_ai_name")]
        public string PetAiName { get; set; } = "Pet";

        // 感知和追击
        [XmlElement("max_enemy_count")]
        public int MaxEnemyCount { get; set; } = 10;

        [XmlElement("sensory_range")]
        public float SensoryRange { get; set; } = 8.0f;

        [XmlElement("sensory_range_short")]
        public float SensoryRangeShort { get; set; } = 4.0f;

        [XmlElement("sensory_angle")]
        public int SensoryAngle { get; set; } = 240;

        [XmlElement("max_chase_time")]
        public int MaxChaseTime { get; set; } = 8;

        [XmlElement("chase_range_fromsp")]
        public float ChaseRangeFromsp { get; set; } = 0.0f;

        [XmlElement("chase_flying")]
        public int ChaseFlying { get; set; } = 0;

        [XmlElement("react_to_pathfind_fail")]
        public string ReactToPathfindFail { get; set; } = "return_to_sp";

        // 移动行为
        [XmlElement("move_speed_flee")]
        public int MoveSpeedFlee { get; set; } = 70;

        [XmlElement("move_type_return")]
        public string MoveTypeReturn { get; set; } = "run";

        [XmlElement("move_speed_return")]
        public int MoveSpeedReturn { get; set; } = 200;

        [XmlElement("decrease_sensory_range_return")]
        public int DecreaseSensoryRangeReturn { get; set; } = 40;

        // 仇恨系统
        [XmlElement("hate_decrease_type")]
        public int HateDecreaseType { get; set; } = 0;

        [XmlElement("hate_decrease_a")]
        public int HateDecreaseA { get; set; } = 364;

        [XmlElement("hate_decrease_b")]
        public int HateDecreaseB { get; set; } = 0;

        // 特殊能力
        [XmlElement("can_see_invisible")]
        public int CanSeeInvisible { get; set; } = 0;

        [XmlElement("can_see_polymorph")]
        public string CanSeePolymorph { get; set; } = "FALSE";

        [XmlElement("can_pass_wall")]
        public int CanPassWall { get; set; } = 0;

        // 攻击和技能
        [XmlArray("attacks")]
        [XmlArrayItem("data")]
        public List<NpcAttack> Attacks { get; set; } = new List<NpcAttack>();

        [XmlArray("skills")]
        [XmlArrayItem("data")]
        public List<NpcSkill> Skills { get; set; } = new List<NpcSkill>();

        // NPC类型和状态
        [XmlElement("npc_type")]
        public string NpcType { get; set; } = "Monster";

        [XmlElement("unattackable")]
        public int Unattackable { get; set; } = 0;

        [XmlElement("verbosity")]
        public float Verbosity { get; set; } = 0.5f;

        // 对话系统
        [XmlArray("gossip_list")]
        [XmlArrayItem("data")]
        public List<NpcGossip> GossipList { get; set; } = new List<NpcGossip>();
    }

    // 辅助类定义
    public class VisibleEquipments
    {
        [XmlElement("main")]
        public string Main { get; set; } = string.Empty;

        [XmlElement("sub")]
        public string Sub { get; set; } = string.Empty;

        [XmlElement("head")]
        public string Head { get; set; } = string.Empty;

        [XmlElement("torso")]
        public string Torso { get; set; } = string.Empty;

        [XmlElement("leg")]
        public string Leg { get; set; } = string.Empty;

        [XmlElement("hand")]
        public string Hand { get; set; } = string.Empty;

        [XmlElement("foot")]
        public string Foot { get; set; } = string.Empty;
    }

    public class BoundRadius
    {
        [XmlElement("front")]
        public float Front { get; set; } = 2.0f;

        [XmlElement("side")]
        public float Side { get; set; } = 2.0f;

        [XmlElement("upper")]
        public float Upper { get; set; } = 2.0f;
    }

    public class FirstLevelAttr
    {
        [XmlElement("elemental_defend_light")]
        public int ElementalDefendLight { get; set; } = 0;

        [XmlElement("elemental_defend_dark")]
        public int ElementalDefendDark { get; set; } = 0;

        [XmlElement("elemental_defend_earth")]
        public int ElementalDefendEarth { get; set; } = 0;

        [XmlElement("elemental_defend_air")]
        public int ElementalDefendAir { get; set; } = 0;

        [XmlElement("elemental_defend_water")]
        public int ElementalDefendWater { get; set; } = 0;

        [XmlElement("elemental_defend_fire")]
        public int ElementalDefendFire { get; set; } = 0;
    }

    public class AbnormalResist
    {
        [XmlElement("abnormal_resist_name")]
        public string AbnormalResistName { get; set; } = string.Empty;

        [XmlElement("abnormal_resist_value")]
        public int AbnormalResistValue { get; set; } = 0;
    }

    public class AbnormalPenetration
    {
        [XmlElement("abnormal_penetration_value")]
        public int AbnormalPenetrationValue { get; set; } = 0;
    }

    public class NpcAttack
    {
        [XmlElement("attack_mod")]
        public int AttackMod { get; set; } = 100;

        [XmlElement("attack_range")]
        public float AttackRange { get; set; } = 2.0f;

        [XmlElement("attack_rate")]
        public int AttackRate { get; set; } = 1000;

        [XmlElement("attack_type")]
        public string AttackType { get; set; } = "physical";
    }

    public class NpcSkill
    {
        [XmlElement("skill_name")]
        public string SkillName { get; set; } = string.Empty;

        [XmlElement("skill_level")]
        public int SkillLevel { get; set; } = 1;

        [XmlElement("skill_rate")]
        public int SkillRate { get; set; } = 0;
    }

    public class NpcGossip
    {
        [XmlElement("gossip")]
        public string Gossip { get; set; } = string.Empty;
    }
}
