using System.Text;

Console.WriteLine("=================================");
Console.WriteLine("   XML字段顺序验证测试");
Console.WriteLine("=================================");
Console.WriteLine();

try
{
    Console.WriteLine("✓ 测试正确的XML字段顺序:");
    Console.WriteLine();
    
    // 使用StringBuilder生成正确顺序的XML
    var xml = new StringBuilder();
    xml.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
    xml.AppendLine("<client_items>");
    xml.AppendLine("  <client_item>");
    
    // 按照您要求的正确顺序
    xml.AppendLine("    <id>187000905</id>");
    xml.AppendLine("    <name>custom_hz_wing_rank14_30day</name>");
    xml.AppendLine("    <desc>CUSTOM_HZ_WING_RANK14_30DAY</desc>");
    xml.AppendLine("    <armor_type>clothes</armor_type>");
    xml.AppendLine("    <item_type>normal</item_type>");
    xml.AppendLine("    <material>mat_flesh</material>");
    
    // 价格 - 关键位置
    xml.AppendLine("    <price>5</price>");
    
    // 交易字段 - 必须紧跟在price之后
    xml.AppendLine("    <trade_in_item_list>");
    xml.AppendLine("      <data>");
    xml.AppendLine("        <trade_in_item>black_aion_toll_32</trade_in_item>");
    xml.AppendLine("        <trade_in_item_count>10000</trade_in_item_count>");
    xml.AppendLine("      </data>");
    xml.AppendLine("    </trade_in_item_list>");
    
    // 其他字段 - 在交易字段之后
    xml.AppendLine("    <max_stack_count>1</max_stack_count>");
    xml.AppendLine("    <quality>epic</quality>");
    xml.AppendLine("    <level>40</level>");
    xml.AppendLine("    <lore>FALSE</lore>");
    xml.AppendLine("    <can_exchange>FALSE</can_exchange>");
    xml.AppendLine("    <can_sell_to_npc>FALSE</can_sell_to_npc>");
    xml.AppendLine("    <can_deposit_to_character_warehouse>TRUE</can_deposit_to_character_warehouse>");
    xml.AppendLine("    <can_deposit_to_account_warehouse>FALSE</can_deposit_to_account_warehouse>");
    xml.AppendLine("    <can_deposit_to_guild_warehouse>FALSE</can_deposit_to_guild_warehouse>");
    xml.AppendLine("    <item_drop_permitted>FALSE</item_drop_permitted>");
    xml.AppendLine("    <breakable>TRUE</breakable>");
    xml.AppendLine("    <soul_bind>FALSE</soul_bind>");
    xml.AppendLine("    <remove_when_logout>FALSE</remove_when_logout>");
    xml.AppendLine("    <race_permitted>pc_light pc_dark</race_permitted>");
    xml.AppendLine("    <gender_permitted>all</gender_permitted>");
    
    // 职业限制
    xml.AppendLine("    <warrior>1</warrior>");
    xml.AppendLine("    <scout>1</scout>");
    xml.AppendLine("    <mage>1</mage>");
    xml.AppendLine("    <cleric>1</cleric>");
    xml.AppendLine("    <engineer>1</engineer>");
    xml.AppendLine("    <artist>1</artist>");
    xml.AppendLine("    <fighter>1</fighter>");
    xml.AppendLine("    <knight>1</knight>");
    xml.AppendLine("    <assassin>1</assassin>");
    xml.AppendLine("    <ranger>1</ranger>");
    xml.AppendLine("    <wizard>1</wizard>");
    xml.AppendLine("    <elementalist>1</elementalist>");
    xml.AppendLine("    <chanter>1</chanter>");
    xml.AppendLine("    <priest>1</priest>");
    xml.AppendLine("    <gunner>1</gunner>");
    xml.AppendLine("    <bard>1</bard>");
    xml.AppendLine("    <rider>1</rider>");
    xml.AppendLine("    <bonus_apply>equip</bonus_apply>");
    
    xml.AppendLine("  </client_item>");
    xml.AppendLine("</client_items>");
    
    // 保存XML文件
    var outputPath = "correct_order_test.xml";
    await File.WriteAllTextAsync(outputPath, xml.ToString(), Encoding.UTF8);
    
    Console.WriteLine($"✅ XML文件已生成: {outputPath}");
    Console.WriteLine();
    
    // 验证字段顺序
    var lines = xml.ToString().Split('\n');
    
    // 查找关键字段的位置
    int priceLineIndex = -1;
    int tradeInListLineIndex = -1;
    int maxStackLineIndex = -1;
    
    for (int i = 0; i < lines.Length; i++)
    {
        var line = lines[i].Trim();
        if (line.Contains("<price>")) priceLineIndex = i;
        if (line.Contains("<trade_in_item_list>")) tradeInListLineIndex = i;
        if (line.Contains("<max_stack_count>")) maxStackLineIndex = i;
    }
    
    Console.WriteLine($"字段位置验证:");
    Console.WriteLine($"  price 位置: 第 {priceLineIndex + 1} 行");
    Console.WriteLine($"  trade_in_item_list 位置: 第 {tradeInListLineIndex + 1} 行");
    Console.WriteLine($"  max_stack_count 位置: 第 {maxStackLineIndex + 1} 行");
    Console.WriteLine();
    
    // 验证顺序是否正确
    bool orderCorrect = priceLineIndex < tradeInListLineIndex && 
                       tradeInListLineIndex < maxStackLineIndex;
    
    if (orderCorrect)
    {
        Console.WriteLine("✅ 字段顺序完全正确！");
        Console.WriteLine("   price → trade_in_item_list → max_stack_count");
        Console.WriteLine();
        Console.WriteLine("这就是您要求的正确顺序:");
        Console.WriteLine("  1. <price>5</price>");
        Console.WriteLine("  2. <trade_in_item_list>");
        Console.WriteLine("       <data>");
        Console.WriteLine("         <trade_in_item>black_aion_toll_32</trade_in_item>");
        Console.WriteLine("         <trade_in_item_count>10000</trade_in_item_count>");
        Console.WriteLine("       </data>");
        Console.WriteLine("     </trade_in_item_list>");
        Console.WriteLine("  3. <max_stack_count>1</max_stack_count>");
    }
    else
    {
        Console.WriteLine("❌ 字段顺序不正确！");
    }
    
    Console.WriteLine();
    Console.WriteLine("=================================");
    Console.WriteLine("✓ 正确的XML字段顺序示例完成！");
    Console.WriteLine("=================================");
    Console.WriteLine();
    Console.WriteLine("现在需要确保SimpleWpfEditor使用这个顺序");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 测试过程中出现错误: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

Console.WriteLine();
Console.WriteLine("按任意键退出...");
Console.ReadKey();
