using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;

public class ClientSkill
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string Desc { get; set; } = "";
    public string SkillType { get; set; } = "";
    public int Level { get; set; }
    public int MaxLevel { get; set; }
    public int MpConsume { get; set; }
    public int HpConsume { get; set; }
    public int DpConsume { get; set; }
    public int CastTime { get; set; }
    public int CoolTime { get; set; }
    public int Duration { get; set; }
    public string TargetType { get; set; } = "";
    public string TargetRange { get; set; } = "";
    public int TargetMaxCount { get; set; }
    public string EffectType { get; set; } = "";
    public int EffectValue { get; set; }
    public string Animation { get; set; } = "";
    public string Icon { get; set; } = "";
}

class Program
{
    static void Main()
    {
        string filePath = @"c:\Users\<USER>\Desktop\aion4.6.1\client_skills.xml";

        try
        {
            Console.WriteLine("开始测试完整的技能解析流程...");
            Console.WriteLine($"文件路径: {filePath}");

            var skills = LoadSkillsFromXml(filePath);
            Console.WriteLine($"最终加载的技能数量: {skills.Count}");

            foreach (var skill in skills)
            {
                Console.WriteLine($"技能: {skill.Id} - {skill.Name} ({skill.SkillType})");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    static List<ClientSkill> LoadSkillsFromXml(string filePath)
    {
        var skills = new List<ClientSkill>();

        if (!File.Exists(filePath))
        {
            Console.WriteLine("文件不存在");
            return skills;
        }

        var xmlContent = File.ReadAllText(filePath);
        var xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlContent);

        Console.WriteLine($"根节点: {xmlDoc.DocumentElement?.Name}");

        var skillNodes = xmlDoc.SelectNodes("//client_skill");
        Console.WriteLine($"找到 {skillNodes?.Count ?? 0} 个技能节点");

        if (skillNodes == null)
        {
            Console.WriteLine("未找到技能节点");
            return skills;
        }

        foreach (XmlNode skillNode in skillNodes)
        {
            try
            {
                var skill = ParseSkillFromXml(skillNode);
                skills.Add(skill);
                Console.WriteLine($"成功解析技能: {skill.Id} - {skill.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析技能时出错: {ex.Message}");
            }
        }

        return skills;
    }

    static ClientSkill ParseSkillFromXml(XmlNode skillNode)
    {
        var skill = new ClientSkill
        {
            Id = GetIntValue(skillNode, "id"),
            Name = GetStringValue(skillNode, "name"),
            Desc = GetStringValue(skillNode, "desc"),
            SkillType = GetStringValue(skillNode, "skill_type"),
            Level = GetIntValue(skillNode, "level", 1),
            MaxLevel = GetIntValue(skillNode, "max_level", 1),
            MpConsume = GetIntValue(skillNode, "mp_consume"),
            HpConsume = GetIntValue(skillNode, "hp_consume"),
            DpConsume = GetIntValue(skillNode, "dp_consume"),
            CastTime = GetIntValue(skillNode, "cast_time"),
            CoolTime = GetIntValue(skillNode, "cool_time"),
            Duration = GetIntValue(skillNode, "duration"),
            TargetType = GetStringValue(skillNode, "target_type"),
            TargetRange = GetStringValue(skillNode, "target_range"),
            TargetMaxCount = GetIntValue(skillNode, "target_max_count", 1),
            EffectType = GetStringValue(skillNode, "effect_type"),
            EffectValue = GetIntValue(skillNode, "effect_value"),
            Animation = GetStringValue(skillNode, "animation"),
            Icon = GetStringValue(skillNode, "icon")
        };

        return skill;
    }

    static string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText ?? defaultValue;
    }

    static int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText, out var value))
        {
            return value;
        }
        return defaultValue;
    }
}
