using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;

namespace LinkTest;

/// <summary>
/// 简化的客户端物品数据模型
/// </summary>
[XmlRoot("client_item")]
public class ClientItem
{
    [XmlElement("id")]
    [Required(ErrorMessage = "物品ID不能为空")]
    public int Id { get; set; }

    [XmlElement("name")]
    [Required(ErrorMessage = "物品名称不能为空")]
    public string Name { get; set; } = string.Empty;

    [XmlElement("desc")]
    public string Desc { get; set; } = string.Empty;

    [XmlElement("armor_type")]
    public string ArmorType { get; set; } = string.Empty;

    [XmlElement("item_type")]
    public string ItemType { get; set; } = "normal";

    [XmlElement("material")]
    public string Material { get; set; } = string.Empty;

    [XmlElement("icon_name")]
    public string IconName { get; set; } = string.Empty;

    [XmlElement("price")]
    [Range(0, int.MaxValue, ErrorMessage = "价格不能为负数")]
    public int Price { get; set; }

    [XmlElement("max_stack_count")]
    [Range(1, 999, ErrorMessage = "堆叠数量必须在1-999之间")]
    public int MaxStackCount { get; set; } = 1;

    [XmlElement("can_sell_to_npc")]
    public bool CanSellToNpc { get; set; } = true;

    [XmlElement("can_deposit_to_character_warehouse")]
    public bool CanDepositToCharacterWarehouse { get; set; } = true;

    [XmlElement("can_deposit_to_account_warehouse")]
    public bool CanDepositToAccountWarehouse { get; set; } = true;

    [XmlElement("can_deposit_to_guild_warehouse")]
    public bool CanDepositToGuildWarehouse { get; set; } = true;

    [XmlElement("equipment_slots")]
    public string EquipmentSlots { get; set; } = string.Empty;

    [XmlElement("breakable")]
    public bool Breakable { get; set; } = true;

    [XmlElement("soul_bind")]
    public bool SoulBind { get; set; } = false;

    [XmlElement("remove_when_logout")]
    public bool RemoveWhenLogout { get; set; } = false;

    [XmlElement("gender_permitted")]
    public string GenderPermitted { get; set; } = "all";

    // 职业限制
    [XmlElement("warrior")]
    public int Warrior { get; set; } = 1;

    [XmlElement("scout")]
    public int Scout { get; set; } = 1;

    [XmlElement("mage")]
    public int Mage { get; set; } = 1;

    [XmlElement("cleric")]
    public int Cleric { get; set; } = 1;

    [XmlElement("engineer")]
    public int Engineer { get; set; } = 1;

    [XmlElement("artist")]
    public int Artist { get; set; } = 1;

    [XmlElement("fighter")]
    public int Fighter { get; set; } = 1;

    [XmlElement("knight")]
    public int Knight { get; set; } = 1;

    [XmlElement("assassin")]
    public int Assassin { get; set; } = 1;

    [XmlElement("ranger")]
    public int Ranger { get; set; } = 1;

    [XmlElement("wizard")]
    public int Wizard { get; set; } = 1;

    [XmlElement("elementalist")]
    public int Elementalist { get; set; } = 1;

    [XmlElement("chanter")]
    public int Chanter { get; set; } = 1;

    [XmlElement("priest")]
    public int Priest { get; set; } = 1;

    [XmlElement("gunner")]
    public int Gunner { get; set; } = 1;

    [XmlElement("bard")]
    public int Bard { get; set; } = 1;

    [XmlElement("rider")]
    public int Rider { get; set; } = 1;

    // 强化属性
    [XmlElement("option_slot_value")]
    public int OptionSlotValue { get; set; } = 0;

    [XmlElement("special_slot_value")]
    public int SpecialSlotValue { get; set; } = 0;

    [XmlElement("option_slot_bonus")]
    public int OptionSlotBonus { get; set; } = 0;

    [XmlElement("bonus_apply")]
    public string BonusApply { get; set; } = "equip";

    [XmlElement("no_enchant")]
    public bool NoEnchant { get; set; } = false;

    [XmlElement("max_enchant_value")]
    public int MaxEnchantValue { get; set; } = 15;

    [XmlElement("can_proc_enchant")]
    public bool CanProcEnchant { get; set; } = true;

    [XmlElement("can_composite_weapon")]
    public bool CanCompositeWeapon { get; set; } = true;

    // 外观和音效
    [XmlElement("cannot_changeskin")]
    public int CannotChangeskin { get; set; } = 0;

    [XmlElement("ui_sound_type")]
    public string UiSoundType { get; set; } = string.Empty;

    [XmlElement("mesh")]
    public string Mesh { get; set; } = string.Empty;

    [XmlElement("mesh_change")]
    public int MeshChange { get; set; } = 0;

    [XmlElement("can_dye")]
    public int CanDye { get; set; } = 0;

    // 特殊属性
    [XmlElement("cash_item")]
    public int CashItem { get; set; } = 0;

    [XmlElement("can_split")]
    public bool CanSplit { get; set; } = false;

    [XmlElement("item_drop_permitted")]
    public bool ItemDropPermitted { get; set; } = false;

    [XmlElement("bm_restrict_category")]
    public int BmRestrictCategory { get; set; } = 1;

    [XmlElement("can_ap_extraction")]
    public bool CanApExtraction { get; set; } = false;

    [XmlElement("can_polish")]
    public bool CanPolish { get; set; } = true;

    [XmlElement("polish_burn_on_attack")]
    public int PolishBurnOnAttack { get; set; } = 29;

    [XmlElement("polish_burn_on_defend")]
    public int PolishBurnOnDefend { get; set; } = 12;

    [XmlElement("race_permitted")]
    public string RacePermitted { get; set; } = "pc_light pc_dark";

    /// <summary>
    /// 获取物品的显示名称
    /// </summary>
    public string DisplayName => $"[{Id}] {Name}";

    /// <summary>
    /// 获取本地化的显示名称
    /// </summary>
    public string GetLocalizedDisplayName(StringService? stringService)
    {
        if (stringService == null)
            return DisplayName;

        var localizedName = stringService.GetString(Desc);
        if (!string.IsNullOrEmpty(localizedName) && localizedName != Desc)
        {
            return $"[{Id}] {localizedName}";
        }

        return DisplayName;
    }

    /// <summary>
    /// 获取本地化的描述文本
    /// </summary>
    public string GetLocalizedDescription(StringService? stringService)
    {
        if (stringService == null || string.IsNullOrEmpty(Desc))
            return Desc;

        return stringService.GetString(Desc);
    }

    /// <summary>
    /// 检查物品是否有效
    /// </summary>
    public bool IsValid()
    {
        return Id > 0 && !string.IsNullOrWhiteSpace(Name);
    }
}
