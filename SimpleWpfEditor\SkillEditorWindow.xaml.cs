using System;
using System.Windows;
using System.Windows.Controls;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 技能编辑窗口
    /// </summary>
    public partial class SkillEditorWindow : Window
    {
        private readonly ClientSkill _originalSkill;
        private ClientSkill _editingSkill;

        public bool DataChanged { get; private set; } = false;
        public ClientSkill EditedSkill => _editingSkill;

        public SkillEditorWindow(ClientSkill skill)
        {
            InitializeComponent();
            
            _originalSkill = skill;
            _editingSkill = CloneSkill(skill);
            
            LoadSkillData();
            BindEvents();
        }

        /// <summary>
        /// 克隆技能对象
        /// </summary>
        private ClientSkill CloneSkill(ClientSkill original)
        {
            return new ClientSkill
            {
                Id = original.Id,
                Name = original.Name,
                Desc = original.Desc,
                SkillType = original.SkillType,
                Level = original.Level,
                MaxLevel = original.MaxLevel,
                MpConsume = original.MpConsume,
                HpConsume = original.HpConsume,
                DpConsume = original.DpConsume,
                CastTime = original.CastTime,
                CoolTime = original.CoolTime,
                Duration = original.Duration,
                TargetType = original.TargetType,
                TargetRange = original.TargetRange,
                TargetMaxCount = original.TargetMaxCount,
                EffectType = original.EffectType,
                EffectValue = original.EffectValue,
                Animation = original.Animation,
                Icon = original.Icon
            };
        }

        /// <summary>
        /// 加载技能数据到界面
        /// </summary>
        private void LoadSkillData()
        {
            IdTextBox.Text = _editingSkill.Id.ToString();
            NameTextBox.Text = _editingSkill.Name;
            DescTextBox.Text = _editingSkill.Desc;
            
            // 设置技能类型
            SetComboBoxValue(SkillTypeComboBox, _editingSkill.SkillType);
            
            LevelTextBox.Text = _editingSkill.Level.ToString();
            MaxLevelTextBox.Text = _editingSkill.MaxLevel.ToString();
            
            // 消耗属性
            MpConsumeTextBox.Text = _editingSkill.MpConsume.ToString();
            HpConsumeTextBox.Text = _editingSkill.HpConsume.ToString();
            DpConsumeTextBox.Text = _editingSkill.DpConsume.ToString();
            
            // 时间属性
            CastTimeTextBox.Text = _editingSkill.CastTime.ToString();
            CoolTimeTextBox.Text = _editingSkill.CoolTime.ToString();
            DurationTextBox.Text = _editingSkill.Duration.ToString();
            
            // 目标属性
            SetComboBoxValue(TargetTypeComboBox, _editingSkill.TargetType);
            TargetRangeTextBox.Text = _editingSkill.TargetRange;
            TargetMaxCountTextBox.Text = _editingSkill.TargetMaxCount.ToString();
            
            // 效果属性
            EffectTypeTextBox.Text = _editingSkill.EffectType;
            EffectValueTextBox.Text = _editingSkill.EffectValue.ToString();
            
            // 显示属性
            AnimationTextBox.Text = _editingSkill.Animation;
            IconTextBox.Text = _editingSkill.Icon;
            
            DataChanged = false;
        }

        /// <summary>
        /// 设置ComboBox的值
        /// </summary>
        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        /// <summary>
        /// 获取ComboBox的值
        /// </summary>
        private string GetComboBoxValue(ComboBox comboBox)
        {
            return (comboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "";
        }

        /// <summary>
        /// 绑定事件
        /// </summary>
        private void BindEvents()
        {
            // 基础信息变更事件
            IdTextBox.TextChanged += (s, e) => { if (int.TryParse(IdTextBox.Text, out var id)) { _editingSkill.Id = id; DataChanged = true; } };
            NameTextBox.TextChanged += (s, e) => { _editingSkill.Name = NameTextBox.Text; DataChanged = true; };
            DescTextBox.TextChanged += (s, e) => { _editingSkill.Desc = DescTextBox.Text; DataChanged = true; };
            SkillTypeComboBox.SelectionChanged += (s, e) => { _editingSkill.SkillType = GetComboBoxValue(SkillTypeComboBox); DataChanged = true; };
            LevelTextBox.TextChanged += (s, e) => { if (int.TryParse(LevelTextBox.Text, out var level)) { _editingSkill.Level = level; DataChanged = true; } };
            MaxLevelTextBox.TextChanged += (s, e) => { if (int.TryParse(MaxLevelTextBox.Text, out var maxLevel)) { _editingSkill.MaxLevel = maxLevel; DataChanged = true; } };

            // 消耗属性变更事件
            MpConsumeTextBox.TextChanged += (s, e) => { if (int.TryParse(MpConsumeTextBox.Text, out var mp)) { _editingSkill.MpConsume = mp; DataChanged = true; } };
            HpConsumeTextBox.TextChanged += (s, e) => { if (int.TryParse(HpConsumeTextBox.Text, out var hp)) { _editingSkill.HpConsume = hp; DataChanged = true; } };
            DpConsumeTextBox.TextChanged += (s, e) => { if (int.TryParse(DpConsumeTextBox.Text, out var dp)) { _editingSkill.DpConsume = dp; DataChanged = true; } };

            // 时间属性变更事件
            CastTimeTextBox.TextChanged += (s, e) => { if (int.TryParse(CastTimeTextBox.Text, out var castTime)) { _editingSkill.CastTime = castTime; DataChanged = true; } };
            CoolTimeTextBox.TextChanged += (s, e) => { if (int.TryParse(CoolTimeTextBox.Text, out var coolTime)) { _editingSkill.CoolTime = coolTime; DataChanged = true; } };
            DurationTextBox.TextChanged += (s, e) => { if (int.TryParse(DurationTextBox.Text, out var duration)) { _editingSkill.Duration = duration; DataChanged = true; } };

            // 目标属性变更事件
            TargetTypeComboBox.SelectionChanged += (s, e) => { _editingSkill.TargetType = GetComboBoxValue(TargetTypeComboBox); DataChanged = true; };
            TargetRangeTextBox.TextChanged += (s, e) => { _editingSkill.TargetRange = TargetRangeTextBox.Text; DataChanged = true; };
            TargetMaxCountTextBox.TextChanged += (s, e) => { if (int.TryParse(TargetMaxCountTextBox.Text, out var count)) { _editingSkill.TargetMaxCount = count; DataChanged = true; } };

            // 效果属性变更事件
            EffectTypeTextBox.TextChanged += (s, e) => { _editingSkill.EffectType = EffectTypeTextBox.Text; DataChanged = true; };
            EffectValueTextBox.TextChanged += (s, e) => { if (int.TryParse(EffectValueTextBox.Text, out var value)) { _editingSkill.EffectValue = value; DataChanged = true; } };

            // 显示属性变更事件
            AnimationTextBox.TextChanged += (s, e) => { _editingSkill.Animation = AnimationTextBox.Text; DataChanged = true; };
            IconTextBox.TextChanged += (s, e) => { _editingSkill.Icon = IconTextBox.Text; DataChanged = true; };
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证数据
                if (string.IsNullOrWhiteSpace(_editingSkill.Name))
                {
                    MessageBox.Show("技能名称不能为空！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    NameTextBox.Focus();
                    return;
                }

                if (_editingSkill.Id <= 0)
                {
                    MessageBox.Show("技能ID必须大于0！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    IdTextBox.Focus();
                    return;
                }

                if (_editingSkill.Level <= 0)
                {
                    MessageBox.Show("技能等级必须大于0！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    LevelTextBox.Focus();
                    return;
                }

                if (_editingSkill.MaxLevel < _editingSkill.Level)
                {
                    MessageBox.Show("最大等级不能小于当前等级！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    MaxLevelTextBox.Focus();
                    return;
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
