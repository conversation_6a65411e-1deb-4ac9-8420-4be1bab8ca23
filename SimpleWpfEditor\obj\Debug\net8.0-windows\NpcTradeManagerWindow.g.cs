﻿#pragma checksum "..\..\..\NpcTradeManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "05E2AEBDD5DC35721DE3F314E46A8DC7A77BD73D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// NpcTradeManagerWindow
    /// </summary>
    public partial class NpcTradeManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveFileButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveWithServerConversionButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TradeTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WeaponTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FactionFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTradeListsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgItemsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilteredCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TradeListsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\NpcTradeManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilePathTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/npctrademanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\NpcTradeManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            return;
            case 2:
            this.SaveFileButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.SaveWithServerConversionButton = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TradeTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.WeaponTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.FactionFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.ClearFilterButton = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.TotalTradeListsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.AvgItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.FilteredCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TradeListsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.FilePathTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

