﻿<?xml version="1.0" encoding="UTF-8"?>
<item_armors>
	<armor_templates>
		<armor_template>
			<id>187000905</id>
			<name>custom_hz_wing_rank14_30day</name>
			<desc>CUSTOM_HZ_WING_RANK14_30DAY</desc>
			<armor_type>clothes</armor_type>
			<item_type>normal</item_type>
			<material>mat_flesh</material>
			<price>5</price>
			<max_stack_count>1</max_stack_count>
			<quality>epic</quality>
			<level>40</level>
			<lore>FALSE</lore>
			<can_exchange>FALSE</can_exchange>
			<can_sell_to_npc>FALSE</can_sell_to_npc>
			<can_deposit_to_character_warehouse>TRUE</can_deposit_to_character_warehouse>
			<can_deposit_to_account_warehouse>FALSE</can_deposit_to_account_warehouse>
			<can_deposit_to_guild_warehouse>FALSE</can_deposit_to_guild_warehouse>
			<item_drop_permitted>FALSE</item_drop_permitted>
			<breakable>TRUE</breakable>
			<soul_bind>FALSE</soul_bind>
			<remove_when_logout>FALSE</remove_when_logout>
			<race_permitted>pc_light pc_dark</race_permitted>
			<gender_permitted>all</gender_permitted>
			<warrior>1</warrior>
			<scout>1</scout>
			<mage>1</mage>
			<cleric>1</cleric>
			<engineer>1</engineer>
			<artist>1</artist>
			<fighter>1</fighter>
			<knight>1</knight>
			<assassin>1</assassin>
			<ranger>1</ranger>
			<wizard>1</wizard>
			<elementalist>1</elementalist>
			<chanter>1</chanter>
			<priest>1</priest>
			<gunner>1</gunner>
			<bard>1</bard>
			<rider>1</rider>
			<bonus_apply>equip</bonus_apply>
			<trade_in_item_list>
				<data>
					<trade_in_item>black_aion_toll_32</trade_in_item>
					<trade_in_item_count>10000</trade_in_item_count>
				</data>
			</trade_in_item_list>
		</armor_template>

	</armor_templates>
</item_armors>
