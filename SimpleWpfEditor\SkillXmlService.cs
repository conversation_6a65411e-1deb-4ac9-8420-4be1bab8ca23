using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 技能XML文件服务
    /// </summary>
    public class SkillXmlService
    {
        private readonly ILogger<SkillXmlService> _logger;

        public SkillXmlService(ILogger<SkillXmlService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 从XML文件加载技能数据
        /// </summary>
        public async Task<List<ClientSkill>> LoadSkillsFromXmlAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("开始加载技能文件: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"技能文件不存在: {filePath}");
                }

                var skills = new List<ClientSkill>();
                var xmlDoc = new XmlDocument();

                // 读取XML文件
                var xmlContent = await File.ReadAllTextAsync(filePath);
                xmlDoc.LoadXml(xmlContent);

                var skillNodes = xmlDoc.SelectNodes("//client_skill");
                if (skillNodes == null)
                {
                    _logger.LogWarning("未找到技能节点");
                    return skills;
                }

                foreach (XmlNode skillNode in skillNodes)
                {
                    try
                    {
                        var skill = ParseSkillFromXml(skillNode);
                        skills.Add(skill);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "解析技能时出错: {SkillNode}", skillNode?.OuterXml);
                    }
                }

                _logger.LogInformation("成功加载 {Count} 个技能", skills.Count);
                return skills;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载技能文件时出错: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 从XML节点解析技能数据
        /// </summary>
        private ClientSkill ParseSkillFromXml(XmlNode skillNode)
        {
            var skill = new ClientSkill
            {
                // 解析基础属性
                Id = GetIntValue(skillNode, "id"),
                Name = GetStringValue(skillNode, "name"),
                Desc = GetStringValue(skillNode, "desc"),
                SkillType = GetStringValue(skillNode, "skill_type"),
                Level = GetIntValue(skillNode, "level", 1),
                MaxLevel = GetIntValue(skillNode, "max_level", 1),

                // 解析消耗属性
                MpConsume = GetIntValue(skillNode, "mp_consume"),
                HpConsume = GetIntValue(skillNode, "hp_consume"),
                DpConsume = GetIntValue(skillNode, "dp_consume"),

                // 解析时间属性
                CastTime = GetIntValue(skillNode, "cast_time"),
                CoolTime = GetIntValue(skillNode, "cool_time"),
                Duration = GetIntValue(skillNode, "duration"),

                // 解析目标属性
                TargetType = GetStringValue(skillNode, "target_type"),
                TargetRange = GetStringValue(skillNode, "target_range"),
                TargetMaxCount = GetIntValue(skillNode, "target_max_count", 1),

                // 解析效果属性
                EffectType = GetStringValue(skillNode, "effect_type"),
                EffectValue = GetIntValue(skillNode, "effect_value"),

                // 解析显示属性
                Animation = GetStringValue(skillNode, "animation"),
                Icon = GetStringValue(skillNode, "icon")
            };

            return skill;
        }

        /// <summary>
        /// 获取XML节点的字符串值
        /// </summary>
        private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
        {
            var node = parentNode.SelectSingleNode(elementName);
            return node?.InnerText ?? defaultValue;
        }

        /// <summary>
        /// 获取XML节点的整数值
        /// </summary>
        private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
        {
            var node = parentNode.SelectSingleNode(elementName);
            if (node != null && int.TryParse(node.InnerText, out var value))
            {
                return value;
            }
            return defaultValue;
        }

        /// <summary>
        /// 保存技能数据到XML文件
        /// </summary>
        public async Task SaveSkillsToXmlAsync(string filePath, List<ClientSkill> skills)
        {
            try
            {
                _logger.LogInformation($"开始保存技能文件: {filePath}");

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var xmlDoc = new XmlDocument();
                xmlDoc.AppendChild(xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null));

                var rootElement = xmlDoc.CreateElement("client_skills");
                rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
                xmlDoc.AppendChild(rootElement);

                // 按ID排序后添加所有技能
                var sortedSkills = skills.OrderBy(skill => skill.Id).ToList();
                foreach (var skill in sortedSkills)
                {
                    var skillElement = CreateSkillXmlElement(xmlDoc, skill);
                    rootElement.AppendChild(skillElement);
                }

                xmlDoc.Save(filePath);
                _logger.LogInformation($"成功保存 {skills.Count} 个技能到文件: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存技能文件时出错: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// 创建技能XML元素
        /// </summary>
        private XmlElement CreateSkillXmlElement(XmlDocument xmlDoc, ClientSkill skill)
        {
            var skillElement = xmlDoc.CreateElement("client_skill");

            // 基础属性
            AppendElement(xmlDoc, skillElement, "id", skill.Id.ToString());
            AppendElement(xmlDoc, skillElement, "name", skill.Name);
            AppendElement(xmlDoc, skillElement, "desc", skill.Desc);
            AppendElement(xmlDoc, skillElement, "skill_type", skill.SkillType);
            AppendElement(xmlDoc, skillElement, "level", skill.Level.ToString());
            AppendElement(xmlDoc, skillElement, "max_level", skill.MaxLevel.ToString());

            // 消耗属性
            if (skill.MpConsume > 0)
                AppendElement(xmlDoc, skillElement, "mp_consume", skill.MpConsume.ToString());
            if (skill.HpConsume > 0)
                AppendElement(xmlDoc, skillElement, "hp_consume", skill.HpConsume.ToString());
            if (skill.DpConsume > 0)
                AppendElement(xmlDoc, skillElement, "dp_consume", skill.DpConsume.ToString());

            // 时间属性
            if (skill.CastTime > 0)
                AppendElement(xmlDoc, skillElement, "cast_time", skill.CastTime.ToString());
            if (skill.CoolTime > 0)
                AppendElement(xmlDoc, skillElement, "cool_time", skill.CoolTime.ToString());
            if (skill.Duration > 0)
                AppendElement(xmlDoc, skillElement, "duration", skill.Duration.ToString());

            // 目标属性
            if (!string.IsNullOrEmpty(skill.TargetType))
                AppendElement(xmlDoc, skillElement, "target_type", skill.TargetType);
            if (!string.IsNullOrEmpty(skill.TargetRange))
                AppendElement(xmlDoc, skillElement, "target_range", skill.TargetRange);
            if (skill.TargetMaxCount > 1)
                AppendElement(xmlDoc, skillElement, "target_max_count", skill.TargetMaxCount.ToString());

            // 效果属性
            if (!string.IsNullOrEmpty(skill.EffectType))
                AppendElement(xmlDoc, skillElement, "effect_type", skill.EffectType);
            if (skill.EffectValue != 0)
                AppendElement(xmlDoc, skillElement, "effect_value", skill.EffectValue.ToString());

            // 显示属性
            if (!string.IsNullOrEmpty(skill.Animation))
                AppendElement(xmlDoc, skillElement, "animation", skill.Animation);
            if (!string.IsNullOrEmpty(skill.Icon))
                AppendElement(xmlDoc, skillElement, "icon", skill.Icon);

            return skillElement;
        }

        /// <summary>
        /// 创建服务端技能XML元素
        /// </summary>
        private XmlElement CreateServerSkillXmlElement(XmlDocument xmlDoc, ClientSkill skill)
        {
            var skillElement = xmlDoc.CreateElement("skill");

            // 基础属性
            AppendElement(xmlDoc, skillElement, "id", skill.Id.ToString());
            AppendElement(xmlDoc, skillElement, "name", skill.Name);
            AppendElement(xmlDoc, skillElement, "desc", skill.Desc);
            AppendElement(xmlDoc, skillElement, "skill_type", skill.SkillType);
            AppendElement(xmlDoc, skillElement, "level", skill.Level.ToString());
            AppendElement(xmlDoc, skillElement, "max_level", skill.MaxLevel.ToString());

            // 消耗属性
            if (skill.MpConsume > 0)
                AppendElement(xmlDoc, skillElement, "mp_consume", skill.MpConsume.ToString());
            if (skill.HpConsume > 0)
                AppendElement(xmlDoc, skillElement, "hp_consume", skill.HpConsume.ToString());
            if (skill.DpConsume > 0)
                AppendElement(xmlDoc, skillElement, "dp_consume", skill.DpConsume.ToString());

            // 时间属性
            if (skill.CastTime > 0)
                AppendElement(xmlDoc, skillElement, "cast_time", skill.CastTime.ToString());
            if (skill.CoolTime > 0)
                AppendElement(xmlDoc, skillElement, "cool_time", skill.CoolTime.ToString());
            if (skill.Duration > 0)
                AppendElement(xmlDoc, skillElement, "duration", skill.Duration.ToString());

            // 目标属性
            if (!string.IsNullOrEmpty(skill.TargetType))
                AppendElement(xmlDoc, skillElement, "target_type", skill.TargetType);
            if (!string.IsNullOrEmpty(skill.TargetRange))
                AppendElement(xmlDoc, skillElement, "target_range", skill.TargetRange);
            if (skill.TargetMaxCount > 1)
                AppendElement(xmlDoc, skillElement, "target_max_count", skill.TargetMaxCount.ToString());

            // 效果属性
            if (!string.IsNullOrEmpty(skill.EffectType))
                AppendElement(xmlDoc, skillElement, "effect_type", skill.EffectType);
            if (skill.EffectValue != 0)
                AppendElement(xmlDoc, skillElement, "effect_value", skill.EffectValue.ToString());

            // 显示属性
            if (!string.IsNullOrEmpty(skill.Animation))
                AppendElement(xmlDoc, skillElement, "animation", skill.Animation);
            if (!string.IsNullOrEmpty(skill.Icon))
                AppendElement(xmlDoc, skillElement, "icon", skill.Icon);

            return skillElement;
        }

        /// <summary>
        /// 添加XML子元素
        /// </summary>
        private void AppendElement(XmlDocument xmlDoc, XmlElement parent, string elementName, string value)
        {
            var element = xmlDoc.CreateElement(elementName);
            element.InnerText = value;
            parent.AppendChild(element);
        }

        /// <summary>
        /// 保存技能数据并转换为服务端格式
        /// </summary>
        public async Task<bool> SaveWithServerConversionAsync(string clientFilePath, List<ClientSkill> skills, string serverBasePath)
        {
            try
            {
                _logger.LogInformation($"开始转换 {skills.Count} 个技能到服务端格式");

                // 确保服务端目录存在
                if (!Directory.Exists(serverBasePath))
                {
                    Directory.CreateDirectory(serverBasePath);
                }

                // 创建服务端XML文档
                var xmlDoc = new XmlDocument();
                xmlDoc.AppendChild(xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null));

                // 使用服务端根标签
                var rootElement = xmlDoc.CreateElement("skills");
                rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
                xmlDoc.AppendChild(rootElement);

                // 添加所有技能（按ID排序）
                var sortedSkills = skills.OrderBy(s => s.Id).ToList();
                foreach (var skill in sortedSkills)
                {
                    var skillElement = CreateServerSkillXmlElement(xmlDoc, skill);
                    rootElement.AppendChild(skillElement);
                }

                // 保存服务端文件
                var serverFilePath = Path.Combine(serverBasePath, "skills.xml");
                xmlDoc.Save(serverFilePath);

                _logger.LogInformation($"服务端技能文件已保存: {serverFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换技能到服务端格式时出错");
                return false;
            }
        }
    }
}
