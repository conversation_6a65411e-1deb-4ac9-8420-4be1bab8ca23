using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor
{
    /// <summary>
    /// NPC XML数据服务
    /// </summary>
    public class NpcXmlService
    {
        private readonly ILogger<NpcXmlService> _logger;

        public NpcXmlService(ILogger<NpcXmlService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 从XML文件加载NPC数据
        /// </summary>
        public async Task<List<NpcClient>> LoadNpcsFromXmlAsync(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始加载NPC文件: {filePath}");

                var npcs = new List<NpcClient>();
                var xmlDoc = new XmlDocument();
                xmlDoc.Load(filePath);

                var npcNodes = xmlDoc.SelectNodes("//npc");
                if (npcNodes == null)
                {
                    _logger.LogWarning("未找到NPC节点");
                    return npcs;
                }

                foreach (XmlNode npcNode in npcNodes)
                {
                    try
                    {
                        var npc = ParseNpcFromXml(npcNode);
                        npcs.Add(npc);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"解析NPC节点时出错: {npcNode.OuterXml}");
                    }
                }

                _logger.LogInformation($"成功加载 {npcs.Count} 个NPC");
                return npcs;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载NPC文件失败: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// 保存NPC数据到XML文件
        /// </summary>
        public async Task SaveNpcsToXmlAsync(string filePath, List<NpcClient> npcs)
        {
            try
            {
                _logger.LogInformation($"开始保存NPC文件: {filePath}");

                var xmlDoc = new XmlDocument();
                var declaration = xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null);
                xmlDoc.AppendChild(declaration);

                var rootElement = xmlDoc.CreateElement("npcs");
                xmlDoc.AppendChild(rootElement);

                foreach (var npc in npcs)
                {
                    var npcElement = CreateNpcElement(xmlDoc, npc);
                    rootElement.AppendChild(npcElement);
                }

                xmlDoc.Save(filePath);
                _logger.LogInformation($"成功保存 {npcs.Count} 个NPC到文件: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存NPC文件失败: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// 从XML节点解析NPC数据
        /// </summary>
        private NpcClient ParseNpcFromXml(XmlNode npcNode)
        {
            var npc = new NpcClient();

            // 基础信息
            npc.Id = GetIntValue(npcNode, "id");
            npc.Name = GetStringValue(npcNode, "name");
            npc.Desc = GetStringValue(npcNode, "desc");
            npc.SpawnZoneName = GetStringValue(npcNode, "__spawn_zonename__");
            npc.Dir = GetStringValue(npcNode, "dir");
            npc.Mesh = GetStringValue(npcNode, "mesh");
            npc.NpcTitle = GetStringValue(npcNode, "npc_title");
            npc.DiskType = GetStringValue(npcNode, "disk_type", "D3");

            // 外观属性
            npc.Scale = GetIntValue(npcNode, "scale", 100);
            npc.WeaponScale = GetIntValue(npcNode, "weapon_scale", 100);
            npc.Altitude = GetFloatValue(npcNode, "altitude");
            npc.NoCheckAnimation = GetIntValue(npcNode, "no_check_animation");
            npc.DmgTexture = GetIntValue(npcNode, "dmg_texture");
            npc.UiType = GetStringValue(npcNode, "ui_type");
            npc.HidePath = GetIntValue(npcNode, "hide_path");
            npc.HideMap = GetIntValue(npcNode, "hide_map");

            // 移动属性
            npc.MoveSpeedNormalWalk = GetFloatValue(npcNode, "move_speed_normal_walk", 1.38f);
            npc.MoveSpeedNormalRun = GetFloatValue(npcNode, "move_speed_normal_run", 6.0f);
            npc.MoveSpeedCombatRun = GetFloatValue(npcNode, "move_speed_combat_run", 7.0f);
            npc.AmmoSpeed = GetIntValue(npcNode, "ammo_speed");
            npc.PushedRange = GetFloatValue(npcNode, "pushed_range", 2.0f);
            npc.Dual = GetIntValue(npcNode, "dual");

            // 基础属性
            npc.Level = GetIntValue(npcNode, "level", 1);
            npc.Exp = GetIntValue(npcNode, "exp");
            npc.Dp = GetIntValue(npcNode, "dp", 100);
            npc.MaxHp = GetIntValue(npcNode, "max_hp", 100);
            npc.HpgaugeLevel = GetIntValue(npcNode, "hpgauge_level", 1);
            npc.HpRegen = GetIntValue(npcNode, "hp_regen");

            // 攻击属性
            npc.PhysicalAttack = GetIntValue(npcNode, "physical_attack");
            npc.PhysicalDefend = GetIntValue(npcNode, "physical_defend");
            npc.MagicalAttack = GetIntValue(npcNode, "magical_attack");
            npc.MagicalDefend = GetIntValue(npcNode, "magical_defend");
            npc.MagicalResist = GetIntValue(npcNode, "magical_resist");
            npc.MinDamage = GetIntValue(npcNode, "min_damage");
            npc.MaxDamage = GetIntValue(npcNode, "max_damage");
            npc.MagicalSkillBoost = GetIntValue(npcNode, "magical_skill_boost");
            npc.MagicalSkillBoostResist = GetIntValue(npcNode, "magical_skill_boost_resist");
            npc.HitCount = GetIntValue(npcNode, "hit_count", 1);
            npc.AttackDelay = GetIntValue(npcNode, "attack_delay", 2000);
            npc.HitAccuracy = GetIntValue(npcNode, "hit_accuracy");
            npc.MagicalHitAccuracy = GetIntValue(npcNode, "magical_hit_accuracy");

            // 暴击属性
            npc.Critical = GetIntValue(npcNode, "critical");
            npc.PhysicalCriticalReduceRate = GetIntValue(npcNode, "physical_critical_reduce_rate");
            npc.PhysicalCriticalDamageReduce = GetIntValue(npcNode, "physical_critical_damage_reduce");
            npc.MagicalCritical = GetIntValue(npcNode, "magical_critical");
            npc.MagicalCriticalReduceRate = GetIntValue(npcNode, "magical_critical_reduce_rate");
            npc.MagicalCriticalDamageReduce = GetIntValue(npcNode, "magical_critical_damage_reduce");

            // 防御属性
            npc.Concentration = GetIntValue(npcNode, "concentration");
            npc.Dodge = GetIntValue(npcNode, "dodge");
            npc.Parry = GetIntValue(npcNode, "parry");
            npc.Block = GetIntValue(npcNode, "block");
            npc.DpReduce = GetIntValue(npcNode, "dp_reduce");

            // 深渊属性
            npc.AbyssPoint = GetIntValue(npcNode, "abyss_point");
            npc.AbyssRank = GetIntValue(npcNode, "abyss_rank", 1);
            npc.CancelLevel = GetIntValue(npcNode, "cancel_level", 50);

            // AI和行为
            npc.GameLang = GetStringValue(npcNode, "game_lang");
            npc.AiName = GetStringValue(npcNode, "ai_name", "NPC");
            npc.IdleName = GetStringValue(npcNode, "idle_name", "NoMove");
            npc.Tribe = GetStringValue(npcNode, "tribe");
            npc.RaceType = GetStringValue(npcNode, "race_type");
            npc.PetAiName = GetStringValue(npcNode, "pet_ai_name", "Pet");

            // 感知和追击
            npc.MaxEnemyCount = GetIntValue(npcNode, "max_enemy_count", 10);
            npc.SensoryRange = GetFloatValue(npcNode, "sensory_range", 8.0f);
            npc.SensoryRangeShort = GetFloatValue(npcNode, "sensory_range_short", 4.0f);
            npc.SensoryAngle = GetIntValue(npcNode, "sensory_angle", 240);
            npc.MaxChaseTime = GetIntValue(npcNode, "max_chase_time", 8);
            npc.ChaseRangeFromsp = GetFloatValue(npcNode, "chase_range_fromsp");
            npc.ChaseFlying = GetIntValue(npcNode, "chase_flying");
            npc.ReactToPathfindFail = GetStringValue(npcNode, "react_to_pathfind_fail", "return_to_sp");

            // 移动行为
            npc.MoveSpeedFlee = GetIntValue(npcNode, "move_speed_flee", 70);
            npc.MoveTypeReturn = GetStringValue(npcNode, "move_type_return", "run");
            npc.MoveSpeedReturn = GetIntValue(npcNode, "move_speed_return", 200);
            npc.DecreaseSensoryRangeReturn = GetIntValue(npcNode, "decrease_sensory_range_return", 40);

            // 仇恨系统
            npc.HateDecreaseType = GetIntValue(npcNode, "hate_decrease_type");
            npc.HateDecreaseA = GetIntValue(npcNode, "hate_decrease_a", 364);
            npc.HateDecreaseB = GetIntValue(npcNode, "hate_decrease_b");

            // 特殊能力
            npc.CanSeeInvisible = GetIntValue(npcNode, "can_see_invisible");
            npc.CanSeePolymorph = GetStringValue(npcNode, "can_see_polymorph", "FALSE");
            npc.CanPassWall = GetIntValue(npcNode, "can_pass_wall");

            // NPC类型和状态
            npc.NpcType = GetStringValue(npcNode, "npc_type", "Monster");
            npc.Unattackable = GetIntValue(npcNode, "unattackable");
            npc.Verbosity = GetFloatValue(npcNode, "verbosity", 0.5f);

            // 解析复杂对象
            ParseBoundRadius(npcNode, npc);
            ParseVisibleEquipments(npcNode, npc);
            ParseFirstLevelAttr(npcNode, npc);
            ParseAbnormalResists(npcNode, npc);
            ParseAbnormalPenetrations(npcNode, npc);
            ParseAttacks(npcNode, npc);
            ParseSkills(npcNode, npc);
            ParseGossipList(npcNode, npc);

            return npc;
        }

        /// <summary>
        /// 创建NPC XML元素
        /// </summary>
        private XmlElement CreateNpcElement(XmlDocument xmlDoc, NpcClient npc)
        {
            var npcElement = xmlDoc.CreateElement("npc");

            // 基础信息
            AppendElement(xmlDoc, npcElement, "id", npc.Id.ToString());
            AppendElement(xmlDoc, npcElement, "name", npc.Name);
            AppendElement(xmlDoc, npcElement, "desc", npc.Desc);
            if (!string.IsNullOrEmpty(npc.SpawnZoneName))
                AppendElement(xmlDoc, npcElement, "__spawn_zonename__", npc.SpawnZoneName);
            AppendElement(xmlDoc, npcElement, "dir", npc.Dir);
            AppendElement(xmlDoc, npcElement, "mesh", npc.Mesh);
            if (!string.IsNullOrEmpty(npc.NpcTitle))
                AppendElement(xmlDoc, npcElement, "npc_title", npc.NpcTitle);
            AppendElement(xmlDoc, npcElement, "disk_type", npc.DiskType);

            // 外观属性
            if (npc.Scale != 100)
                AppendElement(xmlDoc, npcElement, "scale", npc.Scale.ToString());
            if (npc.WeaponScale != 100)
                AppendElement(xmlDoc, npcElement, "weapon_scale", npc.WeaponScale.ToString());
            if (npc.Altitude != 0.0f)
                AppendElement(xmlDoc, npcElement, "altitude", npc.Altitude.ToString("F6"));

            // 基础属性
            AppendElement(xmlDoc, npcElement, "level", npc.Level.ToString());
            if (npc.Exp != 0)
                AppendElement(xmlDoc, npcElement, "exp", npc.Exp.ToString());
            if (npc.Dp != 100)
                AppendElement(xmlDoc, npcElement, "dp", npc.Dp.ToString());
            AppendElement(xmlDoc, npcElement, "max_hp", npc.MaxHp.ToString());

            // AI和行为
            AppendElement(xmlDoc, npcElement, "ai_name", npc.AiName);
            AppendElement(xmlDoc, npcElement, "idle_name", npc.IdleName);
            if (!string.IsNullOrEmpty(npc.Tribe))
                AppendElement(xmlDoc, npcElement, "tribe", npc.Tribe);
            if (!string.IsNullOrEmpty(npc.RaceType))
                AppendElement(xmlDoc, npcElement, "race_type", npc.RaceType);

            // NPC类型
            AppendElement(xmlDoc, npcElement, "npc_type", npc.NpcType);

            return npcElement;
        }

        // 辅助方法
        private void AppendElement(XmlDocument xmlDoc, XmlElement parent, string name, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                var element = xmlDoc.CreateElement(name);
                element.InnerText = value;
                parent.AppendChild(element);
            }
        }

        private string GetStringValue(XmlNode node, string elementName, string defaultValue = "")
        {
            var element = node.SelectSingleNode(elementName);
            return element?.InnerText ?? defaultValue;
        }

        private int GetIntValue(XmlNode node, string elementName, int defaultValue = 0)
        {
            var element = node.SelectSingleNode(elementName);
            if (element != null && int.TryParse(element.InnerText, out int value))
                return value;
            return defaultValue;
        }

        private float GetFloatValue(XmlNode node, string elementName, float defaultValue = 0.0f)
        {
            var element = node.SelectSingleNode(elementName);
            if (element != null && float.TryParse(element.InnerText, out float value))
                return value;
            return defaultValue;
        }

        // 解析复杂对象的方法
        private void ParseBoundRadius(XmlNode npcNode, NpcClient npc)
        {
            var boundRadiusNode = npcNode.SelectSingleNode("bound_radius");
            if (boundRadiusNode != null)
            {
                npc.BoundRadius.Front = GetFloatValue(boundRadiusNode, "front", 2.0f);
                npc.BoundRadius.Side = GetFloatValue(boundRadiusNode, "side", 2.0f);
                npc.BoundRadius.Upper = GetFloatValue(boundRadiusNode, "upper", 2.0f);
            }
        }

        private void ParseVisibleEquipments(XmlNode npcNode, NpcClient npc)
        {
            var equipmentsNode = npcNode.SelectSingleNode("visible_equipments");
            if (equipmentsNode != null)
            {
                npc.VisibleEquipments.Main = GetStringValue(equipmentsNode, "main");
                npc.VisibleEquipments.Sub = GetStringValue(equipmentsNode, "sub");
                npc.VisibleEquipments.Head = GetStringValue(equipmentsNode, "head");
                npc.VisibleEquipments.Torso = GetStringValue(equipmentsNode, "torso");
                npc.VisibleEquipments.Leg = GetStringValue(equipmentsNode, "leg");
                npc.VisibleEquipments.Hand = GetStringValue(equipmentsNode, "hand");
                npc.VisibleEquipments.Foot = GetStringValue(equipmentsNode, "foot");
            }
        }

        private void ParseFirstLevelAttr(XmlNode npcNode, NpcClient npc)
        {
            var firstLevelAttrNode = npcNode.SelectSingleNode("first_level_attr");
            if (firstLevelAttrNode != null)
            {
                npc.FirstLevelAttr.ElementalDefendLight = GetIntValue(firstLevelAttrNode, "elemental_defend_light");
                npc.FirstLevelAttr.ElementalDefendDark = GetIntValue(firstLevelAttrNode, "elemental_defend_dark");
                npc.FirstLevelAttr.ElementalDefendEarth = GetIntValue(firstLevelAttrNode, "elemental_defend_earth");
                npc.FirstLevelAttr.ElementalDefendAir = GetIntValue(firstLevelAttrNode, "elemental_defend_air");
                npc.FirstLevelAttr.ElementalDefendWater = GetIntValue(firstLevelAttrNode, "elemental_defend_water");
                npc.FirstLevelAttr.ElementalDefendFire = GetIntValue(firstLevelAttrNode, "elemental_defend_fire");
            }
        }

        private void ParseAbnormalResists(XmlNode npcNode, NpcClient npc)
        {
            var abnormalResistsNode = npcNode.SelectSingleNode("abnormal_resists");
            if (abnormalResistsNode != null)
            {
                var dataNodes = abnormalResistsNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var resist = new AbnormalResist
                        {
                            AbnormalResistName = GetStringValue(dataNode, "abnormal_resist_name"),
                            AbnormalResistValue = GetIntValue(dataNode, "abnormal_resist_value")
                        };
                        npc.AbnormalResists.Add(resist);
                    }
                }
            }
        }

        private void ParseAbnormalPenetrations(XmlNode npcNode, NpcClient npc)
        {
            var abnormalPenetrationsNode = npcNode.SelectSingleNode("abnormal_penetrations");
            if (abnormalPenetrationsNode != null)
            {
                var dataNodes = abnormalPenetrationsNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var penetration = new AbnormalPenetration
                        {
                            AbnormalPenetrationValue = GetIntValue(dataNode, "abnormal_penetration_value")
                        };
                        npc.AbnormalPenetrations.Add(penetration);
                    }
                }
            }
        }

        private void ParseAttacks(XmlNode npcNode, NpcClient npc)
        {
            var attacksNode = npcNode.SelectSingleNode("attacks");
            if (attacksNode != null)
            {
                var dataNodes = attacksNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var attack = new NpcAttack
                        {
                            AttackMod = GetIntValue(dataNode, "attack_mod", 100),
                            AttackRange = GetFloatValue(dataNode, "attack_range", 2.0f),
                            AttackRate = GetIntValue(dataNode, "attack_rate", 1000),
                            AttackType = GetStringValue(dataNode, "attack_type", "physical")
                        };
                        npc.Attacks.Add(attack);
                    }
                }
            }
        }

        private void ParseSkills(XmlNode npcNode, NpcClient npc)
        {
            var skillsNode = npcNode.SelectSingleNode("skills");
            if (skillsNode != null)
            {
                var dataNodes = skillsNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var skill = new NpcSkill
                        {
                            SkillName = GetStringValue(dataNode, "skill_name"),
                            SkillLevel = GetIntValue(dataNode, "skill_level", 1),
                            SkillRate = GetIntValue(dataNode, "skill_rate")
                        };
                        npc.Skills.Add(skill);
                    }
                }
            }
        }

        private void ParseGossipList(XmlNode npcNode, NpcClient npc)
        {
            var gossipListNode = npcNode.SelectSingleNode("gossip_list");
            if (gossipListNode != null)
            {
                var dataNodes = gossipListNode.SelectNodes("data");
                if (dataNodes != null)
                {
                    foreach (XmlNode dataNode in dataNodes)
                    {
                        var gossip = new NpcGossip
                        {
                            Gossip = GetStringValue(dataNode, "gossip")
                        };
                        npc.GossipList.Add(gossip);
                    }
                }
            }
        }
    }
}
