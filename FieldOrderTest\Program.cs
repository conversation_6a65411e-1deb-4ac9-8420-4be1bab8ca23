using System.Xml;

Console.WriteLine("=================================");
Console.WriteLine("   XML字段顺序修正验证测试");
Console.WriteLine("=================================");
Console.WriteLine();

try
{
    Console.WriteLine("✓ 测试修正后的XML字段顺序:");
    Console.WriteLine();
    
    // 创建简化的测试数据
    var testData = new
    {
        Id = 100000001,
        Name = "sword_circulous",
        Desc = "STR_SWORD_CIRCULOUS",
        WeaponType = "1h_sword",
        ItemType = "normal",
        Mesh = "Sword_circulous",
        Material = "mat_sword_s",
        IconName = "Icon_Item_Sword_C01",
        EquipBone = "Rhand_bone",
        Price = 5,
        ExtraCurrencyItem = "coin_combineskill_01",
        ExtraCurrencyItemCount = 2,
        TradeInItems = new[] { new { Item = "black_aion_toll_32", Count = 10000 } },
        MaxStackCount = 1,
        EquipmentSlots = "main_or_sub",
        Quality = "unique",
        Level = 1,
        RacePermitted = "pc_light pc_dark"
    };
    
    Console.WriteLine($"创建测试物品: {testData.Name} (ID: {testData.Id})");
    Console.WriteLine();

    // 生成XML
    var xmlDoc = new XmlDocument();
    var itemElement = CreateItemElement(xmlDoc, testData);
    
    // 创建根元素
    var rootElement = xmlDoc.CreateElement("client_items");
    xmlDoc.AppendChild(rootElement);
    rootElement.AppendChild(itemElement);
    
    // 保存XML
    var settings = new XmlWriterSettings
    {
        Indent = true,
        IndentChars = "    ",
        Encoding = System.Text.Encoding.UTF8
    };
    
    var outputPath = "field_order_test.xml";
    using var writer = XmlWriter.Create(outputPath, settings);
    xmlDoc.Save(writer);
    
    Console.WriteLine($"✅ XML文件已生成: {outputPath}");
    Console.WriteLine();
    
    // 读取并显示XML内容
    var xmlContent = await File.ReadAllTextAsync(outputPath);
    var lines = xmlContent.Split('\n');
    
    Console.WriteLine("✓ 验证字段顺序:");
    Console.WriteLine();
    
    // 查找关键字段的位置
    int priceLineIndex = -1;
    int extraCurrencyLineIndex = -1;
    int tradeInListLineIndex = -1;
    int maxStackLineIndex = -1;
    
    for (int i = 0; i < lines.Length; i++)
    {
        var line = lines[i].Trim();
        if (line.Contains("<price>")) priceLineIndex = i;
        if (line.Contains("<extra_currency_item>")) extraCurrencyLineIndex = i;
        if (line.Contains("<trade_in_item_list>")) tradeInListLineIndex = i;
        if (line.Contains("<max_stack_count>")) maxStackLineIndex = i;
    }
    
    Console.WriteLine($"字段位置验证:");
    Console.WriteLine($"  price 位置: 第 {priceLineIndex + 1} 行");
    Console.WriteLine($"  extra_currency_item 位置: 第 {extraCurrencyLineIndex + 1} 行");
    Console.WriteLine($"  trade_in_item_list 位置: 第 {tradeInListLineIndex + 1} 行");
    Console.WriteLine($"  max_stack_count 位置: 第 {maxStackLineIndex + 1} 行");
    Console.WriteLine();
    
    // 验证顺序是否正确
    bool orderCorrect = priceLineIndex < extraCurrencyLineIndex && 
                       extraCurrencyLineIndex < tradeInListLineIndex && 
                       tradeInListLineIndex < maxStackLineIndex;
    
    if (orderCorrect)
    {
        Console.WriteLine("✅ 字段顺序正确！");
        Console.WriteLine("   price → extra_currency_item → trade_in_item_list → max_stack_count");
    }
    else
    {
        Console.WriteLine("❌ 字段顺序不正确！");
    }
    
    Console.WriteLine();
    Console.WriteLine("📋 生成的XML片段预览:");
    
    // 显示price周围的内容
    for (int i = Math.Max(0, priceLineIndex - 2); i <= Math.Min(lines.Length - 1, maxStackLineIndex + 2); i++)
    {
        var line = lines[i].Trim();
        if (!string.IsNullOrEmpty(line))
        {
            if (line.Contains("<price>") || line.Contains("<extra_currency_item>") || 
                line.Contains("<trade_in_item_list>") || line.Contains("<max_stack_count>"))
            {
                Console.WriteLine($"  → {line}");
            }
            else
            {
                Console.WriteLine($"    {line}");
            }
        }
    }
    
    Console.WriteLine();
    Console.WriteLine("=================================");
    Console.WriteLine("✓ XML字段顺序修正验证完成！");
    Console.WriteLine("=================================");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 测试过程中出现错误: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

Console.WriteLine();
Console.WriteLine("按任意键退出...");
Console.ReadKey();

// 简化的XML元素创建方法
static XmlElement CreateItemElement(XmlDocument xmlDoc, dynamic item)
{
    var itemElement = xmlDoc.CreateElement("client_item");

    // 按照正确的顺序添加字段
    AddElement(xmlDoc, itemElement, "id", item.Id.ToString());
    AddElement(xmlDoc, itemElement, "name", item.Name);
    AddElement(xmlDoc, itemElement, "desc", item.Desc);
    AddElement(xmlDoc, itemElement, "weapon_type", item.WeaponType);
    AddElement(xmlDoc, itemElement, "item_type", item.ItemType);
    AddElement(xmlDoc, itemElement, "mesh", item.Mesh);
    AddElement(xmlDoc, itemElement, "material", item.Material);
    AddElement(xmlDoc, itemElement, "icon_name", item.IconName);
    AddElement(xmlDoc, itemElement, "equip_bone", item.EquipBone);

    // 价格 - 关键位置
    AddElement(xmlDoc, itemElement, "price", item.Price.ToString());

    // 交易相关字段 - 紧跟在price之后
    if (!string.IsNullOrEmpty(item.ExtraCurrencyItem))
    {
        AddElement(xmlDoc, itemElement, "extra_currency_item", item.ExtraCurrencyItem);
        if (item.ExtraCurrencyItemCount > 0)
            AddElement(xmlDoc, itemElement, "extra_currency_item_count", item.ExtraCurrencyItemCount.ToString());
    }

    // 兑换物品列表 - 在price之后
    if (item.TradeInItems != null && item.TradeInItems.Length > 0)
    {
        var tradeInListElement = xmlDoc.CreateElement("trade_in_item_list");
        foreach (var tradeInItem in item.TradeInItems)
        {
            var dataElement = xmlDoc.CreateElement("data");
            AddElement(xmlDoc, dataElement, "trade_in_item", tradeInItem.Item);
            AddElement(xmlDoc, dataElement, "trade_in_item_count", tradeInItem.Count.ToString());
            tradeInListElement.AppendChild(dataElement);
        }
        itemElement.AppendChild(tradeInListElement);
    }

    // 继续其他字段
    AddElement(xmlDoc, itemElement, "max_stack_count", item.MaxStackCount.ToString());
    AddElement(xmlDoc, itemElement, "equipment_slots", item.EquipmentSlots);
    AddElement(xmlDoc, itemElement, "quality", item.Quality);
    AddElement(xmlDoc, itemElement, "level", item.Level.ToString());
    AddElement(xmlDoc, itemElement, "race_permitted", item.RacePermitted);

    return itemElement;
}

static void AddElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
{
    if (!string.IsNullOrEmpty(value))
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value;
        parentElement.AppendChild(element);
    }
}
