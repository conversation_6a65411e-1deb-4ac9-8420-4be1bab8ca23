using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 服务端怪物掉落管理器窗口
    /// </summary>
    public partial class ServerMonsterDropManagerWindow : Window
    {
        private ServerMonsterXmlService _xmlService = null!;
        private ObservableCollection<ServerMonster> _monsters = null!;
        private CollectionViewSource _filteredMonsters = null!;
        private ServerMonster? _currentEditingMonster;
        private bool _isUpdating = false;

        public ServerMonsterDropManagerWindow()
        {
            try
            {
                InitializeComponent();
                
                // 初始化服务
                var loggerFactory = LoggerFactory.Create(builder => { });
                var logger = loggerFactory.CreateLogger<ServerMonsterXmlService>();
                _xmlService = new ServerMonsterXmlService(logger);
                
                // 初始化集合
                _monsters = new ObservableCollection<ServerMonster>();
                _filteredMonsters = new CollectionViewSource { Source = _monsters };
                
                // 绑定数据源
                if (MonsterListBox != null)
                {
                    MonsterListBox.ItemsSource = _filteredMonsters.View;
                }
                
                // 初始化搜索框
                if (SearchTextBox != null)
                {
                    SearchTextBox.Text = "搜索怪物...";
                    SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
                }
                
                UpdateCountDisplay();

                // 添加示例怪物数据
                AddSampleMonsterData();

                // 异步加载怪物数据
                _ = InitializeAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化服务端怪物管理器时出错: {ex.Message}\n\n详细信息: {ex.StackTrace}", 
                    "初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 异步初始化，自动加载怪物数据
        /// </summary>
        private async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                // 查找服务端怪物数据文件
                var monsterFiles = new[]
                {
                    @"data\static_data\npcs\npcs_monsters.xml",
                    @"data\static_data\npcs\npcs_std_monsters.xml",
                    @"data\npcs\npcs_monsters.xml",
                    @"data\npcs\npcs_std_monsters.xml",
                    @"npcs\npcs_monsters.xml",
                    @"npcs\npcs_std_monsters.xml",
                    @"npcs_monsters.xml",
                    @"npcs_std_monsters.xml"
                };

                string? foundFile = null;
                foreach (var file in monsterFiles)
                {
                    if (System.IO.File.Exists(file))
                    {
                        foundFile = file;
                        break;
                    }
                }

                if (foundFile != null)
                {
                    StatusTextBlock.Text = "正在自动加载服务端怪物数据...";
                    
                    var monsters = await _xmlService.LoadMonstersFromXmlAsync(foundFile);
                    
                    _monsters.Clear();
                    foreach (var monster in monsters)
                    {
                        _monsters.Add(monster);
                    }
                    
                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已自动加载 {monsters.Count} 个服务端怪物 (来源: {foundFile})";
                }
                else
                {
                    StatusTextBlock.Text = "未找到服务端怪物数据文件，请手动加载";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"自动加载服务端怪物数据失败: {ex.Message}";
            }
        }

        #region 事件处理

        private async void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    Title = "选择服务端怪物数据文件"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    StatusTextBlock.Text = "正在加载文件...";
                    
                    var monsters = await _xmlService.LoadMonstersFromXmlAsync(openFileDialog.FileName);
                    
                    _monsters.Clear();
                    foreach (var monster in monsters)
                    {
                        _monsters.Add(monster);
                    }
                    
                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已加载 {monsters.Count} 个服务端怪物";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "加载失败";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_monsters.Count == 0)
                {
                    MessageBox.Show("没有数据可保存", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 保存当前编辑的怪物
                SaveCurrentMonster();

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    Title = "保存服务端怪物数据文件"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    StatusTextBlock.Text = "正在保存文件...";
                    
                    await _xmlService.SaveMonstersToXmlAsync(saveFileDialog.FileName, _monsters.ToList());
                    
                    StatusTextBlock.Text = $"已保存 {_monsters.Count} 个服务端怪物";
                    MessageBox.Show("保存成功!", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "保存失败";
            }
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            var newMonster = new ServerMonster
            {
                Id = GetNextAvailableId(),
                Name = "新怪物",
                Desc = "新怪物描述",
                Level = 1,
                MaxHp = 100,
                DiskType = "monster",
                AiName = "Monster",
                IdleName = "NoMove"
            };

            _monsters.Add(newMonster);
            MonsterListBox.SelectedItem = newMonster;
            UpdateCountDisplay();
            StatusTextBlock.Text = $"已添加新怪物 ID: {newMonster.Id}";
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (MonsterListBox.SelectedItem is ServerMonster selectedMonster)
            {
                var result = MessageBox.Show($"确定要删除怪物 '{selectedMonster.Name}' (ID: {selectedMonster.Id}) 吗？", 
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _monsters.Remove(selectedMonster);
                    UpdateCountDisplay();
                    StatusTextBlock.Text = $"已删除怪物: {selectedMonster.Name}";
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的怪物", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CopyButton_Click(object sender, RoutedEventArgs e)
        {
            if (MonsterListBox.SelectedItem is ServerMonster selectedMonster)
            {
                var copiedMonster = new ServerMonster
                {
                    Id = GetNextAvailableId(),
                    Name = selectedMonster.Name + "_Copy",
                    Desc = selectedMonster.Desc,
                    Level = selectedMonster.Level,
                    MaxHp = selectedMonster.MaxHp,
                    DiskType = selectedMonster.DiskType,
                    AiName = selectedMonster.AiName,
                    IdleName = selectedMonster.IdleName,
                    Tribe = selectedMonster.Tribe,
                    SpawnZoneName = selectedMonster.SpawnZoneName,
                    // 复制掉落信息
                    ItemsInfo = new MonsterItemsInfo
                    {
                        MinCashAmount = selectedMonster.ItemsInfo.MinCashAmount,
                        MaxCashAmount = selectedMonster.ItemsInfo.MaxCashAmount,
                        CashDropProb = selectedMonster.ItemsInfo.CashDropProb,
                        DropGroups = new List<MonsterDropGroup>(selectedMonster.ItemsInfo.DropGroups),
                        Items = new List<MonsterDropItem>(selectedMonster.ItemsInfo.Items),
                        CommonDrops = new List<MonsterCommonDrop>(selectedMonster.ItemsInfo.CommonDrops)
                    }
                };

                _monsters.Add(copiedMonster);
                MonsterListBox.SelectedItem = copiedMonster;
                UpdateCountDisplay();
                StatusTextBlock.Text = $"已复制怪物: {copiedMonster.Name}";
            }
            else
            {
                MessageBox.Show("请先选择要复制的怪物", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void MonsterListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (MonsterListBox.SelectedItem is ServerMonster selectedMonster)
            {
                // 保存之前的怪物
                SaveCurrentMonster();
                
                // 加载新选择的怪物
                LoadMonsterToEditor(selectedMonster);
                _currentEditingMonster = selectedMonster;
                DetailsPanel.IsEnabled = true;
            }
            else
            {
                DetailsPanel.IsEnabled = false;
                _currentEditingMonster = null;
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "搜索怪物...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "搜索怪物...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterMonsters();
        }

        private void SaveMonsterButton_Click(object sender, RoutedEventArgs e)
        {
            SaveCurrentMonster();
            StatusTextBlock.Text = "怪物数据已保存";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingMonster != null)
            {
                LoadMonsterToEditor(_currentEditingMonster);
                StatusTextBlock.Text = "已取消修改";
            }
        }

        private void PreviewDropButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingMonster != null)
            {
                ShowDropPreview(_currentEditingMonster);
            }
        }

        #endregion

        #region 掉落组和物品管理

        private void AddDropGroupButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingMonster != null)
            {
                var newDropGroup = new MonsterDropGroup
                {
                    DropGroupMin = 0,
                    DropGroupMax = 1
                };
                _currentEditingMonster.ItemsInfo.DropGroups.Add(newDropGroup);
                RefreshDropGroupsDataGrid();
                StatusTextBlock.Text = "已添加新掉落组";
            }
        }

        private void DeleteDropGroupButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MonsterDropGroup dropGroup && _currentEditingMonster != null)
            {
                _currentEditingMonster.ItemsInfo.DropGroups.Remove(dropGroup);
                RefreshDropGroupsDataGrid();
                StatusTextBlock.Text = "已删除掉落组";
            }
        }

        private void AddDropItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingMonster != null)
            {
                var newDropItem = new MonsterDropItem
                {
                    Item = "new_item",
                    ItemId = 0,
                    Prob = 100000,
                    ItemCount = 1,
                    MinCount = 1,
                    MaxCount = 1,
                    DropAttribute = "common",
                    DropGroupIdx = 0
                };
                _currentEditingMonster.ItemsInfo.Items.Add(newDropItem);
                RefreshDropItemsDataGrid();
                StatusTextBlock.Text = "已添加新掉落物品";
            }
        }

        private void DeleteDropItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MonsterDropItem dropItem && _currentEditingMonster != null)
            {
                _currentEditingMonster.ItemsInfo.Items.Remove(dropItem);
                RefreshDropItemsDataGrid();
                StatusTextBlock.Text = "已删除掉落物品";
            }
        }

        private void AddCommonDropButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentEditingMonster != null)
            {
                var newCommonDrop = new MonsterCommonDrop
                {
                    CommonDrop = "NEW_COMMON_DROP",
                    CommonDropAdjustment = 1,
                    CommonDropGroupIdx = 0
                };
                _currentEditingMonster.ItemsInfo.CommonDrops.Add(newCommonDrop);
                RefreshCommonDropsDataGrid();
                StatusTextBlock.Text = "已添加新通用掉落";
            }
        }

        private void DeleteCommonDropButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MonsterCommonDrop commonDrop && _currentEditingMonster != null)
            {
                _currentEditingMonster.ItemsInfo.CommonDrops.Remove(commonDrop);
                RefreshCommonDropsDataGrid();
                StatusTextBlock.Text = "已删除通用掉落";
            }
        }

        #endregion

        #region 私有方法

        private int GetNextAvailableId()
        {
            if (_monsters.Count == 0) return 1;
            return _monsters.Max(m => m.Id) + 1;
        }

        private void UpdateCountDisplay()
        {
            if (CountTextBlock != null && _monsters != null)
            {
                CountTextBlock.Text = $"怪物数量: {_monsters.Count}";
            }
        }

        private void FilterMonsters()
        {
            if (SearchTextBox == null || _filteredMonsters?.View == null) return;

            var searchText = SearchTextBox.Text;
            if (searchText == "搜索怪物..." || string.IsNullOrWhiteSpace(searchText))
            {
                _filteredMonsters.View.Filter = null;
            }
            else
            {
                _filteredMonsters.View.Filter = obj =>
                {
                    if (obj is ServerMonster monster)
                    {
                        return (monster.Name?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               monster.Id.ToString().Contains(searchText) ||
                               (monster.Desc?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               (monster.DiskType?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               (monster.Tribe?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                               (monster.SpawnZoneName?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false);
                    }
                    return false;
                };
            }
        }

        private void LoadMonsterToEditor(ServerMonster monster)
        {
            _isUpdating = true;

            try
            {
                // 基础信息
                IdTextBox.Text = monster.Id.ToString();
                NameTextBox.Text = monster.Name ?? "";
                DescTextBox.Text = monster.Desc ?? "";
                LevelTextBox.Text = monster.Level.ToString();
                MaxHpTextBox.Text = monster.MaxHp.ToString();
                TribeTextBox.Text = monster.Tribe ?? "";
                SpawnZoneTextBox.Text = monster.SpawnZoneName ?? "";

                // 确保ItemsInfo不为null
                if (monster.ItemsInfo == null)
                {
                    monster.ItemsInfo = new MonsterItemsInfo();
                }

                // 金币掉落
                MinCashTextBox.Text = monster.ItemsInfo.MinCashAmount.ToString();
                MaxCashTextBox.Text = monster.ItemsInfo.MaxCashAmount.ToString();
                CashDropProbTextBox.Text = monster.ItemsInfo.CashDropProb.ToString();

                // 确保掉落集合不为null
                if (monster.ItemsInfo.DropGroups == null)
                {
                    monster.ItemsInfo.DropGroups = new List<MonsterDropGroup>();
                }
                if (monster.ItemsInfo.Items == null)
                {
                    monster.ItemsInfo.Items = new List<MonsterDropItem>();
                }
                if (monster.ItemsInfo.CommonDrops == null)
                {
                    monster.ItemsInfo.CommonDrops = new List<MonsterCommonDrop>();
                }

                // 调试信息
                var debugInfo = $"加载怪物: {monster.Name} (ID: {monster.Id}) - " +
                               $"掉落组: {monster.ItemsInfo.DropGroups.Count}, " +
                               $"掉落物品: {monster.ItemsInfo.Items.Count}, " +
                               $"金币: {monster.ItemsInfo.MinCashAmount}-{monster.ItemsInfo.MaxCashAmount}";
                StatusTextBlock.Text = debugInfo;

                // 刷新掉落组和物品数据网格
                RefreshDropGroupsDataGrid();
                RefreshDropItemsDataGrid();
                RefreshCommonDropsDataGrid();
            }
            finally
            {
                _isUpdating = false;
            }
        }

        private void SaveCurrentMonster()
        {
            if (_currentEditingMonster == null || _isUpdating) return;

            try
            {
                // 基础信息
                if (int.TryParse(IdTextBox.Text, out int id)) _currentEditingMonster.Id = id;
                _currentEditingMonster.Name = NameTextBox.Text ?? "";
                _currentEditingMonster.Desc = DescTextBox.Text ?? "";
                if (int.TryParse(LevelTextBox.Text, out int level)) _currentEditingMonster.Level = level;
                if (int.TryParse(MaxHpTextBox.Text, out int maxHp)) _currentEditingMonster.MaxHp = maxHp;
                _currentEditingMonster.Tribe = TribeTextBox.Text ?? "";
                _currentEditingMonster.SpawnZoneName = SpawnZoneTextBox.Text ?? "";

                // 金币掉落
                if (int.TryParse(MinCashTextBox.Text, out int minCash)) _currentEditingMonster.ItemsInfo.MinCashAmount = minCash;
                if (int.TryParse(MaxCashTextBox.Text, out int maxCash)) _currentEditingMonster.ItemsInfo.MaxCashAmount = maxCash;
                if (int.TryParse(CashDropProbTextBox.Text, out int cashProb)) _currentEditingMonster.ItemsInfo.CashDropProb = cashProb;

                // 刷新列表显示
                _filteredMonsters.View.Refresh();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"保存怪物数据时出错: {ex.Message}";
            }
        }

        private void RefreshDropGroupsDataGrid()
        {
            if (_currentEditingMonster != null && DropGroupsDataGrid != null)
            {
                DropGroupsDataGrid.ItemsSource = null;
                DropGroupsDataGrid.ItemsSource = _currentEditingMonster.ItemsInfo.DropGroups;
                DropGroupsDataGrid.Items.Refresh();

                // 调试信息
                StatusTextBlock.Text = $"掉落组数量: {_currentEditingMonster.ItemsInfo.DropGroups.Count}";
            }
        }

        private void RefreshDropItemsDataGrid()
        {
            if (_currentEditingMonster != null && DropItemsDataGrid != null)
            {
                DropItemsDataGrid.ItemsSource = null;
                DropItemsDataGrid.ItemsSource = _currentEditingMonster.ItemsInfo.Items;
                DropItemsDataGrid.Items.Refresh();

                // 调试信息
                var itemCount = _currentEditingMonster.ItemsInfo.Items.Count;
                var itemsWithId = _currentEditingMonster.ItemsInfo.Items.Count(i => i.ItemId > 0);
                StatusTextBlock.Text = $"掉落物品数量: {itemCount} (有效物品: {itemsWithId})";
            }
        }

        private void RefreshCommonDropsDataGrid()
        {
            if (_currentEditingMonster != null && CommonDropsDataGrid != null)
            {
                CommonDropsDataGrid.ItemsSource = null;
                CommonDropsDataGrid.ItemsSource = _currentEditingMonster.ItemsInfo.CommonDrops;
                CommonDropsDataGrid.Items.Refresh();

                // 调试信息
                var commonDropCount = _currentEditingMonster.ItemsInfo.CommonDrops.Count;
                var validCommonDrops = _currentEditingMonster.ItemsInfo.CommonDrops.Count(c => !string.IsNullOrEmpty(c.CommonDrop));
                StatusTextBlock.Text = $"通用掉落数量: {commonDropCount} (有效通用掉落: {validCommonDrops})";
            }
        }

        private void ShowDropPreview(ServerMonster monster)
        {
            try
            {
                var preview = GenerateDropPreview(monster);
                var previewWindow = new Window
                {
                    Title = $"掉落预览 - {monster.Name}",
                    Width = 600,
                    Height = 400,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = this
                };

                var scrollViewer = new ScrollViewer
                {
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Padding = new Thickness(16)
                };

                var textBlock = new TextBlock
                {
                    Text = preview,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 12,
                    TextWrapping = TextWrapping.Wrap
                };

                scrollViewer.Content = textBlock;
                previewWindow.Content = scrollViewer;
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成掉落预览时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateDropPreview(ServerMonster monster)
        {
            var preview = new System.Text.StringBuilder();

            preview.AppendLine($"怪物: {monster.Name} (ID: {monster.Id})");
            preview.AppendLine($"等级: {monster.Level}, HP: {monster.MaxHp}");
            preview.AppendLine($"种族: {monster.Tribe}, 区域: {monster.SpawnZoneName}");
            preview.AppendLine();

            // 金币掉落
            preview.AppendLine("=== 金币掉落 ===");
            if (monster.ItemsInfo.CashDropProb > 0)
            {
                preview.AppendLine($"掉落概率: {monster.ItemsInfo.CashDropProb}%");
                preview.AppendLine($"掉落数量: {monster.ItemsInfo.MinCashAmount} - {monster.ItemsInfo.MaxCashAmount}");
            }
            else
            {
                preview.AppendLine("无金币掉落");
            }
            preview.AppendLine();

            // 掉落组
            preview.AppendLine("=== 掉落组设置 ===");
            if (monster.ItemsInfo.DropGroups.Count > 0)
            {
                for (int i = 0; i < monster.ItemsInfo.DropGroups.Count; i++)
                {
                    var group = monster.ItemsInfo.DropGroups[i];
                    preview.AppendLine($"掉落组 {i + 1}: {group.DropGroupMin} - {group.DropGroupMax} 个物品");
                }
            }
            else
            {
                preview.AppendLine("无掉落组设置");
            }
            preview.AppendLine();

            // 掉落物品
            preview.AppendLine("=== 掉落物品 ===");
            if (monster.ItemsInfo.Items.Count > 0)
            {
                foreach (var item in monster.ItemsInfo.Items)
                {
                    if (item.ItemId > 0)
                    {
                        preview.AppendLine($"物品ID: {item.ItemId}, 概率: {item.Prob}%, 数量: {item.MinCount}-{item.MaxCount}");
                    }
                    else
                    {
                        preview.AppendLine($"占位符物品, 概率: {item.Prob}%");
                    }
                }
            }
            else
            {
                preview.AppendLine("无掉落物品");
            }

            // 通用掉落
            if (monster.ItemsInfo.CommonDrops.Count > 0)
            {
                preview.AppendLine();
                preview.AppendLine("=== 通用掉落 ===");
                foreach (var commonDrop in monster.ItemsInfo.CommonDrops)
                {
                    preview.AppendLine($"通用掉落调整: {commonDrop.CommonDropAdjustment}");
                }
            }

            return preview.ToString();
        }

        #endregion

        #region 示例数据

        /// <summary>
        /// 添加示例怪物数据
        /// </summary>
        private void AddSampleMonsterData()
        {
            try
            {
                // 创建完整的怪物数据 - LDF4A_A2_Sheluk_Worm_56_An
                var sampleMonster = CreateShelukWormMonster();
                _monsters.Add(sampleMonster);

                StatusTextBlock.Text = "已添加示例怪物数据: LDF4A_A2_Sheluk_Worm_56_An";
                UpdateCountDisplay();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"添加示例数据时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 创建完整的Sheluk Worm怪物数据
        /// </summary>
        private ServerMonster CreateShelukWormMonster()
        {
            var monster = new ServerMonster
            {
                Id = 217906,
                Name = "LDF4A_A2_Sheluk_Worm_56_An",
                Desc = "STR_LDF4A_A2_Sheluk_Worm_56_An",
                SpawnZoneName = "LDF4A",
                DiskType = "D2",
                Scale = 130,
                WeaponScale = 130,
                MoveSpeedNormalWalk = 0.614f,
                MoveSpeedNormalRun = 7.0f,
                MoveSpeedCombatRun = 5.5f,
                PushedRange = 2.0f,
                Level = 56,
                Exp = 56467,
                Dp = 50,
                MaxHp = 18602,
                HpgaugeLevel = 3,
                HpRegen = 8836,
                PhysicalAttack = 437,
                PhysicalDefend = 812,
                MagicalAttack = 217,
                MagicalDefend = 0,
                MagicalResist = 1107,
                MinDamage = 385,
                MaxDamage = 577,
                MagicalSkillBoost = 0,
                MagicalSkillBoostResist = 0,
                HitCount = 1,
                AttackDelay = 2570,
                HitAccuracy = 1532,
                MagicalHitAccuracy = 1088,
                Critical = 5,
                PhysicalCriticalReduceRate = 44,
                PhysicalCriticalDamageReduce = 0,
                MagicalCritical = 5,
                MagicalCriticalReduceRate = 13,
                MagicalCriticalDamageReduce = 0,
                Concentration = 0,
                Dodge = 0,
                Parry = 0,
                Block = 1010,
                DpReduce = 0,
                AbyssPoint = 0,
                AbyssRank = 1,
                CancelLevel = 100,
                AiName = "LDF4a_ForestSpider_Fighter",
                QuestAiName = "LDF4A_A2_Sheluk_Worm_56_An",
                IdleName = "RandomMove",
                Tribe = "AggressiveSingleMonster",
                RaceType = "Beast",
                PetAiName = "Pet",
                MaxEnemyCount = 10,
                SensoryRange = 5.0f,
                SensoryRangeShort = 5.0f,
                SensoryAngle = 240,
                MaxChaseTime = 8,
                ChaseFlying = 0,
                ReactToPathfindFail = "return_to_sp",
                MoveSpeedFlee = 70,
                MoveTypeReturn = "run",
                MoveSpeedReturn = 200,
                DecreaseSensoryRangeReturn = 40,
                HateDecreaseA = 364,
                CanSeeInvisible = 0,
                CanSeePolymorph = "FALSE"
            };

            // 设置边界半径
            monster.BoundRadius = new MonsterBoundRadius
            {
                Front = 2.5f,
                Side = 3.5f,
                Upper = 1.961f
            };

            // 设置第一级属性
            monster.FirstLevelAttr = new MonsterFirstLevelAttr
            {
                ElementalDefendLight = 0,
                ElementalDefendDark = 0,
                ElementalDefendEarth = -70,
                ElementalDefendAir = -70,
                ElementalDefendWater = -70,
                ElementalDefendFire = -70
            };

            // 设置异常抗性
            monster.AbnormalResists = new List<MonsterAbnormalResist>
            {
                new MonsterAbnormalResist
                {
                    AbnormalResistName = "Sleep",
                    AbnormalResistValue = 50
                }
            };

            // 设置异常穿透
            monster.AbnormalPenetrations = new List<MonsterAbnormalPenetration>
            {
                new MonsterAbnormalPenetration { AbnormalPenetrationValue = 0 },
                new MonsterAbnormalPenetration { AbnormalPenetrationValue = 0 },
                new MonsterAbnormalPenetration { AbnormalPenetrationValue = 0 }
            };

            // 设置攻击
            monster.Attacks = new List<MonsterAttack>
            {
                new MonsterAttack
                {
                    AttackMod = 100,
                    AttackRange = 2.0f,
                    AttackRate = 1000,
                    AttackType = "physical"
                }
            };

            // 设置技能
            monster.Skills = new List<MonsterSkill>
            {
                new MonsterSkill
                {
                    SkillName = "LDF4a_D2_SD_WeakenAtkDef",
                    SkillLevel = 1,
                    SkillRate = 0
                }
            };

            // 设置完整的掉落信息
            monster.ItemsInfo = CreateShelukWormDropInfo();

            return monster;
        }

        /// <summary>
        /// 创建Sheluk Worm的完整掉落信息
        /// </summary>
        private MonsterItemsInfo CreateShelukWormDropInfo()
        {
            var itemsInfo = new MonsterItemsInfo
            {
                MinCashAmount = 0,
                MaxCashAmount = 0,
                CashDropProb = 0
            };

            // 掉落组设置
            itemsInfo.DropGroups = new List<MonsterDropGroup>
            {
                new MonsterDropGroup { DropGroupMin = 0, DropGroupMax = 1 },
                new MonsterDropGroup { DropGroupMin = 0, DropGroupMax = 1 }
            };

            // 掉落物品设置 - 完整的实际数据
            itemsInfo.Items = new List<MonsterDropItem>
            {
                new MonsterDropItem
                {
                    Item = "junk_sheluk_60",
                    ItemCount = 1,
                    Prob = 6000000,
                    DropAttribute = "common",
                    DropGroupIdx = 1
                },
                new MonsterDropItem
                {
                    Item = "battery_50",
                    Prob = 1000000,
                    DropAttribute = "common",
                    DropGroupIdx = 0
                },
                new MonsterDropItem
                {
                    Item = "soulstone_n_c_05a",
                    ItemCount = 1,
                    Prob = 2250000,
                    DropAttribute = "common",
                    DropGroupIdx = 2
                },
                new MonsterDropItem
                {
                    Item = "quest_41191a",
                    ItemCount = 1,
                    Prob = 10000,
                    DropAttribute = "common",
                    DropGroupIdx = 0
                },
                new MonsterDropItem
                {
                    Item = "stigma_shard",
                    ItemCount = 3,
                    Prob = 2500000,
                    DropAttribute = "common",
                    DropGroupIdx = 0
                }
            };

            // 通用掉落设置 - 完整的实际数据（根据你提供的新配置）
            itemsInfo.CommonDrops = new List<MonsterCommonDrop>
            {
                new MonsterCommonDrop
                {
                    CommonDrop = "WEAPON_A_N_C2_54A",
                    CommonDropAdjustment = 19,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ARMOR_A_N_C2_52A",
                    CommonDropAdjustment = 9,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ACCESSORY_A_N_C2_55A",
                    CommonDropAdjustment = 35,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "WEAPON_A_N_R1_52A",
                    CommonDropAdjustment = 23,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ARMOR_A_N_R1_54A",
                    CommonDropAdjustment = 10,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ACCESSORY_A_N_R1_52A",
                    CommonDropAdjustment = 46,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "WEAPON_A_N_L1_53A",
                    CommonDropAdjustment = 28,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ARMOR_A_N_L1_50A",
                    CommonDropAdjustment = 18,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ACCESSORY_A_N_L1_51A",
                    CommonDropAdjustment = 65,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "WEAPON_A_N_U1_50A",
                    CommonDropAdjustment = 105,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "ARMOR_A_N_U1_50A",
                    CommonDropAdjustment = 53,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "SUPPLY_FOOD_60A",
                    CommonDropAdjustment = 50,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "MATTER_OPTION_55A",
                    CommonDropAdjustment = 16,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "MATTER_PROC_50A",
                    CommonDropAdjustment = 23,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "STIGMA_A_54A",
                    CommonDropAdjustment = 29,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "MATERIAL_KEY_54A",
                    CommonDropAdjustment = 41,
                    CommonDropGroupIdx = 0
                },
                new MonsterCommonDrop
                {
                    CommonDrop = "SKILLBOOK_L_53A",
                    CommonDropAdjustment = 104,
                    CommonDropGroupIdx = 0
                }
            };

            return itemsInfo;
        }

        #endregion
    }
}
