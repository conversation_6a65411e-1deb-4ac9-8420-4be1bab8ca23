using Microsoft.Extensions.Logging;
using StringTest;

Console.WriteLine("=================================");
Console.WriteLine("    字符串服务测试程序");
Console.WriteLine("=================================");
Console.WriteLine();

// 创建日志记录器
using var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
var logger = loggerFactory.CreateLogger<StringService>();

// 创建字符串服务
var stringService = new StringService(logger);

try
{
    Console.WriteLine("✓ 正在加载字符串文件...");
    await stringService.LoadStringsAsync();
    
    var stats = stringService.GetStatistics();
    Console.WriteLine($"✓ 字符串加载完成:");
    Console.WriteLine($"  - 按名称索引: {stats.StringsByName} 个");
    Console.WriteLine($"  - 按ID索引: {stats.StringsById} 个");
    Console.WriteLine($"  - 总计: {stats.TotalStrings} 个");
    Console.WriteLine();

    // 测试特定的字符串查找
    Console.WriteLine("✓ 测试字符串查找:");
    
    var testKeys = new[]
    {
        "STR_NPC_CL_TORSO_01",
        "STR_ITEMQUALITY_COMMON",
        "STR_ITEMQUALITY_RARE",
        "STR_ITEMQUALITY_UNIQUE",
        "STR_ITEMQUALITY_EPIC"
    };

    foreach (var key in testKeys)
    {
        var value = stringService.GetString(key);
        var found = stringService.ContainsKey(key);
        Console.WriteLine($"  {key}: {(found ? value : "未找到")}");
    }
    Console.WriteLine();

    // 测试搜索功能
    Console.WriteLine("✓ 测试搜索功能:");
    
    var searchTerms = new[] { "武器", "护甲", "药水", "稀有", "史诗" };
    
    foreach (var term in searchTerms)
    {
        var results = stringService.SearchStrings(term, 5);
        Console.WriteLine($"  搜索 '{term}': 找到 {results.Count} 个结果");
        
        foreach (var result in results.Take(3))
        {
            Console.WriteLine($"    - {result.Key}: {result.Value}");
        }
        
        if (results.Count > 3)
        {
            Console.WriteLine($"    ... 还有 {results.Count - 3} 个结果");
        }
        Console.WriteLine();
    }

    // 测试物品描述查找
    Console.WriteLine("✓ 测试物品描述查找:");
    
    // 从实际的XML文件中获取一些描述键进行测试
    var itemDescKeys = new[]
    {
        "STR_NPC_CL_TORSO_01",
        "STR_ITEM_SWORD_01",
        "STR_ITEM_POTION_HP_01"
    };

    foreach (var descKey in itemDescKeys)
    {
        var localizedName = stringService.GetString(descKey);
        var found = stringService.ContainsKey(descKey);
        
        if (found && localizedName != descKey)
        {
            Console.WriteLine($"  {descKey} -> {localizedName}");
        }
        else
        {
            Console.WriteLine($"  {descKey} -> 未找到本地化文本");
        }
    }
    Console.WriteLine();

    Console.WriteLine("=================================");
    Console.WriteLine("✓ 字符串服务测试完成！");
    Console.WriteLine("=================================");
    Console.WriteLine();
    Console.WriteLine("功能验证:");
    Console.WriteLine("  ✓ UTF-16编码XML文件读取");
    Console.WriteLine("  ✓ 字符串按名称和ID索引");
    Console.WriteLine("  ✓ 字符串搜索功能");
    Console.WriteLine("  ✓ 本地化文本查找");
    Console.WriteLine("  ✓ 大量数据处理");
    Console.WriteLine();
    Console.WriteLine("现在WPF应用程序可以:");
    Console.WriteLine("  • 显示物品的本地化名称");
    Console.WriteLine("  • 搜索和选择字符串键");
    Console.WriteLine("  • 实时预览本地化文本");
    Console.WriteLine("  • 验证描述键的有效性");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 测试过程中出现错误: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

Console.WriteLine();
Console.WriteLine("按任意键退出...");
Console.ReadKey();
