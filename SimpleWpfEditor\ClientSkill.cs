using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Xml.Serialization;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 客户端技能数据模型
    /// </summary>
    [XmlRoot("skill")]
    public class ClientSkill : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private int _id;
        private string _name = string.Empty;
        private string _desc = string.Empty;
        private string _skillType = string.Empty;
        private int _level = 1;
        private int _maxLevel = 1;
        private int _mpConsume = 0;
        private int _hpConsume = 0;
        private int _dpConsume = 0;
        private int _castTime = 0;
        private int _coolTime = 0;
        private int _duration = 0;
        private string _targetType = string.Empty;
        private string _targetRange = string.Empty;
        private int _targetMaxCount = 1;
        private string _effectType = string.Empty;
        private int _effectValue = 0;
        private string _animation = string.Empty;
        private string _icon = string.Empty;
        private bool _isModified = false;

        /// <summary>
        /// 技能ID
        /// </summary>
        [XmlElement("id")]
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 技能名称
        /// </summary>
        [XmlElement("name")]
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 技能描述
        /// </summary>
        [XmlElement("desc")]
        public string Desc
        {
            get => _desc;
            set
            {
                if (_desc != value)
                {
                    _desc = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 技能类型
        /// </summary>
        [XmlElement("skill_type")]
        public string SkillType
        {
            get => _skillType;
            set
            {
                if (_skillType != value)
                {
                    _skillType = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 技能等级
        /// </summary>
        [XmlElement("level")]
        public int Level
        {
            get => _level;
            set
            {
                if (_level != value)
                {
                    _level = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大等级
        /// </summary>
        [XmlElement("max_level")]
        public int MaxLevel
        {
            get => _maxLevel;
            set
            {
                if (_maxLevel != value)
                {
                    _maxLevel = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// MP消耗
        /// </summary>
        [XmlElement("mp_consume")]
        public int MpConsume
        {
            get => _mpConsume;
            set
            {
                if (_mpConsume != value)
                {
                    _mpConsume = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// HP消耗
        /// </summary>
        [XmlElement("hp_consume")]
        public int HpConsume
        {
            get => _hpConsume;
            set
            {
                if (_hpConsume != value)
                {
                    _hpConsume = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// DP消耗
        /// </summary>
        [XmlElement("dp_consume")]
        public int DpConsume
        {
            get => _dpConsume;
            set
            {
                if (_dpConsume != value)
                {
                    _dpConsume = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 施法时间(毫秒)
        /// </summary>
        [XmlElement("cast_time")]
        public int CastTime
        {
            get => _castTime;
            set
            {
                if (_castTime != value)
                {
                    _castTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 冷却时间(毫秒)
        /// </summary>
        [XmlElement("cool_time")]
        public int CoolTime
        {
            get => _coolTime;
            set
            {
                if (_coolTime != value)
                {
                    _coolTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 持续时间(毫秒)
        /// </summary>
        [XmlElement("duration")]
        public int Duration
        {
            get => _duration;
            set
            {
                if (_duration != value)
                {
                    _duration = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标类型
        /// </summary>
        [XmlElement("target_type")]
        public string TargetType
        {
            get => _targetType;
            set
            {
                if (_targetType != value)
                {
                    _targetType = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标范围
        /// </summary>
        [XmlElement("target_range")]
        public string TargetRange
        {
            get => _targetRange;
            set
            {
                if (_targetRange != value)
                {
                    _targetRange = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大目标数量
        /// </summary>
        [XmlElement("target_max_count")]
        public int TargetMaxCount
        {
            get => _targetMaxCount;
            set
            {
                if (_targetMaxCount != value)
                {
                    _targetMaxCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 效果类型
        /// </summary>
        [XmlElement("effect_type")]
        public string EffectType
        {
            get => _effectType;
            set
            {
                if (_effectType != value)
                {
                    _effectType = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 效果数值
        /// </summary>
        [XmlElement("effect_value")]
        public int EffectValue
        {
            get => _effectValue;
            set
            {
                if (_effectValue != value)
                {
                    _effectValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 动画名称
        /// </summary>
        [XmlElement("animation")]
        public string Animation
        {
            get => _animation;
            set
            {
                if (_animation != value)
                {
                    _animation = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        [XmlElement("icon")]
        public string Icon
        {
            get => _icon;
            set
            {
                if (_icon != value)
                {
                    _icon = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 标记技能是否被修改过（不保存到XML）
        /// </summary>
        [XmlIgnore]
        public bool IsModified
        {
            get => _isModified;
            set
            {
                if (_isModified != value)
                {
                    _isModified = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 显示名称（用于界面显示）
        /// </summary>
        [XmlIgnore]
        public string DisplayName => $"{Id} - {Name}";

        /// <summary>
        /// 技能类型显示文本
        /// </summary>
        [XmlIgnore]
        public string SkillTypeDisplay => SkillType switch
        {
            "ACTIVE" => "主动技能",
            "PASSIVE" => "被动技能",
            "TOGGLE" => "切换技能",
            "CHAIN" => "连锁技能",
            _ => SkillType
        };

        /// <summary>
        /// 目标类型显示文本
        /// </summary>
        [XmlIgnore]
        public string TargetTypeDisplay => TargetType switch
        {
            "SELF" => "自身",
            "ENEMY" => "敌人",
            "ALLY" => "盟友",
            "POINT" => "地点",
            "NONE" => "无目标",
            _ => TargetType
        };
    }
}
