<?xml version="1.0" encoding="UTF-8"?>
<skill_base_clients>
  <skill_base_client>
    <id>21368</id>
    <name>IU_Damage</name>
    <desc>STR_IU_Heal</desc>
    <type>Physical</type>
    <sub_type>Attack</sub_type>
    <skill_category>SKILLCTG_NONE</skill_category>
    <skillicon_name>icon_skill_idevent_Areaattack</skillicon_name>
    <activation_attribute>Active</activation_attribute>
    <cost_parameter>MP</cost_parameter>
    <cost_end>0</cost_end>
    <penalty_no_casting_time_succ>0</penalty_no_casting_time_succ>
    <target_slot>none</target_slot>
    <hostile_type>none</hostile_type>
    <delay_type>0</delay_type>
    <delay_time>0</delay_time>
    <target_maxcount>4</target_maxcount>
    <first_target>Me</first_target>
    <first_target_valid_distance>6</first_target_valid_distance>
    <target_range></target_range>
    <target_range_area_type>FireBall</target_range_area_type>
    <target_range_opt1>8</target_range_opt1>
    <target_range_opt3>4</target_range_opt3>
    <target_species_restriction>All</target_species_restriction>
    <target_relation_restriction>Enemy</target_relation_restriction>
    <cancel_rate>0</cancel_rate>
    <obstacle>4</obstacle>
    <casting_delay>0</casting_delay>
    <move_casting>1</move_casting>
    <auto_attack>stop</auto_attack>
    <motion_name></motion_name>
    <motion_play_speed>50</motion_play_speed>
    <effect1_type>NoReduceSpellATK_Instant</effect1_type>
    <effect1_noresist>1</effect1_noresist>
    <effect1_target_type>Target_Only</effect1_target_type>
    <effect1_reserved1>0</effect1_reserved1>
    <effect1_reserved2>12000</effect1_reserved2>
    <effect1_reserved3>0</effect1_reserved3>
    <effect1_reserved4>0</effect1_reserved4>
    <effect1_reserved6>0</effect1_reserved6>
    <effect1_reserved10>earth</effect1_reserved10>
    <effect1_reserved11>255</effect1_reserved11>
    <effect1_critical_prob_mod1>100</effect1_critical_prob_mod1>
    <effect2_type>DispelBuff</effect2_type>
    <effect2_noresist>1</effect2_noresist>
    <effect2_target_type>Target_Only</effect2_target_type>
    <effect2_cond_preeffect>e1</effect2_cond_preeffect>
    <effect2_reserved1>0</effect2_reserved1>
    <effect2_reserved2>3</effect2_reserved2>
    <effect2_reserved10>earth</effect2_reserved10>
    <effect2_reserved11>100</effect2_reserved11>
    <effect2_reserved12>120</effect2_reserved12>
    <effect2_reserved15>0</effect2_reserved15>
    <effect2_reserved16>10</effect2_reserved16>
    <effect2_reserved17>0</effect2_reserved17>
    <effect2_reserved18>10</effect2_reserved18>
    <effect2_critical_prob_mod2>100</effect2_critical_prob_mod2>
  </skill_base_client>
</skill_base_clients>
