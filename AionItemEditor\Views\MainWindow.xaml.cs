using System.Windows;
using AionItemEditor.ViewModels;

namespace AionItemEditor.Views;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        // 设置ViewModel
        DataContext = App.GetService<MainWindowViewModel>();
    }

    /// <summary>
    /// 窗口关闭时的处理
    /// </summary>
    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            // 检查是否有未保存的更改
            if (viewModel.IsFileModified)
            {
                var result = MessageBox.Show(
                    "当前文件有未保存的更改，是否保存？",
                    "确认关闭",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                switch (result)
                {
                    case MessageBoxResult.Yes:
                        // 保存文件
                        if (viewModel.SaveFileCommand.CanExecute(null))
                        {
                            viewModel.SaveFileCommand.Execute(null);
                        }
                        break;
                    case MessageBoxResult.Cancel:
                        // 取消关闭
                        e.Cancel = true;
                        return;
                }
            }
        }

        base.OnClosing(e);
    }
}
