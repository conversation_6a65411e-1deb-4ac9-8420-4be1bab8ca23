using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SimpleWpfEditor;

/// <summary>
/// NPC交易编辑器窗口
/// </summary>
public partial class NpcTradeEditorWindow : Window
{
    private readonly StringService? _stringService;
    private ClientNpcTradeInList _originalTradeList;
    private ClientNpcTradeInList _editingTradeList;
    private ObservableCollection<TradeInGoodsData> _goodsList;

    /// <summary>
    /// 数据是否已更改
    /// </summary>
    public bool DataChanged { get; private set; }

    /// <summary>
    /// 编辑后的交易列表数据
    /// </summary>
    public ClientNpcTradeInList EditedTradeList => _editingTradeList;

    public NpcTradeEditorWindow(ClientNpcTradeInList tradeList, StringService? stringService = null)
    {
        InitializeComponent();
        
        _stringService = stringService;
        _originalTradeList = tradeList;
        _editingTradeList = CloneTradeList(tradeList);
        _goodsList = new ObservableCollection<TradeInGoodsData>(_editingTradeList.GoodsList);
        
        InitializeUI();
        LoadTradeListData();
        BindEvents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        GoodsListBox.ItemsSource = _goodsList;
    }

    /// <summary>
    /// 加载交易列表数据
    /// </summary>
    private void LoadTradeListData()
    {
        if (_editingTradeList == null) return;

        // 基础信息
        IdTextBlock.Text = _editingTradeList.Id.ToString();
        TradeIdTextBlock.Text = $"ID: {_editingTradeList.Id}";
        NameTextBox.Text = _editingTradeList.Name;
        DescTextBox.Text = _editingTradeList.Desc;
        UseCategoryTextBox.Text = _editingTradeList.UseCategory.ToString();
        LocalizedNameTextBlock.Text = _editingTradeList.GetLocalizedDisplayName(_stringService);

        // 交易分类
        TradeTypeTextBlock.Text = _editingTradeList.TradeType;
        WeaponTypeTextBlock.Text = _editingTradeList.WeaponType;
        ArmorTypeTextBlock.Text = _editingTradeList.ArmorType;
        FactionTypeTextBlock.Text = _editingTradeList.FactionType;
        LevelTypeTextBlock.Text = _editingTradeList.LevelType;
        ItemCountTextBlock.Text = _editingTradeList.ItemCount.ToString();

        // 商品统计
        UpdateItemStatistics();

        // 更新窗口标题
        UpdateWindowTitle();
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        // 基础信息变更事件
        NameTextBox.TextChanged += (s, e) => { 
            _editingTradeList.Name = NameTextBox.Text; 
            DataChanged = true; 
            UpdateTradeClassification();
        };
        DescTextBox.TextChanged += (s, e) => { 
            _editingTradeList.Desc = DescTextBox.Text; 
            DataChanged = true; 
            LocalizedNameTextBlock.Text = _editingTradeList.GetLocalizedDisplayName(_stringService);
        };
        UseCategoryTextBox.TextChanged += (s, e) => { 
            if (int.TryParse(UseCategoryTextBox.Text, out var category)) { 
                _editingTradeList.UseCategory = category; 
                DataChanged = true; 
            } 
        };

        // 商品列表按钮事件
        AddItemButton.Click += AddItemButton_Click;
        RemoveItemButton.Click += RemoveItemButton_Click;

        // 底部按钮事件
        ResetButton.Click += ResetButton_Click;
        CancelButton.Click += CancelButton_Click;
        SaveButton.Click += SaveButton_Click;

        // 商品列表变更事件
        _goodsList.CollectionChanged += (s, e) => { 
            _editingTradeList.GoodsList = _goodsList.ToList(); 
            DataChanged = true; 
            UpdateItemStatistics();
            UpdateTradeClassification();
        };
    }

    /// <summary>
    /// 更新交易分类信息
    /// </summary>
    private void UpdateTradeClassification()
    {
        TradeTypeTextBlock.Text = _editingTradeList.TradeType;
        WeaponTypeTextBlock.Text = _editingTradeList.WeaponType;
        ArmorTypeTextBlock.Text = _editingTradeList.ArmorType;
        FactionTypeTextBlock.Text = _editingTradeList.FactionType;
        LevelTypeTextBlock.Text = _editingTradeList.LevelType;
        ItemCountTextBlock.Text = _editingTradeList.ItemCount.ToString();
        UpdateWindowTitle();
    }

    /// <summary>
    /// 更新商品统计
    /// </summary>
    private void UpdateItemStatistics()
    {
        var stats = _editingTradeList.GetItemTypeStatistics();
        var statsList = stats.Select(kvp => new { Key = kvp.Key, Value = kvp.Value }).ToList();
        ItemStatsListBox.ItemsSource = statsList;
    }

    /// <summary>
    /// 更新窗口标题
    /// </summary>
    private void UpdateWindowTitle()
    {
        if (_editingTradeList == null) return;

        var titleParts = new[] { "NPC交易编辑器" };
        
        // 添加交易类型信息
        var tradeInfo = $"{_editingTradeList.TradeType} - {_editingTradeList.FactionType}";
        titleParts = titleParts.Append(tradeInfo).ToArray();

        Title = string.Join(" - ", titleParts);
        TradeInfoTextBlock.Text = $"编辑 {tradeInfo} ({_editingTradeList.ItemCount} 件商品)";
    }

    /// <summary>
    /// 添加商品
    /// </summary>
    private void AddItemButton_Click(object sender, RoutedEventArgs e)
    {
        _goodsList.Add(new TradeInGoodsData { Item = "new_item_id" });
        StatusTextBlock.Text = "已添加新商品";
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
    {
        if (GoodsListBox.SelectedItem is TradeInGoodsData selectedGoods)
        {
            _goodsList.Remove(selectedGoods);
            StatusTextBlock.Text = "已删除选中的商品";
        }
        else
        {
            StatusTextBlock.Text = "请先选择要删除的商品";
        }
    }

    /// <summary>
    /// 重置按钮点击
    /// </summary>
    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("确定要重置所有更改吗？", "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            _editingTradeList = CloneTradeList(_originalTradeList);
            _goodsList.Clear();
            foreach (var goods in _editingTradeList.GoodsList)
            {
                _goodsList.Add(goods);
            }
            LoadTradeListData();
            DataChanged = false;
            StatusTextBlock.Text = "已重置所有更改";
        }
    }

    /// <summary>
    /// 取消按钮点击
    /// </summary>
    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataChanged)
        {
            var result = MessageBox.Show("有未保存的更改，确定要取消吗？", "确认取消", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.No)
                return;
        }

        DialogResult = false;
        Close();
    }

    /// <summary>
    /// 保存按钮点击
    /// </summary>
    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 验证数据
            if (!ValidateData())
                return;

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存时出错: {ex.Message}", "保存错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 验证数据
    /// </summary>
    private bool ValidateData()
    {
        if (_editingTradeList.Id <= 0)
        {
            MessageBox.Show("交易列表ID必须大于0", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingTradeList.Name))
        {
            MessageBox.Show("交易列表内部名称不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            NameTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(_editingTradeList.Desc))
        {
            MessageBox.Show("交易列表描述不能为空", "数据验证", MessageBoxButton.OK, MessageBoxImage.Warning);
            DescTextBox.Focus();
            return false;
        }

        // 验证商品列表中是否有空的物品ID
        var emptyItems = _goodsList.Where(g => string.IsNullOrWhiteSpace(g.Item)).ToList();
        if (emptyItems.Any())
        {
            var result = MessageBox.Show($"发现 {emptyItems.Count} 个空的物品ID，是否自动删除？", "数据验证", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                foreach (var emptyItem in emptyItems)
                {
                    _goodsList.Remove(emptyItem);
                }
            }
            else
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 克隆交易列表对象
    /// </summary>
    private ClientNpcTradeInList CloneTradeList(ClientNpcTradeInList original)
    {
        return new ClientNpcTradeInList
        {
            Id = original.Id,
            Name = original.Name,
            Desc = original.Desc,
            UseCategory = original.UseCategory,
            GoodsList = original.GoodsList.Select(g => new TradeInGoodsData { Item = g.Item }).ToList()
        };
    }
}
