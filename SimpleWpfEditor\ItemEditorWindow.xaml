<Window x:Class="SimpleWpfEditor.ItemEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="物品编辑器" 
        Height="800" 
        Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="#FAFAFA"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="TitleText" Text="物品编辑器" FontSize="20" FontWeight="SemiBold" Foreground="#2196F3"/>
                    <TextBlock x:Name="SubtitleText" Text="编辑物品属性" FontSize="14" Foreground="Gray" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#E3F2FD" CornerRadius="4" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="ItemIdText" Text="ID: 0" FontSize="12" Foreground="#1976D2" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#F3E5F5" CornerRadius="4" Padding="8,4">
                        <TextBlock x:Name="ItemTypeText" Text="类型: 未知" FontSize="12" Foreground="#7B1FA2" FontWeight="Medium"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主要编辑区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧面板 -->
                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                    
                    <!-- 基础信息 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📋 基础信息" FontSize="16" FontWeight="SemiBold" Foreground="#2196F3" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="ID:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="IdTextBox" IsReadOnly="True" Background="#F5F5F5"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="内部名称:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="NameTextBox"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="描述键:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DescTextBox"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="本地化:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock Grid.Column="1" x:Name="LocalizedNameText" Text="未找到" Foreground="Gray" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 物品类型 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="🏷️ 物品类型" FontSize="16" FontWeight="SemiBold" Foreground="#4CAF50" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="装备类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="ArmorTypeComboBox">
                                    <ComboBoxItem Content="weapon"/>
                                    <ComboBoxItem Content="clothes"/>
                                    <ComboBoxItem Content="armor"/>
                                    <ComboBoxItem Content="accessory"/>
                                    <ComboBoxItem Content="shield"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="物品类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="ItemTypeComboBox">
                                    <ComboBoxItem Content="normal"/>
                                    <ComboBoxItem Content="quest"/>
                                    <ComboBoxItem Content="cash"/>
                                    <ComboBoxItem Content="special"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="品质:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="QualityComboBox">
                                    <ComboBoxItem Content="common"/>
                                    <ComboBoxItem Content="rare"/>
                                    <ComboBoxItem Content="legend"/>
                                    <ComboBoxItem Content="unique"/>
                                    <ComboBoxItem Content="epic"/>
                                </ComboBox>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="装备部位:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="EquipmentSlotsComboBox">
                                    <ComboBoxItem Content="torso"/>
                                    <ComboBoxItem Content="leg"/>
                                    <ComboBoxItem Content="head"/>
                                    <ComboBoxItem Content="hand"/>
                                    <ComboBoxItem Content="foot"/>
                                    <ComboBoxItem Content="main_hand"/>
                                    <ComboBoxItem Content="sub_hand"/>
                                    <ComboBoxItem Content="main_or_sub"/>
                                    <ComboBoxItem Content="earring"/>
                                    <ComboBoxItem Content="neck"/>
                                    <ComboBoxItem Content="ring"/>
                                    <ComboBoxItem Content="wing"/>
                                </ComboBox>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 数值属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="🔢 数值属性" FontSize="16" FontWeight="SemiBold" Foreground="#FF9800" Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="价格:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="PriceTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="等级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="LevelTextBox"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="堆叠数:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MaxStackCountTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="闪避:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DodgeTextBox"/>
                            </Grid>
                            
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="物理防御:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="PhysicalDefendTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="魔法抗性:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MagicalResistTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="装备类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="EquipTypeTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="过期时间:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="ExpireTimeTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="使用等级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="UsableRankMinTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="购买等级:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="PurchableRankMinTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="缩放:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="ScaleTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="提取皮肤:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="ExtractSkinTypeTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 交易信息 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="💰 交易信息" FontSize="16" FontWeight="SemiBold" Foreground="#E91E63" Margin="0,0,0,16"/>

                            <!-- 交易模式选择 -->
                            <TextBlock Text="交易模式:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox x:Name="TradeModeComboBox" Margin="0,0,0,12" SelectionChanged="TradeModeComboBox_SelectionChanged">
                                <ComboBoxItem Content="模式1: 基础基纳购买" Tag="basic"/>
                                <ComboBoxItem Content="模式2: 混合货币购买 (基纳+额外货币)" Tag="mixed"/>
                                <ComboBoxItem Content="模式3: 兑换购买 (仅兑换物品)" Tag="exchange"/>
                                <ComboBoxItem Content="模式4: 复杂兑换购买 (基纳+额外货币+兑换物品)" Tag="complex"/>
                            </ComboBox>

                            <!-- 基础价格 -->
                            <Grid x:Name="BasicPriceGrid" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="60"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="基纳价格:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="TradePriceTextBox" Margin="0,0,8,0" ToolTip="基础价格，使用基纳货币"/>
                                <TextBlock Grid.Column="2" Text="基纳" VerticalAlignment="Center" Foreground="Gray"/>
                            </Grid>

                            <!-- 额外货币 -->
                            <StackPanel x:Name="ExtraCurrencyPanel" Visibility="Collapsed">
                                <TextBlock Text="额外货币 (混合模式):" FontWeight="Medium" Margin="0,8,0,8" Foreground="#FF5722"/>
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="货币物品:" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <ComboBox Grid.Column="1" x:Name="ExtraCurrencyItemComboBox" IsEditable="True">
                                        <ComboBoxItem Content="coin_combineskill_01 - 技能合成币"/>
                                        <ComboBoxItem Content="event_chaos_wingwar_coin_02 - 混沌翼战活动币"/>
                                        <ComboBoxItem Content="black_aion_toll_01 - 黑色永恒币"/>
                                        <ComboBoxItem Content="world_promotion_vip_continue_c - VIP续费币"/>
                                        <ComboBoxItem Content="junk_master_recipe_quest_40a - 40级任务配方材料"/>
                                        <ComboBoxItem Content="junk_master_recipe_quest_50a - 50级任务配方材料"/>
                                    </ComboBox>
                                </Grid>

                                <Grid Margin="0,0,0,12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="货币数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                    <TextBox Grid.Column="1" x:Name="ExtraCurrencyItemCountTextBox" ToolTip="额外货币的数量"/>
                                </Grid>
                            </StackPanel>

                            <!-- 兑换物品列表 -->
                            <StackPanel x:Name="TradeInItemPanel" Visibility="Collapsed">
                                <TextBlock Text="兑换物品列表 (兑换模式):" FontWeight="Medium" Margin="0,8,0,8" Foreground="#9C27B0"/>
                                <Border BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4" MaxHeight="120">
                                    <ListBox x:Name="TradeInItemListBox" Background="Transparent" BorderThickness="0">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Grid Margin="4">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="80"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBox Grid.Column="0" Text="{Binding TradeInItem, UpdateSourceTrigger=PropertyChanged}"
                                                             ToolTip="兑换物品ID" Margin="0,0,4,0"/>
                                                    <TextBox Grid.Column="1" Text="{Binding TradeInItemCount, UpdateSourceTrigger=PropertyChanged}"
                                                             ToolTip="数量" Margin="0,0,4,0"/>
                                                    <Button Grid.Column="2" Content="❌" Width="24" Height="24"
                                                            Click="RemoveTradeInItemButton_Click" Tag="{Binding}"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                    </ListBox>
                                </Border>

                                <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                    <Button x:Name="AddTradeInItemButton" Content="➕ 添加兑换物品" Click="AddTradeInItemButton_Click" Margin="0,0,8,0"/>
                                    <Button x:Name="AddCommonTradeItemButton" Content="📋 常用兑换币" Click="AddCommonTradeItemButton_Click" Margin="0,0,8,0"/>
                                </StackPanel>

                                <!-- 常用兑换币说明 -->
                                <Expander Header="💡 常用兑换物品ID参考" FontSize="11" FontWeight="Medium" Margin="0,8,0,0" Foreground="#7F8C8D">
                                    <StackPanel Margin="0,8,0,0">
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap" Margin="0,0,0,4">
                                            <Run Text="• 黑色永恒币: " FontWeight="Medium"/>black_aion_toll_31 (常用数量: 1-10000)
                                        </TextBlock>
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap" Margin="0,0,0,4">
                                            <Run Text="• 一级币: " FontWeight="Medium"/>COIN_01 (常用数量: 15)
                                        </TextBlock>
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap" Margin="0,0,0,4">
                                            <Run Text="• 二级币: " FontWeight="Medium"/>COIN_02 (常用数量: 10)
                                        </TextBlock>
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap" Margin="0,0,0,4">
                                            <Run Text="• 三级币: " FontWeight="Medium"/>COIN_03 (常用数量: 5)
                                        </TextBlock>
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap" Margin="0,0,0,4">
                                            <Run Text="• 防具部件令牌: " FontWeight="Medium"/>junk_armor_parts_token_dark/light (数量: 2)
                                        </TextBlock>
                                        <TextBlock FontSize="10" Foreground="#6C757D" TextWrapping="Wrap">
                                            <Run Text="• 世界事件券: " FontWeight="Medium"/>world_event_ticket_exchange_05 (数量: 10-50)
                                        </TextBlock>
                                    </StackPanel>
                                </Expander>
                            </StackPanel>

                            <!-- 交易模式示例 -->
                            <Border x:Name="TradeModeExampleBorder" Background="#E8F5E8" CornerRadius="4" Padding="12" Margin="0,12,0,0">
                                <StackPanel>
                                    <TextBlock Text="📋 当前模式XML示例:" FontWeight="Medium" FontSize="12" Foreground="#2E7D32" Margin="0,0,0,8"/>
                                    <TextBlock x:Name="TradeModeExampleTextBlock" FontSize="10" Foreground="#388E3C" TextWrapping="Wrap" FontFamily="Consolas"/>
                                </StackPanel>
                            </Border>

                            <!-- 交易成本摘要 -->
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8" Margin="0,8,0,0">
                                <StackPanel>
                                    <TextBlock Text="💰 交易成本摘要:" FontWeight="Medium" FontSize="12" Foreground="#495057" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="TradeCostSummaryTextBlock" Text="免费" FontSize="11" Foreground="#6C757D" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- 交易权限 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="🔒 交易权限" FontSize="16" FontWeight="SemiBold" Foreground="#FF5722" Margin="0,0,0,16"/>

                            <!-- 基础交易权限 -->
                            <TextBlock Text="基础权限:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <CheckBox x:Name="CanExchangeCheckBox" Content="可交易" Margin="0,4"/>
                                    <CheckBox x:Name="CanSellToNpcCheckBox" Content="可卖NPC" Margin="0,4"/>
                                    <CheckBox x:Name="ItemDropPermittedCheckBox" Content="可掉落" Margin="0,4"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <CheckBox x:Name="CanSplitCheckBox" Content="可分割" Margin="0,4"/>
                                    <CheckBox x:Name="CanApExtractionCheckBox" Content="可AP提取" Margin="0,4"/>
                                    <CheckBox x:Name="BreakableCheckBox" Content="可损坏" Margin="0,4"/>
                                </StackPanel>
                            </Grid>

                            <!-- 仓库权限 -->
                            <TextBlock Text="仓库权限:" FontWeight="Medium" Margin="0,12,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <CheckBox x:Name="CanDepositToCharacterWarehouseCheckBox" Content="角色仓库" Margin="0,4"/>
                                    <CheckBox x:Name="CanDepositToAccountWarehouseCheckBox" Content="账户仓库" Margin="0,4"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <CheckBox x:Name="CanDepositToGuildWarehouseCheckBox" Content="军团仓库" Margin="0,4"/>
                                </StackPanel>
                            </Grid>

                            <!-- 绑定属性 -->
                            <TextBlock Text="绑定属性:" FontWeight="Medium" Margin="0,12,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <CheckBox x:Name="SoulBindCheckBox" Content="灵魂绑定" Margin="0,4"/>
                                    <CheckBox x:Name="RemoveWhenLogoutCheckBox" Content="下线删除" Margin="0,4"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <CheckBox x:Name="LoreCheckBox" Content="传说物品" Margin="0,4"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 右侧面板 -->
                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                    
                    <!-- 职业权限 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="⚔️ 职业权限" FontSize="16" FontWeight="SemiBold" Foreground="#9C27B0" Margin="0,0,0,16"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <CheckBox x:Name="WarriorCheckBox" Content="剑星 (Warrior)" Margin="0,4"/>
                                    <CheckBox x:Name="ScoutCheckBox" Content="护星 (Scout)" Margin="0,4"/>
                                    <CheckBox x:Name="MageCheckBox" Content="魔道星 (Mage)" Margin="0,4"/>
                                    <CheckBox x:Name="ClericCheckBox" Content="治愈星 (Cleric)" Margin="0,4"/>
                                    <CheckBox x:Name="EngineerCheckBox" Content="工程师 (Engineer)" Margin="0,4"/>
                                    <CheckBox x:Name="ArtistCheckBox" Content="艺术家 (Artist)" Margin="0,4"/>
                                    <CheckBox x:Name="FighterCheckBox" Content="格斗家 (Fighter)" Margin="0,4"/>
                                    <CheckBox x:Name="KnightCheckBox" Content="骑士 (Knight)" Margin="0,4"/>
                                    <CheckBox x:Name="AssassinCheckBox" Content="杀星 (Assassin)" Margin="0,4"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <CheckBox x:Name="RangerCheckBox" Content="弓星 (Ranger)" Margin="0,4"/>
                                    <CheckBox x:Name="WizardCheckBox" Content="魔族 (Wizard)" Margin="0,4"/>
                                    <CheckBox x:Name="ElementalistCheckBox" Content="精灵使 (Elementalist)" Margin="0,4"/>
                                    <CheckBox x:Name="ChanterCheckBox" Content="吟游诗人 (Chanter)" Margin="0,4"/>
                                    <CheckBox x:Name="PriestCheckBox" Content="牧师 (Priest)" Margin="0,4"/>
                                    <CheckBox x:Name="GunnerCheckBox" Content="枪手 (Gunner)" Margin="0,4"/>
                                    <CheckBox x:Name="BardCheckBox" Content="乐师 (Bard)" Margin="0,4"/>
                                    <CheckBox x:Name="RiderCheckBox" Content="骑手 (Rider)" Margin="0,4"/>
                                </StackPanel>
                            </Grid>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                                <Button x:Name="SelectAllClassesButton" Content="全选" Click="SelectAllClassesButton_Click" Margin="0,0,8,0"/>
                                <Button x:Name="ClearAllClassesButton" Content="全清" Click="ClearAllClassesButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 物品属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="🌍 种族和特殊限制" FontSize="16" FontWeight="SemiBold" Foreground="#607D8B" Margin="0,0,0,16"/>
                            


                            <!-- 种族和性别限制 -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="种族限制:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="RacePermittedComboBox">
                                    <ComboBoxItem Content="全种族" Tag="pc_light pc_dark"/>
                                    <ComboBoxItem Content="仅天族" Tag="pc_light"/>
                                    <ComboBoxItem Content="仅魔族" Tag="pc_dark"/>
                                </ComboBox>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="性别限制:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="GenderPermittedComboBox">
                                    <ComboBoxItem Content="全性别" Tag="all"/>
                                    <ComboBoxItem Content="仅男性" Tag="male"/>
                                    <ComboBoxItem Content="仅女性" Tag="female"/>
                                </ComboBox>
                            </Grid>

                            <!-- 特殊字段 -->
                            <TextBlock Text="特殊字段:" FontWeight="Medium" Margin="0,12,0,8"/>
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="激活技能:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="ActivationSkillTextBox" ToolTip="激活技能ID，如：cash_food_l_shape_herlock_phy_01"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="激活模式:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="ActivationModeComboBox">
                                    <ComboBoxItem Content="无" Tag=""/>
                                    <ComboBoxItem Content="Both" Tag="Both"/>
                                    <ComboBoxItem Content="Active" Tag="Active"/>
                                    <ComboBoxItem Content="Passive" Tag="Passive"/>
                                </ComboBox>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="额外库存:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="ExtraInventoryTextBox" ToolTip="额外库存空间数量"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="分解物品:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DisassemblyItemTextBox" ToolTip="分解后获得的物品ID"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 交易摘要 -->
                    <Border Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="📊 交易摘要" FontSize="16" FontWeight="SemiBold" Foreground="#4CAF50" Margin="0,0,0,16"/>

                            <!-- 交易成本摘要 -->
                            <Border Background="#E8F5E8" CornerRadius="4" Padding="12" Margin="0,0,0,12">
                                <StackPanel>
                                    <TextBlock Text="💰 交易成本:" FontWeight="Medium" FontSize="12" Foreground="#2E7D32" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="DetailedTradeCostTextBlock" Text="免费" FontSize="11" Foreground="#388E3C" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- 权限摘要 -->
                            <Border Background="#E3F2FD" CornerRadius="4" Padding="12" Margin="0,0,0,12">
                                <StackPanel>
                                    <TextBlock Text="🔒 权限摘要:" FontWeight="Medium" FontSize="12" Foreground="#1565C0" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="PermissionSummaryTextBlock" Text="无权限" FontSize="11" Foreground="#1976D2" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- 职业限制摘要 -->
                            <Border Background="#FFF3E0" CornerRadius="4" Padding="12">
                                <StackPanel>
                                    <TextBlock Text="⚔️ 职业限制:" FontWeight="Medium" FontSize="12" Foreground="#E65100" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="ClassRestrictionSummaryTextBlock" Text="全职业" FontSize="11" Foreground="#F57C00" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- 武器专用属性 -->
                    <Border x:Name="WeaponSpecificBorder" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="⚔️ 武器专用属性" FontSize="16" FontWeight="SemiBold" Foreground="#FF5722" Margin="0,0,0,16"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="武器类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="WeaponTypeComboBox" Margin="0,0,8,0">
                                    <ComboBoxItem Content="1h_sword"/>
                                    <ComboBoxItem Content="2h_sword"/>
                                    <ComboBoxItem Content="1h_keyblade"/>
                                    <ComboBoxItem Content="2h_keyblade"/>
                                    <ComboBoxItem Content="1h_gun"/>
                                    <ComboBoxItem Content="dagger"/>
                                    <ComboBoxItem Content="bow"/>
                                    <ComboBoxItem Content="staff"/>
                                    <ComboBoxItem Content="orb"/>
                                    <ComboBoxItem Content="book"/>
                                    <ComboBoxItem Content="mace"/>
                                    <ComboBoxItem Content="shield"/>
                                    <ComboBoxItem Content="polearm"/>
                                    <ComboBoxItem Content="gun"/>
                                    <ComboBoxItem Content="cannon"/>
                                    <ComboBoxItem Content="harp"/>
                                    <ComboBoxItem Content="keyblade"/>
                                </ComboBox>
                                <TextBlock Grid.Column="2" Text="伤害贴花:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DmgDecalTextBox" ToolTip="伤害贴花类型"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="装备骨骼:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="EquipBoneTextBox" ToolTip="例如: Rhand_bone"/>
                            </Grid>

                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="战斗骨骼:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="CombatEquipBoneTextBox" ToolTip="例如: Rhand_bone"/>
                            </Grid>

                            <!-- 武器攻击属性 -->
                            <TextBlock Text="攻击属性" FontSize="14" FontWeight="Medium" Margin="0,16,0,8" Foreground="#FF5722"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="最小伤害:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MinDamageTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="最大伤害:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MaxDamageTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="力量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="StrTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="敏捷:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="AgiTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="知识:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="KnoTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="命中:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="HitAccuracyTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="暴击:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="CriticalTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="格挡:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="ParryTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="魔法增强:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MagicalSkillBoostTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="魔法命中:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="MagicalHitAccuracyTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="攻击类型:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <ComboBox Grid.Column="1" x:Name="AttackTypeComboBox" Margin="0,0,8,0">
                                    <ComboBoxItem Content="physical"/>
                                    <ComboBoxItem Content="magical_fire"/>
                                    <ComboBoxItem Content="magical_water"/>
                                    <ComboBoxItem Content="magical_earth"/>
                                    <ComboBoxItem Content="magical_wind"/>
                                </ComboBox>
                                <TextBlock Grid.Column="2" Text="攻击延迟:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="AttackDelayTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="攻击范围:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="AttackRangeTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="机器人名:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="RobotNameTextBox"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 基础奖励属性 -->
                    <Border x:Name="BasicBonusBorder" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="🎯 基础奖励属性" FontSize="16" FontWeight="SemiBold" Foreground="#E91E63" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="基础属性 1-4" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励1:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr1TextBox" ToolTip="例如: PhysicalAttack 50"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励2:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr2TextBox" ToolTip="例如: MagicalAttack 30"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励3:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr3TextBox" ToolTip="例如: AttackSpeed 15"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励4:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr4TextBox" ToolTip="例如: Critical 10"/>
                                    </Grid>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <TextBlock Text="基础属性 5-8" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励5:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr5TextBox" ToolTip="例如: Accuracy 20"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励6:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr6TextBox" ToolTip="例如: Parry 15"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励7:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr7TextBox" ToolTip="例如: Block 25"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="奖励8:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttr8TextBox" ToolTip="例如: Evasion 18"/>
                                    </Grid>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 高级奖励属性 -->
                    <Border x:Name="AdvancedBonusBorder" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="🔥 高级奖励属性" FontSize="16" FontWeight="SemiBold" Foreground="#9C27B0" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="高级属性 A1-A4" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级1:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA1TextBox" ToolTip="例如: PhysicalCriticalReduceRate 15"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级2:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA2TextBox" ToolTip="例如: magicalCriticalReduceRate 7"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级3:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA3TextBox" ToolTip="例如: magicalhitAccuracy 10"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级4:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA4TextBox" ToolTip="例如: magicalCritical 5"/>
                                    </Grid>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <TextBlock Text="高级属性 A5-A8" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级5:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA5TextBox" ToolTip="例如: physicalHitAccuracy 12"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级6:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA6TextBox" ToolTip="例如: physicalCritical 8"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级7:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA7TextBox" ToolTip="例如: magicalResist 20"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="高级8:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrA8TextBox" ToolTip="例如: physicalResist 25"/>
                                    </Grid>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 特殊效果属性 -->
                    <Border x:Name="SpecialEffectBorder" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="✨ 特殊效果属性" FontSize="16" FontWeight="SemiBold" Foreground="#FF9800" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="特殊效果 B1-B2" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="特效1:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrB1TextBox" ToolTip="例如: 特殊技能效果"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="特效2:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrB2TextBox" ToolTip="例如: 特殊状态效果"/>
                                    </Grid>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                    <TextBlock Text="特殊效果 B3-B4" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="特效3:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrB3TextBox" ToolTip="例如: 特殊光环效果"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="特效4:" VerticalAlignment="Center" FontWeight="Medium"/>
                                        <TextBox Grid.Column="1" x:Name="BonusAttrB4TextBox" ToolTip="例如: 特殊被动效果"/>
                                    </Grid>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 战斗和强化属性 -->
                    <Border x:Name="CombatEnhanceBorder" Background="White" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="⚡ 战斗和强化属性" FontSize="16" FontWeight="SemiBold" Foreground="#607D8B" Margin="0,0,0,16"/>

                            <TextBlock Text="燃烧属性" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="攻击燃烧:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="BurnOnAttackTextBox" Margin="0,0,8,0" ToolTip="攻击时燃烧值"/>
                                <TextBlock Grid.Column="2" Text="防御燃烧:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="BurnOnDefendTextBox" ToolTip="防御时燃烧值"/>
                            </Grid>

                            <TextBlock Text="抛光燃烧属性" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="抛光攻击:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="PolishBurnOnAttackTextBox" Margin="0,0,8,0" ToolTip="抛光后攻击燃烧值"/>
                                <TextBlock Grid.Column="2" Text="抛光防御:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="PolishBurnOnDefendTextBox" ToolTip="抛光后防御燃烧值"/>
                            </Grid>

                            <TextBlock Text="强化选项" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>



                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="限制分类:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="BmRestrictCategoryTextBox" Margin="0,0,8,0" ToolTip="BM限制分类"/>
                                <TextBlock Grid.Column="2" Text="分解物品:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DisassemblyItemTextBox2" ToolTip="分解后获得的物品ID"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 高级属性 -->
                    <Border Background="White" CornerRadius="8" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                        </Border.Effect>

                        <StackPanel>
                            <TextBlock Text="🔧 高级属性" FontSize="16" FontWeight="SemiBold" Foreground="#795548" Margin="0,0,0,16"/>

                            <TextBlock Text="描述信息" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="详细描述:" VerticalAlignment="Top" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBox Grid.Column="1" x:Name="DescLongTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" ToolTip="物品的详细描述"/>
                            </Grid>

                            <Separator Margin="0,8"/>

                            <TextBlock Text="外观和材质" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="材质:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MaterialTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="图标:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="IconNameTextBox"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="网格:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="MeshTextBox" Margin="0,0,8,0"/>
                                <TextBlock Grid.Column="2" Text="视觉槽:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="VisualSlotTextBox" ToolTip="例如: torso leg"/>
                            </Grid>

                            <Separator Margin="0,8"/>

                            <TextBlock Text="限制和权限" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>



                            <Separator Margin="0,8"/>

                            <TextBlock Text="颜色设置" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="男性颜色:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DefaultColorMTextBox" Margin="0,0,8,0" ToolTip="RGB格式: 255,255,255"/>
                                <TextBlock Grid.Column="2" Text="女性颜色:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DefaultColorFTextBox" ToolTip="RGB格式: 255,255,255"/>
                            </Grid>

                            <Separator Margin="0,8"/>

                            <TextBlock Text="交易设置" FontSize="14" FontWeight="Medium" Margin="0,8,0,8"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="交易物品:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="DisposableTradeItemTextBox" Margin="0,0,8,0" ToolTip="一次性交易物品"/>
                                <TextBlock Grid.Column="2" Text="交易数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="3" x:Name="DisposableTradeItemCountTextBox" ToolTip="一次性交易物品数量"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="包装数量:" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBox Grid.Column="1" x:Name="CanPackCountTextBox" ToolTip="可包装数量"/>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="ResetButton" 
                    Content="重置" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="ResetButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="SaveButton" 
                    Content="保存" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="#4CAF50"
                    Click="SaveButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="CancelButton" 
                    Content="取消" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
