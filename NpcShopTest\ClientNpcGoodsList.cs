using System.Collections.Generic;
using System.Xml.Serialization;
using System.Linq;

namespace NpcShopTest;

/// <summary>
/// NPC商店数据模型 - 基于真实的client_npc_goodslist.xml文件结构
/// </summary>
[XmlRoot("client_npc_goodslist")]
public class ClientNpcGoodsList
{
    /// <summary>
    /// 商店ID
    /// </summary>
    [XmlElement("id")]
    public int Id { get; set; }

    /// <summary>
    /// 商店内部名称
    /// </summary>
    [XmlElement("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 商店描述键
    /// </summary>
    [XmlElement("desc")]
    public string Desc { get; set; } = string.Empty;

    /// <summary>
    /// 商品列表
    /// </summary>
    [XmlArray("goods_list")]
    [XmlArrayItem("data")]
    public List<GoodsData> GoodsList { get; set; } = new List<GoodsData>();

    /// <summary>
    /// 显示名称（用于界面显示）
    /// </summary>
    [XmlIgnore]
    public string DisplayName => !string.IsNullOrEmpty(Desc) ? Desc : Name;

    /// <summary>
    /// 商店类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string ShopType
    {
        get
        {
            if (Name.Contains("skillbook"))
                return "技能书商店";
            else if (Name.Contains("material"))
                return "材料商店";
            else if (Name.Contains("recipe"))
                return "配方商店";
            else if (Name.Contains("weapon"))
                return "武器商店";
            else if (Name.Contains("armor"))
                return "防具商店";
            else if (Name.Contains("cooking"))
                return "烹饪商店";
            else if (Name.Contains("alchemy"))
                return "炼金商店";
            else if (Name.Contains("handiwork"))
                return "手工商店";
            else if (Name.Contains("tailoring"))
                return "裁缝商店";
            else
                return "通用商店";
        }
    }

    /// <summary>
    /// 职业类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string ProfessionType
    {
        get
        {
            if (Name.Contains("warrior"))
                return "战士";
            else if (Name.Contains("mage"))
                return "法师";
            else if (Name.Contains("scout"))
                return "侦察";
            else if (Name.Contains("cleric"))
                return "牧师";
            else if (Name.Contains("fighter"))
                return "格斗家";
            else if (Name.Contains("knight"))
                return "守护星";
            else if (Name.Contains("ranger"))
                return "游侠";
            else if (Name.Contains("assassin"))
                return "杀星";
            else if (Name.Contains("priest"))
                return "治愈星";
            else if (Name.Contains("chanter"))
                return "护法星";
            else if (Name.Contains("wizard"))
                return "魔道星";
            else if (Name.Contains("elementalist"))
                return "精灵星";
            else
                return "通用";
        }
    }

    /// <summary>
    /// 等级类型（根据名称推断）
    /// </summary>
    [XmlIgnore]
    public string LevelType
    {
        get
        {
            if (Name.Contains("high"))
                return "高级";
            else if (Name.Contains("master"))
                return "大师级";
            else if (Name.Contains("expert"))
                return "专家级";
            else
                return "基础";
        }
    }

    /// <summary>
    /// 商品数量
    /// </summary>
    [XmlIgnore]
    public int ItemCount => GoodsList?.Count ?? 0;

    /// <summary>
    /// 商品摘要
    /// </summary>
    [XmlIgnore]
    public string ItemsSummary
    {
        get
        {
            if (GoodsList == null || GoodsList.Count == 0)
                return "无商品";

            var items = GoodsList.Take(3).Select(g => g.Item).Where(i => !string.IsNullOrEmpty(i));
            var summary = string.Join(", ", items);
            
            if (GoodsList.Count > 3)
                summary += $" 等{GoodsList.Count}件商品";
            
            return !string.IsNullOrEmpty(summary) ? summary : "无商品";
        }
    }

    /// <summary>
    /// 获取本地化显示名称
    /// </summary>
    public string GetLocalizedDisplayName(object? stringService = null)
    {
        return DisplayName; // 简化版本，直接返回显示名称
    }

    /// <summary>
    /// 获取商品类型统计
    /// </summary>
    public Dictionary<string, int> GetItemTypeStatistics()
    {
        var stats = new Dictionary<string, int>();
        
        foreach (var goods in GoodsList)
        {
            if (string.IsNullOrEmpty(goods.Item)) continue;
            
            var itemType = GetItemType(goods.Item);
            if (stats.ContainsKey(itemType))
                stats[itemType]++;
            else
                stats[itemType] = 1;
        }
        
        return stats;
    }

    /// <summary>
    /// 根据物品名称推断物品类型
    /// </summary>
    private string GetItemType(string itemName)
    {
        if (itemName.StartsWith("skillbook_"))
            return "技能书";
        else if (itemName.Contains("material"))
            return "材料";
        else if (itemName.Contains("recipe"))
            return "配方";
        else if (itemName.Contains("weapon"))
            return "武器";
        else if (itemName.Contains("armor"))
            return "防具";
        else if (itemName.Contains("potion"))
            return "药水";
        else if (itemName.Contains("food"))
            return "食物";
        else
            return "其他";
    }
}

/// <summary>
/// 商品数据
/// </summary>
public class GoodsData
{
    /// <summary>
    /// 商品物品ID
    /// </summary>
    [XmlElement("item")]
    public string Item { get; set; } = string.Empty;
}

/// <summary>
/// NPC商店统计信息
/// </summary>
public class NpcGoodsListStatistics
{
    public int TotalShops { get; set; }
    public Dictionary<string, int> ShopTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> ProfessionTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> LevelTypes { get; set; } = new Dictionary<string, int>();
    public Dictionary<string, int> ItemTypes { get; set; } = new Dictionary<string, int>();
    public int TotalItems { get; set; }
    public double AverageItemsPerShop { get; set; }
    public int MaxItemsInShop { get; set; }
    public int MinItemsInShop { get; set; }
}

/// <summary>
/// 商店搜索筛选条件
/// </summary>
public class ShopFilterCriteria
{
    public string? SearchText { get; set; }
    public string? ShopType { get; set; }
    public string? ProfessionType { get; set; }
    public string? LevelType { get; set; }
    public int? MinItems { get; set; }
    public int? MaxItems { get; set; }
}
