using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using AionItemEditor.Models;
using Microsoft.Extensions.Logging;

namespace AionItemEditor.Services;

/// <summary>
/// XML数据服务实现类
/// 负责XML文件的读取、写入和管理
/// </summary>
public class XmlDataService : IXmlDataService
{
    private readonly ILogger<XmlDataService> _logger;
    private readonly XmlSerializer _itemSerializer;
    private readonly XmlSerializer _itemsSerializer;

    public XmlDataService(ILogger<XmlDataService> logger)
    {
        _logger = logger;
        _itemSerializer = new XmlSerializer(typeof(ClientItem));
        _itemsSerializer = new XmlSerializer(typeof(ClientItemCollection));
    }

    /// <summary>
    /// 加载XML文件中的所有物品
    /// </summary>
    public async Task<List<ClientItem>> LoadItemsAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载XML文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("文件不存在: {FilePath}", filePath);
                return new List<ClientItem>();
            }

            var items = new List<ClientItem>();
            var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);

            // 解析XML文档
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);

            var itemNodes = xmlDoc.SelectNodes("//client_item");
            if (itemNodes == null)
            {
                _logger.LogWarning("未找到client_item节点");
                return items;
            }

            foreach (XmlNode itemNode in itemNodes)
            {
                try
                {
                    var item = ParseClientItem(itemNode);
                    if (item != null)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析物品节点时出错");
                }
            }

            _logger.LogInformation("成功加载 {Count} 个物品", items.Count);
            return items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 保存物品列表到XML文件
    /// </summary>
    public async Task SaveItemsAsync(string filePath, List<ClientItem> items)
    {
        try
        {
            _logger.LogInformation("开始保存XML文件: {FilePath}, 物品数量: {Count}", filePath, items.Count);

            // 创建备份
            if (File.Exists(filePath))
            {
                await CreateBackupAsync(filePath);
            }

            var xmlDoc = new XmlDocument();
            var declaration = xmlDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            xmlDoc.AppendChild(declaration);

            var rootElement = xmlDoc.CreateElement("client_items");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            foreach (var item in items)
            {
                var itemElement = CreateItemElement(xmlDoc, item);
                rootElement.AppendChild(itemElement);
            }

            // 格式化XML并保存
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                NewLineChars = "\r\n"
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存XML文件: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存XML文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 验证XML文件格式
    /// </summary>
    public async Task<bool> ValidateXmlFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);

            // 检查根节点
            var rootNode = xmlDoc.DocumentElement;
            if (rootNode?.Name != "client_items")
                return false;

            // 检查是否有client_item节点
            var itemNodes = xmlDoc.SelectNodes("//client_item");
            return itemNodes?.Count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证XML文件时出错: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// 创建XML文件的备份
    /// </summary>
    public async Task<string> CreateBackupAsync(string filePath)
    {
        try
        {
            var backupPath = $"{filePath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
            File.Copy(filePath, backupPath);
            _logger.LogInformation("创建备份文件: {BackupPath}", backupPath);
            return backupPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建备份文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 获取支持的XML文件列表
    /// </summary>
    public async Task<List<string>> GetXmlFilesAsync(string directoryPath)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
                return new List<string>();

            var xmlFiles = Directory.GetFiles(directoryPath, "*.xml", SearchOption.TopDirectoryOnly)
                .Where(f => Path.GetFileName(f).StartsWith("client_items"))
                .ToList();

            _logger.LogInformation("找到 {Count} 个XML文件", xmlFiles.Count);
            return xmlFiles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取XML文件列表时出错: {DirectoryPath}", directoryPath);
            return new List<string>();
        }
    }

    /// <summary>
    /// 根据ID查找物品
    /// </summary>
    public ClientItem? FindItemById(List<ClientItem> items, int id)
    {
        return items.FirstOrDefault(item => item.Id == id);
    }

    /// <summary>
    /// 根据名称搜索物品
    /// </summary>
    public List<ClientItem> SearchItemsByName(List<ClientItem> items, string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return items;

        return items.Where(item =>
            item.Name.Contains(name, StringComparison.OrdinalIgnoreCase) ||
            item.Desc.Contains(name, StringComparison.OrdinalIgnoreCase))
            .ToList();
    }

    /// <summary>
    /// 验证物品数据
    /// </summary>
    public (bool IsValid, List<string> Errors) ValidateItem(ClientItem item)
    {
        var errors = new List<string>();
        var context = new ValidationContext(item);
        var results = new List<ValidationResult>();

        if (!Validator.TryValidateObject(item, context, results, true))
        {
            errors.AddRange(results.Select(r => r.ErrorMessage ?? "未知验证错误"));
        }

        // 自定义验证规则
        if (item.Id <= 0)
            errors.Add("物品ID必须大于0");

        if (string.IsNullOrWhiteSpace(item.Name))
            errors.Add("物品名称不能为空");

        if (item.MaxStackCount <= 0)
            errors.Add("最大堆叠数量必须大于0");

        return (errors.Count == 0, errors);
    }

    #region 私有辅助方法

    /// <summary>
    /// 解析client_item XML节点
    /// </summary>
    private ClientItem? ParseClientItem(XmlNode itemNode)
    {
        try
        {
            var item = new ClientItem();

            // 解析基础属性
            item.Id = GetIntValue(itemNode, "id");
            item.Name = GetStringValue(itemNode, "name");
            item.Desc = GetStringValue(itemNode, "desc");
            item.ArmorType = GetStringValue(itemNode, "armor_type");
            item.ItemType = GetStringValue(itemNode, "item_type");
            item.Material = GetStringValue(itemNode, "material");
            item.IconName = GetStringValue(itemNode, "icon_name");

            // 解析经济属性
            item.Price = GetIntValue(itemNode, "price");
            item.MaxStackCount = GetIntValue(itemNode, "max_stack_count", 1);
            item.CanSellToNpc = GetBoolValue(itemNode, "can_sell_to_npc", true);

            // 解析存储权限
            item.CanDepositToCharacterWarehouse = GetBoolValue(itemNode, "can_deposit_to_character_warehouse", true);
            item.CanDepositToAccountWarehouse = GetBoolValue(itemNode, "can_deposit_to_account_warehouse", true);
            item.CanDepositToGuildWarehouse = GetBoolValue(itemNode, "can_deposit_to_guild_warehouse", true);

            // 解析物品属性
            item.EquipmentSlots = GetStringValue(itemNode, "equipment_slots");
            item.Breakable = GetBoolValue(itemNode, "breakable", true);
            item.SoulBind = GetBoolValue(itemNode, "soul_bind", false);
            item.RemoveWhenLogout = GetBoolValue(itemNode, "remove_when_logout", false);
            item.GenderPermitted = GetStringValue(itemNode, "gender_permitted", "all");

            // 解析职业限制
            item.Warrior = GetIntValue(itemNode, "warrior", 1);
            item.Scout = GetIntValue(itemNode, "scout", 1);
            item.Mage = GetIntValue(itemNode, "mage", 1);
            item.Cleric = GetIntValue(itemNode, "cleric", 1);
            item.Engineer = GetIntValue(itemNode, "engineer", 1);
            item.Artist = GetIntValue(itemNode, "artist", 1);
            item.Fighter = GetIntValue(itemNode, "fighter", 1);
            item.Knight = GetIntValue(itemNode, "knight", 1);
            item.Assassin = GetIntValue(itemNode, "assassin", 1);
            item.Ranger = GetIntValue(itemNode, "ranger", 1);
            item.Wizard = GetIntValue(itemNode, "wizard", 1);
            item.Elementalist = GetIntValue(itemNode, "elementalist", 1);
            item.Chanter = GetIntValue(itemNode, "chanter", 1);
            item.Priest = GetIntValue(itemNode, "priest", 1);
            item.Gunner = GetIntValue(itemNode, "gunner", 1);
            item.Bard = GetIntValue(itemNode, "bard", 1);
            item.Rider = GetIntValue(itemNode, "rider", 1);

            // 解析强化属性
            item.OptionSlotValue = GetIntValue(itemNode, "option_slot_value");
            item.SpecialSlotValue = GetIntValue(itemNode, "special_slot_value");
            item.OptionSlotBonus = GetIntValue(itemNode, "option_slot_bonus");
            item.BonusApply = GetStringValue(itemNode, "bonus_apply", "equip");
            item.NoEnchant = GetBoolValue(itemNode, "no_enchant", false);
            item.MaxEnchantValue = GetIntValue(itemNode, "max_enchant_value", 15);
            item.CanProcEnchant = GetBoolValue(itemNode, "can_proc_enchant", true);
            item.CanCompositeWeapon = GetBoolValue(itemNode, "can_composite_weapon", true);

            // 解析外观和音效
            item.CannotChangeskin = GetIntValue(itemNode, "cannot_changeskin");
            item.UiSoundType = GetStringValue(itemNode, "ui_sound_type");
            item.Mesh = GetStringValue(itemNode, "mesh");
            item.MeshChange = GetIntValue(itemNode, "mesh_change");
            item.CanDye = GetIntValue(itemNode, "can_dye");

            // 解析特殊属性
            item.CashItem = GetIntValue(itemNode, "cash_item");
            item.CanSplit = GetBoolValue(itemNode, "can_split", false);
            item.ItemDropPermitted = GetBoolValue(itemNode, "item_drop_permitted", false);
            item.BmRestrictCategory = GetIntValue(itemNode, "bm_restrict_category", 1);
            item.CanApExtraction = GetBoolValue(itemNode, "can_ap_extraction", false);
            item.CanPolish = GetBoolValue(itemNode, "can_polish", true);
            item.PolishBurnOnAttack = GetIntValue(itemNode, "polish_burn_on_attack", 29);
            item.PolishBurnOnDefend = GetIntValue(itemNode, "polish_burn_on_defend", 12);
            item.RacePermitted = GetStringValue(itemNode, "race_permitted", "pc_light pc_dark");

            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析物品节点时出错");
            return null;
        }
    }

    /// <summary>
    /// 创建物品的XML元素
    /// </summary>
    private XmlElement CreateItemElement(XmlDocument xmlDoc, ClientItem item)
    {
        var itemElement = xmlDoc.CreateElement("client_item");

        // 基础信息
        AddElement(xmlDoc, itemElement, "id", item.Id.ToString());
        AddElement(xmlDoc, itemElement, "name", item.Name);
        AddElement(xmlDoc, itemElement, "desc", item.Desc);
        AddElement(xmlDoc, itemElement, "armor_type", item.ArmorType);
        AddElement(xmlDoc, itemElement, "item_type", item.ItemType);
        AddElement(xmlDoc, itemElement, "material", item.Material);
        AddElement(xmlDoc, itemElement, "icon_name", item.IconName);

        // 经济属性
        AddElement(xmlDoc, itemElement, "price", item.Price.ToString());
        AddElement(xmlDoc, itemElement, "max_stack_count", item.MaxStackCount.ToString());
        AddElement(xmlDoc, itemElement, "can_sell_to_npc", item.CanSellToNpc ? "TRUE" : "FALSE");

        // 存储权限
        AddElement(xmlDoc, itemElement, "can_deposit_to_character_warehouse", item.CanDepositToCharacterWarehouse ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_deposit_to_account_warehouse", item.CanDepositToAccountWarehouse ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_deposit_to_guild_warehouse", item.CanDepositToGuildWarehouse ? "TRUE" : "FALSE");

        // 物品属性
        AddElement(xmlDoc, itemElement, "equipment_slots", item.EquipmentSlots);
        AddElement(xmlDoc, itemElement, "breakable", item.Breakable ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "soul_bind", item.SoulBind ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "remove_when_logout", item.RemoveWhenLogout ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "gender_permitted", item.GenderPermitted);

        // 职业限制
        AddElement(xmlDoc, itemElement, "warrior", item.Warrior.ToString());
        AddElement(xmlDoc, itemElement, "scout", item.Scout.ToString());
        AddElement(xmlDoc, itemElement, "mage", item.Mage.ToString());
        AddElement(xmlDoc, itemElement, "cleric", item.Cleric.ToString());
        AddElement(xmlDoc, itemElement, "engineer", item.Engineer.ToString());
        AddElement(xmlDoc, itemElement, "artist", item.Artist.ToString());
        AddElement(xmlDoc, itemElement, "fighter", item.Fighter.ToString());
        AddElement(xmlDoc, itemElement, "knight", item.Knight.ToString());
        AddElement(xmlDoc, itemElement, "assassin", item.Assassin.ToString());
        AddElement(xmlDoc, itemElement, "ranger", item.Ranger.ToString());
        AddElement(xmlDoc, itemElement, "wizard", item.Wizard.ToString());
        AddElement(xmlDoc, itemElement, "elementalist", item.Elementalist.ToString());
        AddElement(xmlDoc, itemElement, "chanter", item.Chanter.ToString());
        AddElement(xmlDoc, itemElement, "priest", item.Priest.ToString());
        AddElement(xmlDoc, itemElement, "gunner", item.Gunner.ToString());
        AddElement(xmlDoc, itemElement, "bard", item.Bard.ToString());
        AddElement(xmlDoc, itemElement, "rider", item.Rider.ToString());

        // 强化属性
        AddElement(xmlDoc, itemElement, "option_slot_value", item.OptionSlotValue.ToString());
        AddElement(xmlDoc, itemElement, "special_slot_value", item.SpecialSlotValue.ToString());
        AddElement(xmlDoc, itemElement, "option_slot_bonus", item.OptionSlotBonus.ToString());
        AddElement(xmlDoc, itemElement, "bonus_apply", item.BonusApply);
        AddElement(xmlDoc, itemElement, "no_enchant", item.NoEnchant ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "max_enchant_value", item.MaxEnchantValue.ToString());
        AddElement(xmlDoc, itemElement, "can_proc_enchant", item.CanProcEnchant ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_composite_weapon", item.CanCompositeWeapon ? "TRUE" : "FALSE");

        // 外观和音效
        AddElement(xmlDoc, itemElement, "cannot_changeskin", item.CannotChangeskin.ToString());
        AddElement(xmlDoc, itemElement, "ui_sound_type", item.UiSoundType);
        AddElement(xmlDoc, itemElement, "mesh", item.Mesh);
        AddElement(xmlDoc, itemElement, "mesh_change", item.MeshChange.ToString());
        AddElement(xmlDoc, itemElement, "can_dye", item.CanDye.ToString());

        // 特殊属性
        AddElement(xmlDoc, itemElement, "cash_item", item.CashItem.ToString());
        AddElement(xmlDoc, itemElement, "can_split", item.CanSplit ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "item_drop_permitted", item.ItemDropPermitted ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "bm_restrict_category", item.BmRestrictCategory.ToString());
        AddElement(xmlDoc, itemElement, "can_ap_extraction", item.CanApExtraction ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "can_polish", item.CanPolish ? "TRUE" : "FALSE");
        AddElement(xmlDoc, itemElement, "polish_burn_on_attack", item.PolishBurnOnAttack.ToString());
        AddElement(xmlDoc, itemElement, "polish_burn_on_defend", item.PolishBurnOnDefend.ToString());
        AddElement(xmlDoc, itemElement, "race_permitted", item.RacePermitted);

        return itemElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AddElement(XmlDocument xmlDoc, XmlElement parent, string name, string value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            var element = xmlDoc.CreateElement(name);
            element.InnerText = value;
            parent.AppendChild(element);
        }
    }

    /// <summary>
    /// 获取XML节点的字符串值
    /// </summary>
    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText ?? defaultValue;
    }

    /// <summary>
    /// 获取XML节点的整数值
    /// </summary>
    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText, out var value))
            return value;
        return defaultValue;
    }

    /// <summary>
    /// 获取XML节点的布尔值
    /// </summary>
    private bool GetBoolValue(XmlNode parentNode, string elementName, bool defaultValue = false)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null)
        {
            var text = node.InnerText.ToUpperInvariant();
            return text == "TRUE" || text == "1";
        }
        return defaultValue;
    }

    #endregion
}

/// <summary>
/// 物品集合类，用于XML序列化
/// </summary>
[XmlRoot("client_items")]
public class ClientItemCollection
{
    [XmlElement("client_item")]
    public List<ClientItem> Items { get; set; } = new();

    [XmlAttribute("generated_time")]
    public string GeneratedTime { get; set; } = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
}
