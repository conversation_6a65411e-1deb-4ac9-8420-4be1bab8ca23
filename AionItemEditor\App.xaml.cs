using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Windows;
using AionItemEditor.Services;
using AionItemEditor.ViewModels;

namespace AionItemEditor;

/// <summary>
/// 应用程序主类
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // 配置日志
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.File("logs/app-.log", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // 配置依赖注入
        _host = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // 注册服务
                services.AddSingleton<IXmlDataService, XmlDataService>();
                services.AddSingleton<IDialogService, DialogService>();
                
                // 注册ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<ItemEditViewModel>();
                
                // 注册Views
                services.AddTransient<Views.MainWindow>();
            })
            .Build();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        Log.CloseAndFlush();
        base.OnExit(e);
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    public static T GetService<T>() where T : class
    {
        return ((App)Current)._host?.Services.GetRequiredService<T>() 
               ?? throw new InvalidOperationException($"Service {typeof(T).Name} not found");
    }
}
