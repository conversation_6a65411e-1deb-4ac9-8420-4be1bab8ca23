<Window x:Class="SimpleWpfEditor.ProfessionalSkillManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="专业技能管理器" Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <materialDesign:PackIcon Grid.Column="0" Kind="MagicStaff" Width="32" Height="32" 
                                         Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="专业技能管理器" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="管理 skill_base_client 格式的专业技能数据" FontSize="14" Foreground="#BDC3C7" Margin="0,4,0,0"/>
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="OpenFileButton" Content="📁 打开文件" Padding="12,8" Margin="0,0,8,0" 
                            Background="#3498DB" Foreground="White" BorderThickness="0" Click="OpenFileButton_Click"/>
                    <Button x:Name="SaveFileButton" Content="💾 保存文件" Padding="12,8" Margin="0,0,8,0" 
                            Background="#27AE60" Foreground="White" BorderThickness="0" Click="SaveFileButton_Click"/>
                    <Button x:Name="AddSkillButton" Content="➕ 添加技能" Padding="12,8" 
                            Background="#E67E22" Foreground="White" BorderThickness="0" Click="AddSkillButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏和筛选 -->
        <Border Grid.Row="1" Background="#ECF0F1" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 搜索框 -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0" 
                         materialDesign:HintAssist.Hint="搜索技能ID、名称或描述..." 
                         Margin="0,0,16,0" TextChanged="SearchTextBox_TextChanged"/>

                <!-- 类型筛选 -->
                <ComboBox x:Name="TypeFilterComboBox" Grid.Column="1" Width="120" Margin="0,0,8,0"
                          materialDesign:HintAssist.Hint="技能类型" SelectionChanged="TypeFilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="全部类型" Tag=""/>
                    <ComboBoxItem Content="Magical" Tag="Magical"/>
                    <ComboBoxItem Content="Physical" Tag="Physical"/>
                    <ComboBoxItem Content="Stigma" Tag="Stigma"/>
                </ComboBox>

                <!-- 激活类型筛选 -->
                <ComboBox x:Name="ActivationFilterComboBox" Grid.Column="2" Width="120" Margin="0,0,8,0"
                          materialDesign:HintAssist.Hint="激活类型" SelectionChanged="ActivationFilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="全部激活" Tag=""/>
                    <ComboBoxItem Content="Active" Tag="Active"/>
                    <ComboBoxItem Content="Passive" Tag="Passive"/>
                    <ComboBoxItem Content="Toggle" Tag="Toggle"/>
                </ComboBox>

                <!-- 清除筛选按钮 -->
                <Button x:Name="ClearFilterButton" Grid.Column="3" Content="🧹 清除筛选" 
                        Padding="8,6" Background="#95A5A6" Foreground="White" BorderThickness="0" Click="ClearFilterButton_Click"/>
            </Grid>
        </Border>

        <!-- 主内容区域 - 左右分栏 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1.5*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：技能列表 -->
            <DataGrid x:Name="SkillsDataGrid" Grid.Column="0"
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False" 
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      Background="White"
                      AlternatingRowBackground="#F8F9FA"
                      RowHeight="35"
                      SelectionChanged="SkillsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>
                    <DataGridTextColumn Header="技能名称" Binding="{Binding DisplayName}" Width="180" IsReadOnly="True"/>
                    <DataGridTextColumn Header="类型" Binding="{Binding TypeDisplay}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="激活" Binding="{Binding ActivationDisplay}" Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="效果" Binding="{Binding EffectSummary}" Width="150" IsReadOnly="True"/>
                </DataGrid.Columns>
                
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem x:Name="EditSkillMenuItem" Header="📝 编辑技能" Click="EditSkillMenuItem_Click"/>
                        <MenuItem x:Name="CopySkillMenuItem" Header="📋 复制技能" Click="CopySkillMenuItem_Click"/>
                        <Separator/>
                        <MenuItem x:Name="DeleteSkillMenuItem" Header="🗑️ 删除技能" Click="DeleteSkillMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>

            <!-- 分隔线 -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="#BDC3C7"/>

            <!-- 右侧：技能编辑面板 -->
            <Border Grid.Column="2" Background="White" BorderBrush="#BDC3C7" BorderThickness="1,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="SkillEditPanel" Margin="16" IsEnabled="False">
                        <!-- 编辑面板标题 -->
                        <TextBlock Text="专业技能编辑" FontSize="18" FontWeight="Bold" Margin="0,0,0,16" Foreground="#2C3E50"/>
                        
                        <!-- 基础信息 -->
                        <Expander Header="基础信息" IsExpanded="True" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="技能ID:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillIdTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8" IsReadOnly="True" Background="#F8F9FA"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="技能名称:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillNameTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="描述:" VerticalAlignment="Top" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillDescTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"
                                         TextWrapping="Wrap" AcceptsReturn="True" Height="60"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="详细描述:" VerticalAlignment="Top" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillDescLongTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"
                                         TextWrapping="Wrap" AcceptsReturn="True" Height="60"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="技能类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="SkillTypeComboBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="Magical" Tag="Magical"/>
                                    <ComboBoxItem Content="Physical" Tag="Physical"/>
                                    <ComboBoxItem Content="Stigma" Tag="Stigma"/>
                                </ComboBox>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="子类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="SkillSubTypeComboBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="None" Tag="None"/>
                                    <ComboBoxItem Content="Attack" Tag="Attack"/>
                                    <ComboBoxItem Content="Heal" Tag="Heal"/>
                                    <ComboBoxItem Content="Buff" Tag="Buff"/>
                                    <ComboBoxItem Content="Debuff" Tag="Debuff"/>
                                </ComboBox>

                                <TextBlock Grid.Row="6" Grid.Column="0" Text="激活属性:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="SkillActivationComboBox" Grid.Row="6" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="Active" Tag="Active"/>
                                    <ComboBoxItem Content="Passive" Tag="Passive"/>
                                    <ComboBoxItem Content="Toggle" Tag="Toggle"/>
                                </ComboBox>

                                <TextBlock Grid.Row="7" Grid.Column="0" Text="图标名称:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillIconNameTextBox" Grid.Row="7" Grid.Column="1" Margin="0,0,0,8"/>
                            </Grid>
                        </Expander>

                        <!-- 时间和行为属性 -->
                        <Expander Header="时间和行为属性" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="施法延迟(ms):" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="CastingDelayTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="取消率:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="CancelRateTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="障碍物:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="ObstacleTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="移动施法:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="MoveCastingComboBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="允许" Tag="1"/>
                                    <ComboBoxItem Content="禁止" Tag="0"/>
                                </ComboBox>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="自动攻击:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="AutoAttackComboBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="停止" Tag="stop"/>
                                    <ComboBoxItem Content="继续" Tag="continue"/>
                                </ComboBox>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="消耗参数:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="CostParameterComboBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="MP" Tag="MP"/>
                                    <ComboBoxItem Content="HP" Tag="HP"/>
                                    <ComboBoxItem Content="DP" Tag="DP"/>
                                </ComboBox>
                            </Grid>
                        </Expander>

                        <!-- 消耗和成本 -->
                        <Expander Header="消耗和成本" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="消耗值:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="CostEndTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="武器充能消耗:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="CostChargeWeaponTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="护甲充能消耗:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="CostChargeArmorTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="抛光充能武器:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="PolishChargeWeaponTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="驱散分类:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="DispelCategoryComboBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="" Tag=""/>
                                    <ComboBoxItem Content="Buff" Tag="Buff"/>
                                    <ComboBoxItem Content="Debuff" Tag="Debuff"/>
                                </ComboBox>
                            </Grid>
                        </Expander>

                        <!-- 目标系统 -->
                        <Expander Header="目标系统" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="目标槽位:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="TargetSlotComboBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="none" Tag="none"/>
                                    <ComboBoxItem Content="buff" Tag="buff"/>
                                    <ComboBoxItem Content="Buff" Tag="Buff"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="敌对类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="HostileTypeComboBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="none" Tag="none"/>
                                    <ComboBoxItem Content="None" Tag="None"/>
                                    <ComboBoxItem Content="direct" Tag="direct"/>
                                    <ComboBoxItem Content="indirect" Tag="indirect"/>
                                </ComboBox>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="延迟时间(ms):" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="DelayTimeTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="目标最大数量:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="TargetMaxcountTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="首要目标:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="FirstTargetComboBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="Me" Tag="Me"/>
                                    <ComboBoxItem Content="ME" Tag="ME"/>
                                    <ComboBoxItem Content="Target" Tag="Target"/>
                                </ComboBox>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="目标范围:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="TargetRangeComboBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="" Tag=""/>
                                    <ComboBoxItem Content="OnlyOne" Tag="OnlyOne"/>
                                    <ComboBoxItem Content="Area" Tag="Area"/>
                                    <ComboBoxItem Content="Chain" Tag="Chain"/>
                                </ComboBox>

                                <TextBlock Grid.Row="6" Grid.Column="0" Text="目标种族限制:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="TargetSpeciesRestrictionComboBox" Grid.Row="6" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="All" Tag="All"/>
                                    <ComboBoxItem Content="Player" Tag="Player"/>
                                    <ComboBoxItem Content="NPC" Tag="NPC"/>
                                </ComboBox>

                                <TextBlock Grid.Row="7" Grid.Column="0" Text="目标关系限制:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="TargetRelationRestrictionComboBox" Grid.Row="7" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="All" Tag="All"/>
                                    <ComboBoxItem Content="Enemy" Tag="Enemy"/>
                                    <ComboBoxItem Content="Friend" Tag="Friend"/>
                                </ComboBox>
                            </Grid>
                        </Expander>

                        <!-- 动作和动画 -->
                        <Expander Header="动作和动画" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="动作名称:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="MotionNameTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="动作播放速度:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="MotionPlaySpeedTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="瞬发技能:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <CheckBox x:Name="InstantSkillCheckBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="和平技能:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <CheckBox x:Name="PeaceSkillCheckBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="显示武器:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <CheckBox x:Name="ShowWeaponCheckBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8"/>
                            </Grid>
                        </Expander>

                        <!-- 效果1系统 -->
                        <Expander Header="效果1系统" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="效果类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1TypeTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="目标类型:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <ComboBox x:Name="Effect1TargetTypeComboBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8">
                                    <ComboBoxItem Content="" Tag=""/>
                                    <ComboBoxItem Content="Target_Only" Tag="Target_Only"/>
                                    <ComboBoxItem Content="Self_Only" Tag="Self_Only"/>
                                    <ComboBoxItem Content="Area" Tag="Area"/>
                                </ComboBox>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="持续时间1:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Remain1TextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="持续时间2:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Remain2TextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="效果ID:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1EffectIdTextBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="保留值2:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Reserved2TextBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="6" Grid.Column="0" Text="保留值4:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Reserved4TextBox" Grid.Row="6" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="7" Grid.Column="0" Text="保留值13:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Reserved13TextBox" Grid.Row="7" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="8" Grid.Column="0" Text="保留值14:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="Effect1Reserved14TextBox" Grid.Row="8" Grid.Column="1" Margin="0,0,0,8"/>
                            </Grid>
                        </Expander>

                        <!-- 高级属性 -->
                        <Expander Header="高级属性" IsExpanded="False" Margin="0,0,0,8">
                            <Grid Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="技能分类:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillCategoryTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="延迟ID:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="DelayIdTextBox" Grid.Row="1" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="连锁分类等级:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="ChainCategoryLevelTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="技能组名称:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                                <TextBox x:Name="SkillGroupNameTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="异常描述:" VerticalAlignment="Top" Margin="0,0,8,8"/>
                                <TextBox x:Name="DescAbnormalTextBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,8"
                                         TextWrapping="Wrap" AcceptsReturn="True" Height="40"/>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="前置链分类名:" VerticalAlignment="Top" Margin="0,0,8,8"/>
                                <TextBox x:Name="DescPrechainCategoryNameTextBox" Grid.Row="5" Grid.Column="1" Margin="0,0,0,8"
                                         TextWrapping="Wrap" AcceptsReturn="True" Height="40"/>
                            </Grid>
                        </Expander>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                            <Button x:Name="SaveSkillButton" Content="💾 保存修改" Padding="12,8" Margin="0,0,8,0" 
                                    Background="#27AE60" Foreground="White" BorderThickness="0" Click="SaveSkillButton_Click"/>
                            <Button x:Name="CancelEditButton" Content="❌ 取消" Padding="12,8" 
                                    Background="#95A5A6" Foreground="White" BorderThickness="0" Click="CancelEditButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#34495E" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusText" Grid.Column="0" Text="就绪" VerticalAlignment="Center" Foreground="White"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="技能总数: " VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock x:Name="TotalSkillsText" Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,16,0" Foreground="#3498DB"/>
                    <TextBlock Text="当前显示: " VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock x:Name="FilteredSkillsText" Text="0" FontWeight="Bold" VerticalAlignment="Center" Foreground="#E74C3C"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
