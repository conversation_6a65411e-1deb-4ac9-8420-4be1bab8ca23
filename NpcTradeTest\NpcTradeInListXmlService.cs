using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace NpcTradeTest;

/// <summary>
/// NPC交易列表XML文件服务
/// </summary>
public class NpcTradeInListXmlService
{
    private readonly ILogger<NpcTradeInListXmlService> _logger;

    public NpcTradeInListXmlService(ILogger<NpcTradeInListXmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加载NPC交易列表文件
    /// </summary>
    public async Task<List<ClientNpcTradeInList>> LoadNpcTradeInListsAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载NPC交易列表文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"NPC交易列表文件不存在: {filePath}");
            }

            var tradeLists = new List<ClientNpcTradeInList>();
            var xmlDoc = new XmlDocument();
            
            // 读取XML文件
            var xmlContent = await File.ReadAllTextAsync(filePath);
            xmlDoc.LoadXml(xmlContent);

            var tradeListNodes = xmlDoc.SelectNodes("//client_npc_trade_in_list");
            if (tradeListNodes == null)
            {
                _logger.LogWarning("未找到NPC交易列表节点");
                return tradeLists;
            }

            foreach (XmlNode tradeListNode in tradeListNodes)
            {
                try
                {
                    var tradeList = ParseClientNpcTradeInList(tradeListNode);
                    tradeLists.Add(tradeList);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析NPC交易列表时出错: {TradeListNode}", tradeListNode?.OuterXml);
                }
            }

            _logger.LogInformation("成功加载 {Count} 个NPC交易列表", tradeLists.Count);
            return tradeLists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载NPC交易列表文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 解析NPC交易列表节点
    /// </summary>
    private ClientNpcTradeInList ParseClientNpcTradeInList(XmlNode tradeListNode)
    {
        var tradeList = new ClientNpcTradeInList
        {
            Id = GetIntValue(tradeListNode, "id", 0),
            Name = GetStringValue(tradeListNode, "name"),
            Desc = GetStringValue(tradeListNode, "desc"),
            UseCategory = GetIntValue(tradeListNode, "use_category", 0)
        };

        // 解析商品列表
        var goodsListXmlNode = tradeListNode.SelectSingleNode("goods_list");
        if (goodsListXmlNode != null)
        {
            var dataNodes = goodsListXmlNode.SelectNodes("data");
            if (dataNodes != null)
            {
                foreach (XmlNode dataNode in dataNodes)
                {
                    var itemNode = dataNode.SelectSingleNode("item");
                    if (itemNode != null && !string.IsNullOrEmpty(itemNode.InnerText))
                    {
                        tradeList.GoodsList.Add(new TradeInGoodsData
                        {
                            Item = itemNode.InnerText.Trim()
                        });
                    }
                }
            }
        }

        return tradeList;
    }

    /// <summary>
    /// 获取字符串值
    /// </summary>
    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText?.Trim() ?? defaultValue;
    }

    /// <summary>
    /// 获取整数值
    /// </summary>
    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText?.Trim(), out var value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 保存NPC交易列表到XML文件
    /// </summary>
    public async Task SaveNpcTradeInListsAsync(string filePath, List<ClientNpcTradeInList> tradeLists)
    {
        try
        {
            _logger.LogInformation("开始保存NPC交易列表文件: {FilePath}", filePath);

            var xmlDoc = new XmlDocument();
            
            // 创建根节点
            var rootElement = xmlDoc.CreateElement("client_npc_trade_in_lists");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有交易列表
            foreach (var tradeList in tradeLists)
            {
                var tradeListElement = CreateTradeListXmlElement(xmlDoc, tradeList);
                rootElement.AppendChild(tradeListElement);
            }

            // 保存到文件
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = System.Text.Encoding.Unicode // 使用UTF-16编码，与原文件一致
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存 {Count} 个NPC交易列表到文件", tradeLists.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存NPC交易列表文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 创建交易列表的XML元素
    /// </summary>
    private XmlElement CreateTradeListXmlElement(XmlDocument xmlDoc, ClientNpcTradeInList tradeList)
    {
        var tradeListElement = xmlDoc.CreateElement("client_npc_trade_in_list");

        // 添加基础属性
        AddXmlElement(xmlDoc, tradeListElement, "id", tradeList.Id.ToString());
        AddXmlElement(xmlDoc, tradeListElement, "name", tradeList.Name);
        AddXmlElement(xmlDoc, tradeListElement, "desc", tradeList.Desc);
        AddXmlElement(xmlDoc, tradeListElement, "use_category", tradeList.UseCategory.ToString());

        // 添加商品列表
        if (tradeList.GoodsList != null && tradeList.GoodsList.Count > 0)
        {
            var goodsListXmlElement = xmlDoc.CreateElement("goods_list");
            
            foreach (var goods in tradeList.GoodsList)
            {
                if (!string.IsNullOrEmpty(goods.Item))
                {
                    var dataElement = xmlDoc.CreateElement("data");
                    AddXmlElement(xmlDoc, dataElement, "item", goods.Item);
                    goodsListXmlElement.AppendChild(dataElement);
                }
            }
            
            tradeListElement.AppendChild(goodsListXmlElement);
        }

        return tradeListElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AddXmlElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value ?? string.Empty;
        parentElement.AppendChild(element);
    }

    /// <summary>
    /// 获取NPC交易列表统计信息
    /// </summary>
    public NpcTradeInListStatistics GetStatistics(List<ClientNpcTradeInList> tradeLists)
    {
        var stats = new NpcTradeInListStatistics
        {
            TotalTradeLists = tradeLists.Count
        };

        // 统计交易类型
        foreach (var tradeList in tradeLists)
        {
            var tradeType = tradeList.TradeType;
            if (stats.TradeTypes.ContainsKey(tradeType))
                stats.TradeTypes[tradeType]++;
            else
                stats.TradeTypes[tradeType] = 1;

            var weaponType = tradeList.WeaponType;
            if (stats.WeaponTypes.ContainsKey(weaponType))
                stats.WeaponTypes[weaponType]++;
            else
                stats.WeaponTypes[weaponType] = 1;

            var armorType = tradeList.ArmorType;
            if (stats.ArmorTypes.ContainsKey(armorType))
                stats.ArmorTypes[armorType]++;
            else
                stats.ArmorTypes[armorType] = 1;

            var factionType = tradeList.FactionType;
            if (stats.FactionTypes.ContainsKey(factionType))
                stats.FactionTypes[factionType]++;
            else
                stats.FactionTypes[factionType] = 1;

            var levelType = tradeList.LevelType;
            if (stats.LevelTypes.ContainsKey(levelType))
                stats.LevelTypes[levelType]++;
            else
                stats.LevelTypes[levelType] = 1;

            var useCategory = tradeList.UseCategory;
            if (stats.UseCategoryDistribution.ContainsKey(useCategory))
                stats.UseCategoryDistribution[useCategory]++;
            else
                stats.UseCategoryDistribution[useCategory] = 1;
        }

        // 统计商品信息
        var allItemCounts = tradeLists.Select(t => t.ItemCount).ToList();
        stats.TotalItems = allItemCounts.Sum();
        stats.AverageItemsPerList = allItemCounts.Count > 0 ? allItemCounts.Average() : 0;
        stats.MaxItemsInList = allItemCounts.Count > 0 ? allItemCounts.Max() : 0;
        stats.MinItemsInList = allItemCounts.Count > 0 ? allItemCounts.Min() : 0;

        // 统计物品类型
        foreach (var tradeList in tradeLists)
        {
            var itemTypeStats = tradeList.GetItemTypeStatistics();
            foreach (var itemType in itemTypeStats)
            {
                if (stats.ItemTypes.ContainsKey(itemType.Key))
                    stats.ItemTypes[itemType.Key] += itemType.Value;
                else
                    stats.ItemTypes[itemType.Key] = itemType.Value;
            }
        }

        return stats;
    }

    /// <summary>
    /// 筛选交易列表
    /// </summary>
    public List<ClientNpcTradeInList> FilterTradeLists(List<ClientNpcTradeInList> tradeLists, TradeInListFilterCriteria criteria)
    {
        var filtered = tradeLists.AsEnumerable();

        if (!string.IsNullOrEmpty(criteria.SearchText))
        {
            var searchText = criteria.SearchText.ToLower();
            filtered = filtered.Where(t => 
                t.Id.ToString().Contains(searchText) ||
                t.Name.ToLower().Contains(searchText) ||
                t.Desc.ToLower().Contains(searchText) ||
                t.DisplayName.ToLower().Contains(searchText));
        }

        if (!string.IsNullOrEmpty(criteria.TradeType))
        {
            filtered = filtered.Where(t => t.TradeType == criteria.TradeType);
        }

        if (!string.IsNullOrEmpty(criteria.WeaponType))
        {
            filtered = filtered.Where(t => t.WeaponType == criteria.WeaponType);
        }

        if (!string.IsNullOrEmpty(criteria.ArmorType))
        {
            filtered = filtered.Where(t => t.ArmorType == criteria.ArmorType);
        }

        if (!string.IsNullOrEmpty(criteria.FactionType))
        {
            filtered = filtered.Where(t => t.FactionType == criteria.FactionType);
        }

        if (!string.IsNullOrEmpty(criteria.LevelType))
        {
            filtered = filtered.Where(t => t.LevelType == criteria.LevelType);
        }

        if (criteria.UseCategory.HasValue)
        {
            filtered = filtered.Where(t => t.UseCategory == criteria.UseCategory.Value);
        }

        if (criteria.MinItems.HasValue)
        {
            filtered = filtered.Where(t => t.ItemCount >= criteria.MinItems.Value);
        }

        if (criteria.MaxItems.HasValue)
        {
            filtered = filtered.Where(t => t.ItemCount <= criteria.MaxItems.Value);
        }

        return filtered.ToList();
    }
}
