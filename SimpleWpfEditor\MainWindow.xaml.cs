using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Xml;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor;

public partial class MainWindow : Window
{
    private readonly XmlService _xmlService;
    private readonly StringService _stringService;
    private readonly TitleXmlService _titleXmlService;
    private readonly SkillXmlService _skillXmlService;
    private readonly NpcGoodsListXmlService _npcShopXmlService;
    private readonly NpcTradeInListXmlService _npcTradeXmlService;
    private readonly ItemStringLinkService _linkService;
    private readonly ObservableCollection<ClientItem> _items;
    private readonly ICollectionView _filteredItems;
    private ClientItem? _currentItem;
    private string? _currentFilePath;
    private string _currentFileType = "物品";
    private bool _stringsLoaded = false;
    private bool _linksBuilt = false;

    public MainWindow()
    {
        InitializeComponent();

        // 创建服务
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
        var xmlLogger = loggerFactory.CreateLogger<XmlService>();
        var stringLogger = loggerFactory.CreateLogger<StringService>();
        var linkLogger = loggerFactory.CreateLogger<ItemStringLinkService>();
        var titleLogger = loggerFactory.CreateLogger<TitleXmlService>();
        var skillLogger = loggerFactory.CreateLogger<SkillXmlService>();
        var npcShopLogger = loggerFactory.CreateLogger<NpcGoodsListXmlService>();
        var npcTradeLogger = loggerFactory.CreateLogger<NpcTradeInListXmlService>();

        _xmlService = new XmlService(xmlLogger);
        _stringService = new StringService(stringLogger);
        _titleXmlService = new TitleXmlService(titleLogger);
        _skillXmlService = new SkillXmlService(skillLogger);
        _npcShopXmlService = new NpcGoodsListXmlService(npcShopLogger);
        _npcTradeXmlService = new NpcTradeInListXmlService(npcTradeLogger);
        _linkService = new ItemStringLinkService(_stringService, _xmlService, linkLogger);

        // 初始化数据
        _items = new ObservableCollection<ClientItem>();
        _filteredItems = CollectionViewSource.GetDefaultView(_items);
        _filteredItems.Filter = FilterItems;

        ItemsDataGrid.ItemsSource = _filteredItems;

        // 异步加载字符串和建立关联
        _ = InitializeAsync();

        UpdateStatus("就绪");
    }

    private async Task InitializeAsync()
    {
        try
        {
            // 第一步：加载字符串文件
            UpdateStatus("正在加载字符串文件...");
            await _stringService.LoadStringsAsync();
            _stringsLoaded = true;

            var stringStats = _stringService.GetStatistics();
            UpdateStatus($"字符串加载完成 - 总计: {stringStats.TotalStrings} 个");

            // 第二步：加载所有物品文件并建立关联
            UpdateStatus("正在加载物品文件并建立关联...");
            await _linkService.LoadAllItemsAndBuildLinksAsync();
            _linksBuilt = true;

            var linkStats = _linkService.GetStatistics();
            UpdateStatus($"关联建立完成 - 总物品: {linkStats.TotalItems}, 已关联: {linkStats.LinkedItems}, 未关联: {linkStats.UnlinkedItems}");

            // 刷新显示以应用本地化文本
            _filteredItems.Refresh();
        }
        catch (Exception ex)
        {
            UpdateStatus($"初始化失败: {ex.Message}");
        }
    }

    private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new OpenFileDialog
        {
            Title = "选择XML文件",
            Filter = "所有支持的文件|*.xml|物品文件|client_items_*.xml|NPC文件|client_npcs.xml|技能文件|client_skills.xml|所有文件|*.*",
            FilterIndex = 1,
            InitialDirectory = Path.Combine(Environment.CurrentDirectory, "客户端")
        };

        if (dialog.ShowDialog() == true)
        {
            try
            {
                UpdateStatus("正在加载文件...");

                var items = await _xmlService.LoadItemsAsync(dialog.FileName);

                _items.Clear();
                foreach (var item in items)
                {
                    _items.Add(item);
                }

                _currentFilePath = dialog.FileName;

                // 根据文件名判断类型
                var fileName = Path.GetFileName(dialog.FileName).ToLowerInvariant();
                _currentFileType = fileName.Contains("npc") ? "NPC" :
                                  fileName.Contains("skill") ? "技能" : "物品";

                FileNameText.Text = $"当前文件: {Path.GetFileName(dialog.FileName)}";
                ItemCountText.Text = $"{_currentFileType}数量: {_items.Count}";

                // 更新界面标题
                Title = $"Aion数据编辑器 - {Path.GetFileName(dialog.FileName)} ({_currentFileType})";

                UpdateStatus($"成功加载 {_items.Count} 个{_currentFileType}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("加载文件失败");
            }
        }
    }

    private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_items.Count == 0)
        {
            MessageBox.Show("没有要保存的数据", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        string filePath;
        if (string.IsNullOrEmpty(_currentFilePath))
        {
            var dialog = new SaveFileDialog
            {
                Title = "保存XML文件",
                Filter = "XML文件|*.xml|所有文件|*.*",
                FilterIndex = 1,
                FileName = "items.xml"
            };

            if (dialog.ShowDialog() != true)
                return;
                
            filePath = dialog.FileName;
        }
        else
        {
            filePath = _currentFilePath;
        }

        try
        {
            UpdateStatus("正在保存文件...");
            
            await _xmlService.SaveItemsAsync(filePath, _items.ToList());
            
            _currentFilePath = filePath;
            FileNameText.Text = $"当前文件: {Path.GetFileName(filePath)}";
            
            UpdateStatus("文件保存成功");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("保存文件失败");
        }
    }

    private async void SaveWithServerConversionButton_Click(object sender, RoutedEventArgs e)
    {
        if (_items.Count == 0)
        {
            MessageBox.Show("没有物品数据需要转换", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        try
        {
            // 选择服务端文件保存目录
            var folderDialog = new OpenFileDialog
            {
                Title = "选择服务端文件保存目录",
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "选择文件夹",
                Filter = "文件夹|*.folder"
            };

            if (folderDialog.ShowDialog() == true)
            {
                var serverBasePath = Path.GetDirectoryName(folderDialog.FileName) ?? "";

                UpdateStatus("正在转换物品数据...");

                // 确定客户端文件路径
                string clientFilePath;
                if (string.IsNullOrEmpty(_currentFilePath))
                {
                    var dialog = new SaveFileDialog
                    {
                        Title = "保存客户端XML文件",
                        Filter = "XML文件|*.xml|所有文件|*.*",
                        FilterIndex = 1,
                        FileName = "client_items.xml"
                    };

                    if (dialog.ShowDialog() != true)
                        return;

                    clientFilePath = dialog.FileName;
                }
                else
                {
                    clientFilePath = _currentFilePath;
                }

                try
                {
                    // 只获取修改过的物品
                    var modifiedItems = _items.Where(item => item.IsModified).ToList();

                    if (modifiedItems.Count == 0)
                    {
                        MessageBox.Show("没有修改过的物品需要转换！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        UpdateStatus("没有修改过的物品");
                        return;
                    }

                    // 显示修改过的物品列表，让用户确认
                    var modifiedItemsList = string.Join("\n", modifiedItems.Select(item => $"• {item.Id} - {item.Name}"));
                    var confirmMessage = $"发现 {modifiedItems.Count} 个修改过的物品:\n\n{modifiedItemsList}\n\n是否继续转换这些物品到服务端格式？\n\n注意：这不会修改您的客户端文件，只会生成服务端文件。";

                    var confirmResult = MessageBox.Show(confirmMessage, "确认转换", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (confirmResult != MessageBoxResult.Yes)
                    {
                        UpdateStatus("用户取消转换");
                        return;
                    }

                    // 执行转换 - 只保存修改过的物品
                    var result = await _xmlService.SaveItemsWithServerConversionAsync(clientFilePath, modifiedItems, serverBasePath);

                    if (result.Success)
                    {
                        var message = $"修改过的物品转换完成！\n\n" +
                                    $"修改过的物品总数: {result.TotalCount}\n" +
                                    $"武器: {result.WeaponCount} 个 → item_weapons.xml\n" +
                                    $"防具: {result.ArmorCount} 个 → item_armors.xml\n" +
                                    $"杂货: {result.EtcCount} 个 → item_etc.xml\n\n" +
                                    $"文件保存位置: {serverBasePath}\n\n" +
                                    $"只保存了修改过的物品，按ID自动排序并分类保存到对应的服务端文件中。\n\n" +
                                    $"修改过的物品ID列表:\n" +
                                    string.Join(", ", modifiedItems.Select(item => $"{item.Id}({item.Name})"));

                        MessageBox.Show(message, "转换成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        UpdateStatus("服务端转换完成");

                        _currentFilePath = clientFilePath;
                        FileNameText.Text = $"当前文件: {Path.GetFileName(clientFilePath)}";
                    }
                    else
                    {
                        MessageBox.Show($"转换失败：{result.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        UpdateStatus("服务端转换失败");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"转换过程中出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    UpdateStatus("服务端转换失败");
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"转换文件时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("服务端转换失败");
        }
    }

    /// <summary>
    /// 清除修改标记按钮点击事件
    /// </summary>
    private void ClearModifiedButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var modifiedItems = _items.Where(item => item.IsModified).ToList();

            if (modifiedItems.Count == 0)
            {
                MessageBox.Show("没有物品被标记为已修改！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 显示修改过的物品列表，让用户确认
            var modifiedItemsList = string.Join("\n", modifiedItems.Select(item => $"• {item.Id} - {item.Name}"));
            var confirmMessage = $"发现 {modifiedItems.Count} 个修改过的物品:\n\n{modifiedItemsList}\n\n是否清除这些物品的修改标记？\n\n注意：清除后这些物品将不会被包含在服务端转换中。";

            var confirmResult = MessageBox.Show(confirmMessage, "确认清除修改标记", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (confirmResult == MessageBoxResult.Yes)
            {
                // 清除所有修改标记
                foreach (var item in modifiedItems)
                {
                    item.IsModified = false;
                }

                MessageBox.Show($"已清除 {modifiedItems.Count} 个物品的修改标记！", "清除完成", MessageBoxButton.OK, MessageBoxImage.Information);
                UpdateStatus($"已清除 {modifiedItems.Count} 个物品的修改标记");
            }
            else
            {
                UpdateStatus("用户取消清除修改标记");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"清除修改标记时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("清除修改标记失败");
        }
    }

    private void AddItemButton_Click(object sender, RoutedEventArgs e)
    {
        var newId = _items.Count > 0 ? _items.Max(i => i.Id) + 1 : 1;
        var newItem = new ClientItem
        {
            Id = newId,
            Name = $"new_item_{newId}",
            Desc = $"STR_NEW_ITEM_{newId}",
            ArmorType = "clothes",
            ItemType = "normal",
            Price = 100,
            MaxStackCount = 1
        };

        _items.Add(newItem);
        ItemsDataGrid.SelectedItem = newItem;
        ItemCountText.Text = $"物品数量: {_items.Count}";

        UpdateStatus($"新增物品: {newItem.DisplayName}");
    }

    private void DeleteItemButton_Click(object sender, RoutedEventArgs e)
    {
        if (ItemsDataGrid.SelectedItem is not ClientItem selectedItem)
        {
            MessageBox.Show("请先选择要删除的物品", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var result = MessageBox.Show(
            $"确定要删除物品 '{selectedItem.DisplayName}' 吗？\n\n此操作无法撤销！",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning,
            MessageBoxResult.No);

        if (result == MessageBoxResult.Yes)
        {
            var deletedItemName = selectedItem.DisplayName;
            _items.Remove(selectedItem);
            ItemCountText.Text = $"物品数量: {_items.Count}";

            // 清空编辑区域
            if (_currentItem == selectedItem)
            {
                _currentItem = null;
                EditPanel.Visibility = Visibility.Collapsed;
                EditTitleText.Text = "选择一个物品进行编辑";
            }

            UpdateStatus($"已删除物品: {deletedItemName}");
        }
    }

    private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        _filteredItems.Refresh();
    }

    private void ItemsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (ItemsDataGrid.SelectedItem is ClientItem item)
        {
            _currentItem = item;
            LoadItemToEditor(item);
            EditPanel.Visibility = Visibility.Visible;
            EditTitleText.Text = $"编辑物品: {item.DisplayName}";
        }
        else
        {
            _currentItem = null;
            EditPanel.Visibility = Visibility.Collapsed;
            EditTitleText.Text = "选择一个物品进行编辑";
        }
    }

    private void LoadItemToEditor(ClientItem item)
    {
        // 更新本地化信息显示
        if (_linksBuilt)
        {
            // 使用智能关联服务获取本地化名称
            var localizedName = _linkService.GetLocalizedNameById(item.Id);
            LocalizedNameText.Text = !string.IsNullOrEmpty(localizedName)
                ? localizedName
                : "未找到本地化文本";

            // 显示关联信息
            var descKey = _linkService.GetDescKeyById(item.Id);
            DescKeyText.Text = !string.IsNullOrEmpty(descKey) ? descKey : item.Desc;
        }
        else if (_stringsLoaded)
        {
            var localizedName = item.GetLocalizedDescription(_stringService);
            LocalizedNameText.Text = !string.IsNullOrEmpty(localizedName) && localizedName != item.Desc
                ? localizedName
                : "未找到本地化文本";
            DescKeyText.Text = item.Desc;
        }
        else
        {
            LocalizedNameText.Text = "正在建立关联...";
            DescKeyText.Text = item.Desc;
        }

        // 更新基础信息
        IdTextBox.Text = item.Id.ToString();
        NameTextBox.Text = item.Name;
        DescTextBox.Text = item.Desc;
        ArmorTypeComboBox.Text = item.ArmorType;
        PriceTextBox.Text = item.Price.ToString();
        MaxStackCountTextBox.Text = item.MaxStackCount.ToString();

        // 职业权限
        WarriorCheckBox.IsChecked = item.Warrior == 1;
        ScoutCheckBox.IsChecked = item.Scout == 1;
        MageCheckBox.IsChecked = item.Mage == 1;
        ClericCheckBox.IsChecked = item.Cleric == 1;
        FighterCheckBox.IsChecked = item.Fighter == 1;
        KnightCheckBox.IsChecked = item.Knight == 1;
        AssassinCheckBox.IsChecked = item.Assassin == 1;
        RangerCheckBox.IsChecked = item.Ranger == 1;
        WizardCheckBox.IsChecked = item.Wizard == 1;
        ElementalistCheckBox.IsChecked = item.Elementalist == 1;
        ChanterCheckBox.IsChecked = item.Chanter == 1;
        PriestCheckBox.IsChecked = item.Priest == 1;
        GunnerCheckBox.IsChecked = item.Gunner == 1;
        BardCheckBox.IsChecked = item.Bard == 1;
        RiderCheckBox.IsChecked = item.Rider == 1;
    }

    private void SaveChangesButton_Click(object sender, RoutedEventArgs e)
    {
        if (_currentItem == null) return;

        try
        {
            // 保存基础信息
            if (int.TryParse(IdTextBox.Text, out var id))
                _currentItem.Id = id;
            
            _currentItem.Name = NameTextBox.Text;
            _currentItem.Desc = DescTextBox.Text;
            _currentItem.ArmorType = ArmorTypeComboBox.Text;
            
            if (int.TryParse(PriceTextBox.Text, out var price))
                _currentItem.Price = price;
                
            if (int.TryParse(MaxStackCountTextBox.Text, out var maxStack))
                _currentItem.MaxStackCount = maxStack;

            // 保存职业权限
            _currentItem.Warrior = WarriorCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Scout = ScoutCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Mage = MageCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Cleric = ClericCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Fighter = FighterCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Knight = KnightCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Assassin = AssassinCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Ranger = RangerCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Wizard = WizardCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Elementalist = ElementalistCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Chanter = ChanterCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Priest = PriestCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Gunner = GunnerCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Bard = BardCheckBox.IsChecked == true ? 1 : 0;
            _currentItem.Rider = RiderCheckBox.IsChecked == true ? 1 : 0;

            // 验证数据
            var (isValid, errors) = _xmlService.ValidateItem(_currentItem);
            if (!isValid)
            {
                var errorMessage = string.Join("\n", errors);
                MessageBox.Show($"数据验证失败:\n{errorMessage}", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            _filteredItems.Refresh();
            UpdateStatus($"保存物品更改: {_currentItem.DisplayName}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存更改时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ResetButton_Click(object sender, RoutedEventArgs e)
    {
        if (_currentItem != null)
        {
            LoadItemToEditor(_currentItem);
            UpdateStatus("已重置更改");
        }
    }

    private void AllowAllClassesButton_Click(object sender, RoutedEventArgs e)
    {
        SetAllClassPermissions(true);
    }

    private void DenyAllClassesButton_Click(object sender, RoutedEventArgs e)
    {
        SetAllClassPermissions(false);
    }

    private void SetAllClassPermissions(bool allowed)
    {
        WarriorCheckBox.IsChecked = allowed;
        ScoutCheckBox.IsChecked = allowed;
        MageCheckBox.IsChecked = allowed;
        ClericCheckBox.IsChecked = allowed;
        FighterCheckBox.IsChecked = allowed;
        KnightCheckBox.IsChecked = allowed;
        AssassinCheckBox.IsChecked = allowed;
        RangerCheckBox.IsChecked = allowed;
        WizardCheckBox.IsChecked = allowed;
        ElementalistCheckBox.IsChecked = allowed;
        ChanterCheckBox.IsChecked = allowed;
        PriestCheckBox.IsChecked = allowed;
        GunnerCheckBox.IsChecked = allowed;
        BardCheckBox.IsChecked = allowed;
        RiderCheckBox.IsChecked = allowed;
    }

    private bool FilterItems(object obj)
    {
        if (obj is not ClientItem item) return false;
        if (string.IsNullOrWhiteSpace(SearchTextBox.Text)) return true;

        var searchText = SearchTextBox.Text.ToLowerInvariant();
        return item.Id.ToString().Contains(searchText) ||
               item.Name.ToLowerInvariant().Contains(searchText) ||
               item.Desc.ToLowerInvariant().Contains(searchText) ||
               item.ArmorType.ToLowerInvariant().Contains(searchText);
    }



    private void GlobalSearchButton_Click(object sender, RoutedEventArgs e)
    {
        if (!_linksBuilt)
        {
            MessageBox.Show("物品关联尚未建立完成", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var globalSearchWindow = new GlobalSearchWindow(_linkService);
        globalSearchWindow.Owner = this;
        var result = globalSearchWindow.ShowDialog();

        if (result == true && globalSearchWindow.SelectedItem != null)
        {
            // 用户选择了一个物品，加载到编辑器
            var selectedItem = globalSearchWindow.SelectedItem;

            // 清空当前列表并添加选中的物品
            _items.Clear();
            _items.Add(selectedItem);

            // 选中该物品
            ItemsDataGrid.SelectedItem = selectedItem;

            UpdateStatus($"已加载物品: {selectedItem.DisplayName}");
        }
    }



    #region 右键菜单事件处理

    private void EditItemMenuItem_Click(object sender, RoutedEventArgs e)
    {
        if (ItemsDataGrid.SelectedItem is ClientItem selectedItem)
        {
            // 物品已经在选择时自动加载到编辑器，这里只需要确保编辑面板可见
            if (EditPanel.Visibility != Visibility.Visible)
            {
                EditPanel.Visibility = Visibility.Visible;
                EditTitleText.Text = $"编辑物品: {selectedItem.DisplayName}";
            }
            UpdateStatus($"正在编辑: {selectedItem.DisplayName}");
        }
    }

    private void CopyItemMenuItem_Click(object sender, RoutedEventArgs e)
    {
        if (ItemsDataGrid.SelectedItem is not ClientItem selectedItem)
            return;

        try
        {
            var newId = _items.Count > 0 ? _items.Max(i => i.Id) + 1 : 1;
            var copiedItem = new ClientItem
            {
                // 复制所有基础属性
                Id = newId,
                Name = $"{selectedItem.Name}_copy",
                Desc = selectedItem.Desc,
                ArmorType = selectedItem.ArmorType,
                ItemType = selectedItem.ItemType,
                Material = selectedItem.Material,
                IconName = selectedItem.IconName,
                Price = selectedItem.Price,
                MaxStackCount = selectedItem.MaxStackCount,
                CanSellToNpc = selectedItem.CanSellToNpc,
                CanDepositToCharacterWarehouse = selectedItem.CanDepositToCharacterWarehouse,
                CanDepositToAccountWarehouse = selectedItem.CanDepositToAccountWarehouse,
                CanDepositToGuildWarehouse = selectedItem.CanDepositToGuildWarehouse,
                EquipmentSlots = selectedItem.EquipmentSlots,
                Breakable = selectedItem.Breakable,
                SoulBind = selectedItem.SoulBind,
                RemoveWhenLogout = selectedItem.RemoveWhenLogout,
                GenderPermitted = selectedItem.GenderPermitted,

                // 复制职业权限
                Warrior = selectedItem.Warrior,
                Scout = selectedItem.Scout,
                Mage = selectedItem.Mage,
                Cleric = selectedItem.Cleric,
                Engineer = selectedItem.Engineer,
                Artist = selectedItem.Artist,
                Fighter = selectedItem.Fighter,
                Knight = selectedItem.Knight,
                Assassin = selectedItem.Assassin,
                Ranger = selectedItem.Ranger,
                Wizard = selectedItem.Wizard,
                Elementalist = selectedItem.Elementalist,
                Chanter = selectedItem.Chanter,
                Priest = selectedItem.Priest,
                Gunner = selectedItem.Gunner,
                Bard = selectedItem.Bard,
                Rider = selectedItem.Rider,

                // 复制强化属性
                OptionSlotValue = selectedItem.OptionSlotValue,
                SpecialSlotValue = selectedItem.SpecialSlotValue,
                OptionSlotBonus = selectedItem.OptionSlotBonus,
                BonusApply = selectedItem.BonusApply,
                NoEnchant = selectedItem.NoEnchant,
                MaxEnchantValue = selectedItem.MaxEnchantValue,
                CanProcEnchant = selectedItem.CanProcEnchant,
                CanCompositeWeapon = selectedItem.CanCompositeWeapon,

                // 复制其他属性
                CannotChangeskin = selectedItem.CannotChangeskin,
                UiSoundType = selectedItem.UiSoundType,
                Mesh = selectedItem.Mesh,
                MeshChange = selectedItem.MeshChange,
                CanDye = selectedItem.CanDye,
                CashItem = selectedItem.CashItem,
                CanSplit = selectedItem.CanSplit,
                ItemDropPermitted = selectedItem.ItemDropPermitted,
                BmRestrictCategory = selectedItem.BmRestrictCategory,
                CanApExtraction = selectedItem.CanApExtraction,
                CanPolish = selectedItem.CanPolish,
                PolishBurnOnAttack = selectedItem.PolishBurnOnAttack,
                PolishBurnOnDefend = selectedItem.PolishBurnOnDefend,
                RacePermitted = selectedItem.RacePermitted
            };

            _items.Add(copiedItem);
            ItemsDataGrid.SelectedItem = copiedItem;
            ItemCountText.Text = $"物品数量: {_items.Count}";

            UpdateStatus($"复制物品: {selectedItem.DisplayName} -> {copiedItem.DisplayName}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"复制物品时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("复制物品失败");
        }
    }

    private void DeleteItemMenuItem_Click(object sender, RoutedEventArgs e)
    {
        // 调用删除按钮的相同逻辑
        DeleteItemButton_Click(sender, e);
    }

    #endregion

    private void UpdateStatus(string message)
    {
        StatusText.Text = message;
    }

    private void ItemsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (ItemsDataGrid.SelectedItem is ClientItem selectedItem)
        {
            OpenItemEditor(selectedItem);
        }
    }

    private async void OpenItemEditor(ClientItem item)
    {
        try
        {
            var itemEditor = new ItemEditorWindow(item, _stringService);
            itemEditor.Owner = this;

            var result = itemEditor.ShowDialog();

            if (result == true && itemEditor.DataChanged)
            {
                // 更新原始数据
                UpdateItemData(item, itemEditor.EditedItem);

                // 保存到XML文件
                if (!string.IsNullOrEmpty(_currentFilePath))
                {
                    try
                    {
                        await SaveItemsToXmlFileAsync(_currentFilePath);
                        UpdateStatus($"已保存物品: {item.DisplayName}");
                    }
                    catch (Exception saveEx)
                    {
                        MessageBox.Show($"保存文件时出错: {saveEx.Message}", "保存错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        UpdateStatus($"保存失败: {item.DisplayName}");
                    }
                }
                else
                {
                    UpdateStatus($"已更新物品: {item.DisplayName} (仅内存)");
                }

                // 刷新显示
                _filteredItems.Refresh();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开物品编辑器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateItemData(ClientItem original, ClientItem edited)
    {
        // 更新原始物品的所有属性
        original.Name = edited.Name;
        original.Desc = edited.Desc;
        original.ArmorType = edited.ArmorType;
        original.ItemType = edited.ItemType;
        original.Material = edited.Material;
        original.IconName = edited.IconName;
        original.Price = edited.Price;
        original.MaxStackCount = edited.MaxStackCount;
        original.EquipmentSlots = edited.EquipmentSlots;
        original.Dodge = edited.Dodge;
        original.Quality = edited.Quality;
        original.Level = edited.Level;
        original.MagicalResist = edited.MagicalResist;
        original.PhysicalDefend = edited.PhysicalDefend;
        original.Lore = edited.Lore;
        original.CanExchange = edited.CanExchange;
        original.CanSellToNpc = edited.CanSellToNpc;
        original.CanDepositToCharacterWarehouse = edited.CanDepositToCharacterWarehouse;
        original.CanDepositToAccountWarehouse = edited.CanDepositToAccountWarehouse;
        original.CanDepositToGuildWarehouse = edited.CanDepositToGuildWarehouse;
        original.Breakable = edited.Breakable;
        original.SoulBind = edited.SoulBind;
        original.RemoveWhenLogout = edited.RemoveWhenLogout;
        original.GenderPermitted = edited.GenderPermitted;
        original.Warrior = edited.Warrior;
        original.Scout = edited.Scout;
        original.Mage = edited.Mage;
        original.Cleric = edited.Cleric;
        original.Engineer = edited.Engineer;
        original.Artist = edited.Artist;
        original.Fighter = edited.Fighter;
        original.Knight = edited.Knight;
        original.Assassin = edited.Assassin;
        original.Ranger = edited.Ranger;
        original.Wizard = edited.Wizard;
        original.Elementalist = edited.Elementalist;
        original.Chanter = edited.Chanter;
        original.Priest = edited.Priest;
        original.Gunner = edited.Gunner;
        original.Bard = edited.Bard;
        original.Rider = edited.Rider;
        original.OptionSlotValue = edited.OptionSlotValue;
        original.SpecialSlotValue = edited.SpecialSlotValue;
        original.OptionSlotBonus = edited.OptionSlotBonus;
        original.BonusApply = edited.BonusApply;
        original.NoEnchant = edited.NoEnchant;
        original.MaxEnchantValue = edited.MaxEnchantValue;
        original.CannotChangeskin = edited.CannotChangeskin;
        original.UiSoundType = edited.UiSoundType;
        original.CashItem = edited.CashItem;
        original.CanSplit = edited.CanSplit;
        original.ItemDropPermitted = edited.ItemDropPermitted;
        original.CanApExtraction = edited.CanApExtraction;
        original.Mesh = edited.Mesh;
        original.MeshChange = edited.MeshChange;
        original.CanDye = edited.CanDye;
        original.RacePermitted = edited.RacePermitted;

        // 武器专用属性
        original.WeaponType = edited.WeaponType;
        original.DmgDecal = edited.DmgDecal;
        original.EquipBone = edited.EquipBone;
        original.CombatEquipBone = edited.CombatEquipBone;

        // 基础奖励属性
        original.BonusAttr1 = edited.BonusAttr1;
        original.BonusAttr2 = edited.BonusAttr2;
        original.BonusAttr3 = edited.BonusAttr3;
        original.BonusAttr4 = edited.BonusAttr4;
        original.BonusAttr5 = edited.BonusAttr5;
        original.BonusAttr6 = edited.BonusAttr6;
        original.BonusAttr7 = edited.BonusAttr7;
        original.BonusAttr8 = edited.BonusAttr8;

        // 高级奖励属性
        original.BonusAttrA1 = edited.BonusAttrA1;
        original.BonusAttrA2 = edited.BonusAttrA2;
        original.BonusAttrA3 = edited.BonusAttrA3;
        original.BonusAttrA4 = edited.BonusAttrA4;
        original.BonusAttrA5 = edited.BonusAttrA5;
        original.BonusAttrA6 = edited.BonusAttrA6;
        original.BonusAttrA7 = edited.BonusAttrA7;
        original.BonusAttrA8 = edited.BonusAttrA8;

        // 特殊效果属性
        original.BonusAttrB1 = edited.BonusAttrB1;
        original.BonusAttrB2 = edited.BonusAttrB2;
        original.BonusAttrB3 = edited.BonusAttrB3;
        original.BonusAttrB4 = edited.BonusAttrB4;

        // 战斗和强化属性
        original.BurnOnAttack = edited.BurnOnAttack;
        original.BurnOnDefend = edited.BurnOnDefend;
        original.PolishBurnOnAttack = edited.PolishBurnOnAttack;
        original.PolishBurnOnDefend = edited.PolishBurnOnDefend;
        original.CanProcEnchant = edited.CanProcEnchant;
        original.CanCompositeWeapon = edited.CanCompositeWeapon;
        original.CanPolish = edited.CanPolish;
        original.BmRestrictCategory = edited.BmRestrictCategory;
        original.DisassemblyItem = edited.DisassemblyItem;

        // 高级属性
        original.DescLong = edited.DescLong;
        original.DisposableTradeItem = edited.DisposableTradeItem;
        original.DisposableTradeItemCount = edited.DisposableTradeItemCount;
        original.CanPackCount = edited.CanPackCount;
        original.VisualSlot = edited.VisualSlot;
        original.DefaultColorM = edited.DefaultColorM;
        original.DefaultColorF = edited.DefaultColorF;

        // 标记物品已被修改
        original.IsModified = true;
    }

    /// <summary>
    /// 保存物品数据到XML文件
    /// </summary>
    private async Task SaveItemsToXmlFileAsync(string filePath)
    {
        try
        {
            UpdateStatus("正在保存文件...");

            // 创建XML文档
            var xmlDoc = new System.Xml.XmlDocument();

            // 创建根节点
            var rootElement = xmlDoc.CreateElement("client_items");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有物品
            foreach (var item in _items)
            {
                var itemElement = CreateItemXmlElement(xmlDoc, item);
                rootElement.AppendChild(itemElement);
            }

            // 保存到文件
            var settings = new System.Xml.XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = System.Text.Encoding.UTF8
            };

            using var writer = System.Xml.XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            UpdateStatus($"成功保存 {_items.Count} 个物品到文件");
        }
        catch (Exception ex)
        {
            UpdateStatus($"保存文件时出错: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 创建物品的XML元素
    /// </summary>
    private System.Xml.XmlElement CreateItemXmlElement(System.Xml.XmlDocument xmlDoc, ClientItem item)
    {
        var itemElement = xmlDoc.CreateElement("client_item");

        // 添加所有属性
        AddXmlElement(xmlDoc, itemElement, "id", item.Id.ToString());
        AddXmlElement(xmlDoc, itemElement, "name", item.Name);
        AddXmlElement(xmlDoc, itemElement, "desc", item.Desc);
        AddXmlElement(xmlDoc, itemElement, "armor_type", item.ArmorType);
        AddXmlElement(xmlDoc, itemElement, "item_type", item.ItemType);
        AddXmlElement(xmlDoc, itemElement, "material", item.Material);
        AddXmlElement(xmlDoc, itemElement, "icon_name", item.IconName);
        AddXmlElement(xmlDoc, itemElement, "price", item.Price.ToString());
        AddXmlElement(xmlDoc, itemElement, "max_stack_count", item.MaxStackCount.ToString());
        AddXmlElement(xmlDoc, itemElement, "equipment_slots", item.EquipmentSlots);
        AddXmlElement(xmlDoc, itemElement, "dodge", item.Dodge.ToString());
        AddXmlElement(xmlDoc, itemElement, "quality", item.Quality);
        AddXmlElement(xmlDoc, itemElement, "level", item.Level.ToString());
        AddXmlElement(xmlDoc, itemElement, "magical_resist", item.MagicalResist.ToString());
        AddXmlElement(xmlDoc, itemElement, "physical_defend", item.PhysicalDefend.ToString());
        AddXmlElement(xmlDoc, itemElement, "lore", item.Lore ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_exchange", item.CanExchange ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_sell_to_npc", item.CanSellToNpc ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_deposit_to_character_warehouse", item.CanDepositToCharacterWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_deposit_to_account_warehouse", item.CanDepositToAccountWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_deposit_to_guild_warehouse", item.CanDepositToGuildWarehouse ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "breakable", item.Breakable ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "soul_bind", item.SoulBind ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "remove_when_logout", item.RemoveWhenLogout ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "gender_permitted", item.GenderPermitted);

        // 职业权限
        AddXmlElement(xmlDoc, itemElement, "warrior", item.Warrior.ToString());
        AddXmlElement(xmlDoc, itemElement, "scout", item.Scout.ToString());
        AddXmlElement(xmlDoc, itemElement, "mage", item.Mage.ToString());
        AddXmlElement(xmlDoc, itemElement, "cleric", item.Cleric.ToString());
        AddXmlElement(xmlDoc, itemElement, "engineer", item.Engineer.ToString());
        AddXmlElement(xmlDoc, itemElement, "artist", item.Artist.ToString());
        AddXmlElement(xmlDoc, itemElement, "fighter", item.Fighter.ToString());
        AddXmlElement(xmlDoc, itemElement, "knight", item.Knight.ToString());
        AddXmlElement(xmlDoc, itemElement, "assassin", item.Assassin.ToString());
        AddXmlElement(xmlDoc, itemElement, "ranger", item.Ranger.ToString());
        AddXmlElement(xmlDoc, itemElement, "wizard", item.Wizard.ToString());
        AddXmlElement(xmlDoc, itemElement, "elementalist", item.Elementalist.ToString());
        AddXmlElement(xmlDoc, itemElement, "chanter", item.Chanter.ToString());
        AddXmlElement(xmlDoc, itemElement, "priest", item.Priest.ToString());
        AddXmlElement(xmlDoc, itemElement, "gunner", item.Gunner.ToString());
        AddXmlElement(xmlDoc, itemElement, "bard", item.Bard.ToString());
        AddXmlElement(xmlDoc, itemElement, "rider", item.Rider.ToString());

        // 高级属性
        AddXmlElement(xmlDoc, itemElement, "option_slot_value", item.OptionSlotValue.ToString());
        AddXmlElement(xmlDoc, itemElement, "special_slot_value", item.SpecialSlotValue.ToString());
        AddXmlElement(xmlDoc, itemElement, "option_slot_bonus", item.OptionSlotBonus.ToString());
        AddXmlElement(xmlDoc, itemElement, "bonus_apply", item.BonusApply);
        AddXmlElement(xmlDoc, itemElement, "no_enchant", item.NoEnchant ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "max_enchant_value", item.MaxEnchantValue.ToString());
        AddXmlElement(xmlDoc, itemElement, "cannot_changeskin", item.CannotChangeskin.ToString());
        AddXmlElement(xmlDoc, itemElement, "ui_sound_type", item.UiSoundType);
        AddXmlElement(xmlDoc, itemElement, "cash_item", item.CashItem.ToString());
        AddXmlElement(xmlDoc, itemElement, "can_split", item.CanSplit ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "item_drop_permitted", item.ItemDropPermitted ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_ap_extraction", item.CanApExtraction ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "mesh", item.Mesh);
        AddXmlElement(xmlDoc, itemElement, "mesh_change", item.MeshChange.ToString());
        AddXmlElement(xmlDoc, itemElement, "can_dye", item.CanDye.ToString());
        AddXmlElement(xmlDoc, itemElement, "race_permitted", item.RacePermitted);

        // 武器专用属性
        AddXmlElement(xmlDoc, itemElement, "weapon_type", item.WeaponType);
        AddXmlElement(xmlDoc, itemElement, "dmg_decal", item.DmgDecal.ToString());
        AddXmlElement(xmlDoc, itemElement, "equip_bone", item.EquipBone);
        AddXmlElement(xmlDoc, itemElement, "combat_equip_bone", item.CombatEquipBone);

        // 基础奖励属性
        AddXmlElement(xmlDoc, itemElement, "bonus_attr1", item.BonusAttr1);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr2", item.BonusAttr2);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr3", item.BonusAttr3);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr4", item.BonusAttr4);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr5", item.BonusAttr5);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr6", item.BonusAttr6);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr7", item.BonusAttr7);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr8", item.BonusAttr8);

        // 高级奖励属性
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a1", item.BonusAttrA1);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a2", item.BonusAttrA2);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a3", item.BonusAttrA3);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a4", item.BonusAttrA4);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a5", item.BonusAttrA5);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a6", item.BonusAttrA6);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a7", item.BonusAttrA7);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_a8", item.BonusAttrA8);

        // 特殊效果属性
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_b1", item.BonusAttrB1);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_b2", item.BonusAttrB2);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_b3", item.BonusAttrB3);
        AddXmlElement(xmlDoc, itemElement, "bonus_attr_b4", item.BonusAttrB4);

        // 战斗和强化属性
        AddXmlElement(xmlDoc, itemElement, "burn_on_attack", item.BurnOnAttack.ToString());
        AddXmlElement(xmlDoc, itemElement, "burn_on_defend", item.BurnOnDefend.ToString());
        AddXmlElement(xmlDoc, itemElement, "polish_burn_on_attack", item.PolishBurnOnAttack.ToString());
        AddXmlElement(xmlDoc, itemElement, "polish_burn_on_defend", item.PolishBurnOnDefend.ToString());
        AddXmlElement(xmlDoc, itemElement, "can_proc_enchant", item.CanProcEnchant ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_composite_weapon", item.CanCompositeWeapon ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "can_polish", item.CanPolish ? "TRUE" : "FALSE");
        AddXmlElement(xmlDoc, itemElement, "bm_restrict_category", item.BmRestrictCategory.ToString());
        AddXmlElement(xmlDoc, itemElement, "disassembly_item", item.DisassemblyItem.ToString());

        // 高级属性
        AddXmlElement(xmlDoc, itemElement, "desc_long", item.DescLong);
        AddXmlElement(xmlDoc, itemElement, "disposable_trade_item", item.DisposableTradeItem);
        AddXmlElement(xmlDoc, itemElement, "disposable_trade_item_count", item.DisposableTradeItemCount.ToString());
        AddXmlElement(xmlDoc, itemElement, "can_pack_count", item.CanPackCount.ToString());
        AddXmlElement(xmlDoc, itemElement, "visual_slot", item.VisualSlot);
        AddXmlElement(xmlDoc, itemElement, "default_color_m", item.DefaultColorM);
        AddXmlElement(xmlDoc, itemElement, "default_color_f", item.DefaultColorF);

        return itemElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AddXmlElement(System.Xml.XmlDocument xmlDoc, System.Xml.XmlElement parentElement, string elementName, string value)
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value ?? string.Empty;
        parentElement.AppendChild(element);
    }

    /// <summary>
    /// 称号管理器按钮点击事件
    /// </summary>
    private void TitleManagerButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
            var titleManagerLogger = loggerFactory.CreateLogger<TitleManagerWindow>();

            var titleManager = new TitleManagerWindow(titleManagerLogger, _titleXmlService, _stringService)
            {
                Owner = this
            };

            titleManager.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开称号管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 技能管理器按钮点击事件
    /// </summary>
    private void SkillManagerButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 显示选择对话框
            var result = MessageBox.Show(
                "请选择技能管理器类型：\n\n" +
                "是(Yes) - 专业技能管理器 (skill_base_client 格式)\n" +
                "否(No) - 简单技能管理器 (client_skill 格式)\n" +
                "取消(Cancel) - 取消操作",
                "选择技能管理器",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 打开专业技能管理器
                using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
                var professionalSkillManagerLogger = loggerFactory.CreateLogger<ProfessionalSkillManagerWindow>();
                var skillBaseXmlServiceLogger = loggerFactory.CreateLogger<SkillBaseXmlService>();
                var skillBaseXmlService = new SkillBaseXmlService(skillBaseXmlServiceLogger);

                var professionalSkillManager = new ProfessionalSkillManagerWindow(professionalSkillManagerLogger, skillBaseXmlService)
                {
                    Owner = this
                };
                professionalSkillManager.ShowDialog();
            }
            else if (result == MessageBoxResult.No)
            {
                // 打开简单技能管理器
                using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
                var skillManagerLogger = loggerFactory.CreateLogger<SkillManagerWindow>();

                var skillManager = new SkillManagerWindow(skillManagerLogger, _skillXmlService, _xmlService)
                {
                    Owner = this
                };
                skillManager.ShowDialog();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开技能管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// NPC管理器按钮点击事件
    /// </summary>
    private void NpcManagerButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var npcManager = new ProfessionalNpcManagerWindow(_items, _xmlService)
            {
                Owner = this
            };
            npcManager.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开NPC管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 服务端怪物掉落管理器按钮点击事件
    /// </summary>
    private void ServerMonsterButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var serverMonsterManager = new ServerMonsterDropManagerWindow
            {
                Owner = this
            };
            serverMonsterManager.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开服务端怪物掉落管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    /// <summary>
    /// NPC商店管理器按钮点击事件
    /// </summary>
    private void NpcShopManagerButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
            var npcShopManagerLogger = loggerFactory.CreateLogger<NpcShopManagerWindow>();

            var npcShopManager = new NpcShopManagerWindow(npcShopManagerLogger, _npcShopXmlService, _stringService)
            {
                Owner = this
            };

            npcShopManager.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开NPC商店管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// NPC交易管理器按钮点击事件
    /// </summary>
    private void NpcTradeManagerButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddDebug());
            var npcTradeManagerLogger = loggerFactory.CreateLogger<NpcTradeManagerWindow>();

            var npcTradeManager = new NpcTradeManagerWindow(npcTradeManagerLogger, _npcTradeXmlService, _stringService)
            {
                Owner = this
            };

            npcTradeManager.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开NPC交易管理器时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
