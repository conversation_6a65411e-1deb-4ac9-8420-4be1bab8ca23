﻿#pragma checksum "..\..\..\SkillEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6D079071492FF60DD4142F40AA627BD4D395D2CC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// SkillEditorWindow
    /// </summary>
    public partial class SkillEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IdTextBox;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SkillTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxLevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DpConsumeTextBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CastTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CoolTimeTextBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetRangeTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetMaxCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EffectTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EffectValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AnimationTextBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IconTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\SkillEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/skilleditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SkillEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.IdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.SkillTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.LevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.MaxLevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.MpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.HpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.DpConsumeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CastTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.CoolTimeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.DurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.TargetTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.TargetRangeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.TargetMaxCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.EffectTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.EffectValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.AnimationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.IconTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\SkillEditorWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 197 "..\..\..\SkillEditorWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

