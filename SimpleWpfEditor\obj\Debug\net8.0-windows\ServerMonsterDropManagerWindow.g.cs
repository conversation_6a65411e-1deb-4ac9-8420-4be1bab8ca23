﻿#pragma checksum "..\..\..\ServerMonsterDropManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F4644B8E45FB8320C06B57956110040861C4B6BC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// ServerMonsterDropManagerWindow
    /// </summary>
    public partial class ServerMonsterDropManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 76 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddButton;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteButton;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox MonsterListBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IdTextBox;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LevelTextBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxHpTextBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TribeTextBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SpawnZoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinCashTextBox;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxCashTextBox;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CashDropProbTextBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDropGroupButton;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DropGroupsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDropItemButton;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DropItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCommonDropButton;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CommonDropsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveMonsterButton;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewDropButton;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\ServerMonsterDropManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/servermonsterdropmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LoadButton = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.LoadButton.Click += new System.Windows.RoutedEventHandler(this.LoadButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.AddButton.Click += new System.Windows.RoutedEventHandler(this.AddButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.DeleteButton.Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CopyButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.CopyButton.Click += new System.Windows.RoutedEventHandler(this.CopyButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 87 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 88 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 88 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.MonsterListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 119 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.MonsterListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.MonsterListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.IdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.LevelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.MaxHpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TribeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SpawnZoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.MinCashTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.MaxCashTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.CashDropProbTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.AddDropGroupButton = ((System.Windows.Controls.Button)(target));
            
            #line 272 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.AddDropGroupButton.Click += new System.Windows.RoutedEventHandler(this.AddDropGroupButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.DropGroupsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 22:
            this.AddDropItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 307 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.AddDropItemButton.Click += new System.Windows.RoutedEventHandler(this.AddDropItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.DropItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 25:
            this.AddCommonDropButton = ((System.Windows.Controls.Button)(target));
            
            #line 345 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.AddCommonDropButton.Click += new System.Windows.RoutedEventHandler(this.AddCommonDropButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CommonDropsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 28:
            this.SaveMonsterButton = ((System.Windows.Controls.Button)(target));
            
            #line 374 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.SaveMonsterButton.Click += new System.Windows.RoutedEventHandler(this.SaveMonsterButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 376 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.PreviewDropButton = ((System.Windows.Controls.Button)(target));
            
            #line 378 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            this.PreviewDropButton.Click += new System.Windows.RoutedEventHandler(this.PreviewDropButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.CountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 21:
            
            #line 284 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteDropGroupButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 325 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteDropItemButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 358 "..\..\..\ServerMonsterDropManagerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCommonDropButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

