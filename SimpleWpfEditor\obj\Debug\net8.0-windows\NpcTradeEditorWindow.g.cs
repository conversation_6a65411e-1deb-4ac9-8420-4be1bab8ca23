﻿#pragma checksum "..\..\..\NpcTradeEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "954D483914D255B748F8E368A0AE62F984B9E462"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// NpcTradeEditorWindow
    /// </summary>
    public partial class NpcTradeEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TradeInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TradeIdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UseCategoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TradeTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeaponTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArmorTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FactionTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LevelTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ItemStatsListBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddItemButton;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveItemButton;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox GoodsListBox;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\NpcTradeEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/npctradeeditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\NpcTradeEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TradeInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TradeIdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.IdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.UseCategoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.LocalizedNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TradeTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.WeaponTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ArmorTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.FactionTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LevelTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ItemCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ItemStatsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 15:
            this.AddItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.RemoveItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 17:
            this.GoodsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 18:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            return;
            case 20:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

