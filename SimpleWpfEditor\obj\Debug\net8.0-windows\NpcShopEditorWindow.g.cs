﻿#pragma checksum "..\..\..\NpcShopEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5E93916B44DC78C7C0C01C0D6B544EE6AF87C939"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleWpfEditor {
    
    
    /// <summary>
    /// NpcShopEditorWindow
    /// </summary>
    public partial class NpcShopEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShopInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShopIdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescTextBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocalizedNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShopTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfessionTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LevelTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ItemCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ItemStatsListBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddItemButton;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveItemButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox GoodsListBox;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\NpcShopEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleWpfEditor;component/npcshopeditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\NpcShopEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ShopInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ShopIdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.IdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.DescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.LocalizedNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ShopTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ProfessionTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LevelTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ItemCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ItemStatsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 12:
            this.AddItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 13:
            this.RemoveItemButton = ((System.Windows.Controls.Button)(target));
            return;
            case 14:
            this.GoodsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 15:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            return;
            case 17:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            return;
            case 18:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

