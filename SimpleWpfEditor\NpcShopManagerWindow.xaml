<Window x:Class="SimpleWpfEditor.NpcShopManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="NPC商店管理器" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="🏪" FontSize="32" VerticalAlignment="Center" Margin="0,0,16,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="NPC商店管理器" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="管理游戏中的所有NPC商店和商品列表" FontSize="14" Foreground="#7F8C8D" Margin="0,4,0,0"/>
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="OpenFileButton" Content="📁 打开文件" Padding="12,8" Margin="0,0,8,0" Background="#3498DB" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveFileButton" Content="💾 保存文件" Padding="12,8" Margin="0,0,8,0" Background="#27AE60" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveWithServerConversionButton" Content="🔄 保存并转换服务端" Padding="12,8" Background="#FF5722" Foreground="White" BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏和筛选 -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 搜索和筛选 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,0,12">
                    <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBox x:Name="SearchTextBox" Width="200" Padding="8,4" Margin="0,0,16,0" ToolTip="搜索商店ID、名称或描述"/>
                    
                    <TextBlock Text="商店类型:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox x:Name="ShopTypeFilterComboBox" Width="120" Margin="0,0,16,0"/>
                    
                    <TextBlock Text="职业:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox x:Name="ProfessionFilterComboBox" Width="100" Margin="0,0,16,0"/>
                    
                    <TextBlock Text="等级:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox x:Name="LevelFilterComboBox" Width="80" Margin="0,0,16,0"/>
                    
                    <Button x:Name="ClearFilterButton" Content="🗑️ 清除筛选" Padding="8,4" Background="#95A5A6" Foreground="White" BorderThickness="0"/>
                </StackPanel>
                
                <!-- 统计信息 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#3498DB" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="TotalShopsTextBlock" Text="总商店: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#E67E22" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="TotalItemsTextBlock" Text="总商品: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#9B59B6" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="AvgItemsTextBlock" Text="平均: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#27AE60" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="FilteredCountTextBlock" Text="筛选结果: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主数据网格 -->
        <Border Grid.Row="2" Background="White" Margin="20,10,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <DataGrid x:Name="ShopsDataGrid" 
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False" 
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F8F9FA"
                      RowBackground="White"
                      BorderThickness="0">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="内部名称" Binding="{Binding Name}" Width="200" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="描述键" Binding="{Binding Desc}" Width="150" IsReadOnly="True"/>
                    
                    <DataGridTextColumn Header="商店类型" Binding="{Binding ShopType}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="Foreground" Value="#2C3E50"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="职业类型" Binding="{Binding ProfessionType}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="等级" Binding="{Binding LevelType}" Width="60" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="商品数量" Binding="{Binding ItemCount}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#E67E22"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="商品摘要" Binding="{Binding ItemsSummary}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextWrapping" Value="Wrap"/>
                                <Setter Property="Foreground" Value="#7F8C8D"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
                
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Height" Value="35"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Border>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#34495E" Padding="20,12" Margin="20,10,20,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="准备就绪" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" x:Name="FilePathTextBlock" Text="未加载文件" Foreground="#BDC3C7" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
