using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace SimpleWpfEditor;

/// <summary>
/// 专业技能管理器窗口 - 处理 skill_base_client 格式
/// </summary>
public partial class ProfessionalSkillManagerWindow : Window
{
    #region 字段和属性

    private readonly SkillBaseXmlService _xmlService;
    private readonly ILogger<ProfessionalSkillManagerWindow> _logger;
    private readonly ObservableCollection<SkillBaseClient> _skills;
    private readonly CollectionViewSource _filteredSkills;
    private string? _currentFilePath;
    private SkillBaseClient? _currentEditingSkill;

    #endregion

    #region 构造函数

    public ProfessionalSkillManagerWindow(ILogger<ProfessionalSkillManagerWindow> logger, SkillBaseXmlService xmlService)
    {
        InitializeComponent();

        // 设置服务
        _logger = logger;
        _xmlService = xmlService;

        // 初始化集合
        _skills = new ObservableCollection<SkillBaseClient>();
        _filteredSkills = new CollectionViewSource { Source = _skills };

        // 设置数据绑定
        SkillsDataGrid.ItemsSource = _filteredSkills.View;

        // 初始化界面
        UpdateSkillCounts();
        UpdateStatus("就绪");

        _logger.LogInformation("专业技能管理器窗口已初始化");
    }

    #endregion

    #region 文件操作

    /// <summary>
    /// 打开文件按钮点击事件
    /// </summary>
    private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择技能文件",
                Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                await LoadSkillsFile(openFileDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开文件时出错");
            MessageBox.Show($"打开文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 保存文件按钮点击事件
    /// </summary>
    private async void SaveFileButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存技能文件",
                    Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                    DefaultExt = "xml",
                    FileName = "skill_base_client.xml"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _currentFilePath = saveFileDialog.FileName;
                }
                else
                {
                    return;
                }
            }

            UpdateStatus("正在保存文件...");
            await _xmlService.SaveSkillsToXmlAsync(_currentFilePath, _skills.ToList());
            UpdateStatus($"文件已保存: {System.IO.Path.GetFileName(_currentFilePath)}");
            MessageBox.Show("文件保存成功！", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件时出错");
            MessageBox.Show($"保存文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("保存文件失败");
        }
    }

    /// <summary>
    /// 加载技能文件
    /// </summary>
    private async Task LoadSkillsFile(string filePath)
    {
        try
        {
            UpdateStatus("正在加载技能文件...");
            
            _logger.LogInformation("开始加载技能文件: {FilePath}", filePath);
            
            var skills = await _xmlService.LoadSkillsFromXmlAsync(filePath);
            
            _logger.LogInformation("加载到 {Count} 个技能", skills.Count);
            
            _skills.Clear();
            foreach (var skill in skills)
            {
                _skills.Add(skill);
            }
            
            _currentFilePath = filePath;
            UpdateSkillCounts();
            UpdateStatus($"成功加载 {skills.Count} 个技能");
            Title = $"专业技能管理器 - {System.IO.Path.GetFileName(filePath)}";
            
            // 显示加载结果
            if (skills.Count > 0)
            {
                MessageBox.Show($"成功加载 {skills.Count} 个专业技能！", "加载成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("文件中没有找到技能数据，请检查XML格式是否正确。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载技能文件时出错: {FilePath}", filePath);
            MessageBox.Show($"加载技能文件时出错:\n\n{ex.Message}\n\n详细信息:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatus("加载文件失败");
        }
    }

    #endregion

    #region 技能管理

    /// <summary>
    /// 添加技能按钮点击事件
    /// </summary>
    private void AddSkillButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 创建新技能
            var newSkill = new SkillBaseClient
            {
                Id = GetNextSkillId(),
                Name = "新技能",
                Desc = "新技能描述",
                Type = "Magical",
                SubType = "None",
                ActivationAttribute = "Active",
                CostParameter = "MP"
            };

            _skills.Add(newSkill);
            UpdateSkillCounts();
            
            // 选中新添加的技能
            SkillsDataGrid.SelectedItem = newSkill;
            SkillsDataGrid.ScrollIntoView(newSkill);
            
            UpdateStatus($"已添加新技能 ID: {newSkill.Id}");
            _logger.LogInformation("添加新技能: {SkillId}", newSkill.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加技能时出错");
            MessageBox.Show($"添加技能时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 获取下一个技能ID
    /// </summary>
    private int GetNextSkillId()
    {
        if (_skills.Count == 0)
            return 10001;
        
        return _skills.Max(s => s.Id) + 1;
    }

    /// <summary>
    /// 编辑技能菜单项点击事件
    /// </summary>
    private void EditSkillMenuItem_Click(object sender, RoutedEventArgs e)
    {
        if (SkillsDataGrid.SelectedItem is SkillBaseClient selectedSkill)
        {
            LoadSkillToEditor(selectedSkill);
            SkillEditPanel.IsEnabled = true;
        }
    }

    /// <summary>
    /// 复制技能菜单项点击事件
    /// </summary>
    private void CopySkillMenuItem_Click(object sender, RoutedEventArgs e)
    {
        if (SkillsDataGrid.SelectedItem is SkillBaseClient selectedSkill)
        {
            try
            {
                var copiedSkill = new SkillBaseClient
                {
                    Id = GetNextSkillId(),
                    Name = selectedSkill.Name + "_副本",
                    Desc = selectedSkill.Desc,
                    DescLong = selectedSkill.DescLong,
                    Type = selectedSkill.Type,
                    SubType = selectedSkill.SubType,
                    ActivationAttribute = selectedSkill.ActivationAttribute,
                    CostParameter = selectedSkill.CostParameter,
                    // 复制其他重要属性...
                };

                _skills.Add(copiedSkill);
                UpdateSkillCounts();
                
                SkillsDataGrid.SelectedItem = copiedSkill;
                SkillsDataGrid.ScrollIntoView(copiedSkill);
                
                UpdateStatus($"已复制技能，新ID: {copiedSkill.Id}");
                _logger.LogInformation("复制技能: {OriginalId} -> {NewId}", selectedSkill.Id, copiedSkill.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制技能时出错");
                MessageBox.Show($"复制技能时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 删除技能菜单项点击事件
    /// </summary>
    private void DeleteSkillMenuItem_Click(object sender, RoutedEventArgs e)
    {
        if (SkillsDataGrid.SelectedItem is SkillBaseClient selectedSkill)
        {
            var result = MessageBox.Show($"确定要删除技能 {selectedSkill.Id} - {selectedSkill.DisplayName} 吗？", 
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _skills.Remove(selectedSkill);
                UpdateSkillCounts();
                ClearEditor();
                SkillEditPanel.IsEnabled = false;
                
                UpdateStatus($"已删除技能 ID: {selectedSkill.Id}");
                _logger.LogInformation("删除技能: {SkillId} - {SkillName}", selectedSkill.Id, selectedSkill.DisplayName);
            }
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新技能计数
    /// </summary>
    private void UpdateSkillCounts()
    {
        TotalSkillsText.Text = _skills.Count.ToString();
        FilteredSkillsText.Text = _filteredSkills.View?.Cast<object>().Count().ToString() ?? "0";
    }

    /// <summary>
    /// 更新状态栏
    /// </summary>
    private void UpdateStatus(string message)
    {
        StatusText.Text = message;
        UpdateSkillCounts();
    }

    #endregion

    #region 技能编辑功能

    /// <summary>
    /// 技能选择变化事件
    /// </summary>
    private void SkillsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (SkillsDataGrid.SelectedItem is SkillBaseClient selectedSkill)
        {
            LoadSkillToEditor(selectedSkill);
            SkillEditPanel.IsEnabled = true;
        }
        else
        {
            ClearEditor();
            SkillEditPanel.IsEnabled = false;
        }
    }

    /// <summary>
    /// 将技能数据加载到编辑器
    /// </summary>
    private void LoadSkillToEditor(SkillBaseClient skill)
    {
        _currentEditingSkill = skill;

        // 基础信息
        SkillIdTextBox.Text = skill.Id.ToString();
        SkillNameTextBox.Text = skill.Name;
        SkillDescTextBox.Text = skill.Desc;
        SkillDescLongTextBox.Text = skill.DescLong;

        // 设置技能类型
        foreach (ComboBoxItem item in SkillTypeComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.Type)
            {
                SkillTypeComboBox.SelectedItem = item;
                break;
            }
        }

        // 设置子类型
        foreach (ComboBoxItem item in SkillSubTypeComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.SubType)
            {
                SkillSubTypeComboBox.SelectedItem = item;
                break;
            }
        }

        // 设置激活属性
        foreach (ComboBoxItem item in SkillActivationComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.ActivationAttribute)
            {
                SkillActivationComboBox.SelectedItem = item;
                break;
            }
        }

        SkillIconNameTextBox.Text = skill.SkillIconName;

        // 时间和行为属性
        CastingDelayTextBox.Text = skill.CastingDelay.ToString();
        CancelRateTextBox.Text = skill.CancelRate.ToString();
        ObstacleTextBox.Text = skill.Obstacle.ToString();

        // 设置移动施法
        foreach (ComboBoxItem item in MoveCastingComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.MoveCasting.ToString())
            {
                MoveCastingComboBox.SelectedItem = item;
                break;
            }
        }

        // 设置自动攻击
        foreach (ComboBoxItem item in AutoAttackComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.AutoAttack)
            {
                AutoAttackComboBox.SelectedItem = item;
                break;
            }
        }

        // 设置消耗参数
        foreach (ComboBoxItem item in CostParameterComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.CostParameter)
            {
                CostParameterComboBox.SelectedItem = item;
                break;
            }
        }

        // 消耗和成本
        CostEndTextBox.Text = skill.CostEnd.ToString();
        CostChargeWeaponTextBox.Text = skill.CostChargeWeapon.ToString();
        CostChargeArmorTextBox.Text = skill.CostChargeArmor.ToString();
        PolishChargeWeaponTextBox.Text = skill.PolishChargeWeapon.ToString();

        // 设置驱散分类
        foreach (ComboBoxItem item in DispelCategoryComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.DispelCategory)
            {
                DispelCategoryComboBox.SelectedItem = item;
                break;
            }
        }

        // 目标系统
        foreach (ComboBoxItem item in TargetSlotComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.TargetSlot)
            {
                TargetSlotComboBox.SelectedItem = item;
                break;
            }
        }

        foreach (ComboBoxItem item in HostileTypeComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.HostileType)
            {
                HostileTypeComboBox.SelectedItem = item;
                break;
            }
        }

        DelayTimeTextBox.Text = skill.DelayTime.ToString();
        TargetMaxcountTextBox.Text = skill.TargetMaxcount.ToString();

        foreach (ComboBoxItem item in FirstTargetComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.FirstTarget)
            {
                FirstTargetComboBox.SelectedItem = item;
                break;
            }
        }

        foreach (ComboBoxItem item in TargetRangeComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.TargetRange)
            {
                TargetRangeComboBox.SelectedItem = item;
                break;
            }
        }

        foreach (ComboBoxItem item in TargetSpeciesRestrictionComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.TargetSpeciesRestriction)
            {
                TargetSpeciesRestrictionComboBox.SelectedItem = item;
                break;
            }
        }

        foreach (ComboBoxItem item in TargetRelationRestrictionComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.TargetRelationRestriction)
            {
                TargetRelationRestrictionComboBox.SelectedItem = item;
                break;
            }
        }

        // 动作和动画
        MotionNameTextBox.Text = skill.MotionName;
        MotionPlaySpeedTextBox.Text = skill.MotionPlaySpeed.ToString();
        InstantSkillCheckBox.IsChecked = skill.InstantSkill == 1;
        PeaceSkillCheckBox.IsChecked = skill.PeaceSkill == 1;
        ShowWeaponCheckBox.IsChecked = skill.ShowWeapon == 1;

        // 效果1系统
        Effect1TypeTextBox.Text = skill.Effect1Type;
        foreach (ComboBoxItem item in Effect1TargetTypeComboBox.Items)
        {
            if (item.Tag?.ToString() == skill.Effect1TargetType)
            {
                Effect1TargetTypeComboBox.SelectedItem = item;
                break;
            }
        }
        Effect1Remain1TextBox.Text = skill.Effect1Remain1.ToString();
        Effect1Remain2TextBox.Text = skill.Effect1Remain2.ToString();
        Effect1EffectIdTextBox.Text = skill.Effect1EffectId.ToString();
        Effect1Reserved2TextBox.Text = skill.Effect1Reserved2.ToString();
        Effect1Reserved4TextBox.Text = skill.Effect1Reserved4.ToString();
        Effect1Reserved13TextBox.Text = skill.Effect1Reserved13;
        Effect1Reserved14TextBox.Text = skill.Effect1Reserved14;

        // 高级属性
        SkillCategoryTextBox.Text = skill.SkillCategory;
        DelayIdTextBox.Text = skill.DelayId.ToString();
        ChainCategoryLevelTextBox.Text = skill.ChainCategoryLevel.ToString();
        SkillGroupNameTextBox.Text = skill.SkillGroupName;
        DescAbnormalTextBox.Text = skill.DescAbnormal;
        DescPrechainCategoryNameTextBox.Text = skill.DescPrechainCategoryName;
    }

    /// <summary>
    /// 清空编辑器
    /// </summary>
    private void ClearEditor()
    {
        _currentEditingSkill = null;

        SkillIdTextBox.Text = "";
        SkillNameTextBox.Text = "";
        SkillDescTextBox.Text = "";
        SkillDescLongTextBox.Text = "";
        SkillTypeComboBox.SelectedIndex = -1;
        SkillSubTypeComboBox.SelectedIndex = -1;
        SkillActivationComboBox.SelectedIndex = -1;
        SkillIconNameTextBox.Text = "";
    }

    /// <summary>
    /// 保存技能修改
    /// </summary>
    private void SaveSkillButton_Click(object sender, RoutedEventArgs e)
    {
        if (_currentEditingSkill == null)
            return;

        try
        {
            // 验证并保存数据
            _currentEditingSkill.Name = SkillNameTextBox.Text;
            _currentEditingSkill.Desc = SkillDescTextBox.Text;
            _currentEditingSkill.DescLong = SkillDescLongTextBox.Text;

            if (SkillTypeComboBox.SelectedItem is ComboBoxItem skillTypeItem)
                _currentEditingSkill.Type = skillTypeItem.Tag?.ToString() ?? "Magical";

            if (SkillSubTypeComboBox.SelectedItem is ComboBoxItem subTypeItem)
                _currentEditingSkill.SubType = subTypeItem.Tag?.ToString() ?? "None";

            if (SkillActivationComboBox.SelectedItem is ComboBoxItem activationItem)
                _currentEditingSkill.ActivationAttribute = activationItem.Tag?.ToString() ?? "Active";

            _currentEditingSkill.SkillIconName = SkillIconNameTextBox.Text;

            // 时间和行为属性
            if (int.TryParse(CastingDelayTextBox.Text, out int castingDelay))
                _currentEditingSkill.CastingDelay = castingDelay;
            if (int.TryParse(CancelRateTextBox.Text, out int cancelRate))
                _currentEditingSkill.CancelRate = cancelRate;
            if (int.TryParse(ObstacleTextBox.Text, out int obstacle))
                _currentEditingSkill.Obstacle = obstacle;

            if (MoveCastingComboBox.SelectedItem is ComboBoxItem moveCastingItem)
                _currentEditingSkill.MoveCasting = int.Parse(moveCastingItem.Tag?.ToString() ?? "1");

            if (AutoAttackComboBox.SelectedItem is ComboBoxItem autoAttackItem)
                _currentEditingSkill.AutoAttack = autoAttackItem.Tag?.ToString() ?? "stop";

            if (CostParameterComboBox.SelectedItem is ComboBoxItem costParameterItem)
                _currentEditingSkill.CostParameter = costParameterItem.Tag?.ToString() ?? "MP";

            // 消耗和成本
            if (int.TryParse(CostEndTextBox.Text, out int costEnd))
                _currentEditingSkill.CostEnd = costEnd;
            if (int.TryParse(CostChargeWeaponTextBox.Text, out int costChargeWeapon))
                _currentEditingSkill.CostChargeWeapon = costChargeWeapon;
            if (int.TryParse(CostChargeArmorTextBox.Text, out int costChargeArmor))
                _currentEditingSkill.CostChargeArmor = costChargeArmor;
            if (int.TryParse(PolishChargeWeaponTextBox.Text, out int polishChargeWeapon))
                _currentEditingSkill.PolishChargeWeapon = polishChargeWeapon;

            if (DispelCategoryComboBox.SelectedItem is ComboBoxItem dispelCategoryItem)
                _currentEditingSkill.DispelCategory = dispelCategoryItem.Tag?.ToString() ?? "";

            // 目标系统
            if (TargetSlotComboBox.SelectedItem is ComboBoxItem targetSlotItem)
                _currentEditingSkill.TargetSlot = targetSlotItem.Tag?.ToString() ?? "none";

            if (HostileTypeComboBox.SelectedItem is ComboBoxItem hostileTypeItem)
                _currentEditingSkill.HostileType = hostileTypeItem.Tag?.ToString() ?? "none";

            if (int.TryParse(DelayTimeTextBox.Text, out int delayTime))
                _currentEditingSkill.DelayTime = delayTime;
            if (int.TryParse(TargetMaxcountTextBox.Text, out int targetMaxcount))
                _currentEditingSkill.TargetMaxcount = targetMaxcount;

            if (FirstTargetComboBox.SelectedItem is ComboBoxItem firstTargetItem)
                _currentEditingSkill.FirstTarget = firstTargetItem.Tag?.ToString() ?? "Me";

            if (TargetRangeComboBox.SelectedItem is ComboBoxItem targetRangeItem)
                _currentEditingSkill.TargetRange = targetRangeItem.Tag?.ToString() ?? "";

            if (TargetSpeciesRestrictionComboBox.SelectedItem is ComboBoxItem targetSpeciesItem)
                _currentEditingSkill.TargetSpeciesRestriction = targetSpeciesItem.Tag?.ToString() ?? "All";

            if (TargetRelationRestrictionComboBox.SelectedItem is ComboBoxItem targetRelationItem)
                _currentEditingSkill.TargetRelationRestriction = targetRelationItem.Tag?.ToString() ?? "Enemy";

            // 动作和动画
            _currentEditingSkill.MotionName = MotionNameTextBox.Text;
            if (int.TryParse(MotionPlaySpeedTextBox.Text, out int motionPlaySpeed))
                _currentEditingSkill.MotionPlaySpeed = motionPlaySpeed;
            _currentEditingSkill.InstantSkill = InstantSkillCheckBox.IsChecked == true ? 1 : 0;
            _currentEditingSkill.PeaceSkill = PeaceSkillCheckBox.IsChecked == true ? 1 : 0;
            _currentEditingSkill.ShowWeapon = ShowWeaponCheckBox.IsChecked == true ? 1 : 0;

            // 效果1系统
            _currentEditingSkill.Effect1Type = Effect1TypeTextBox.Text;
            if (Effect1TargetTypeComboBox.SelectedItem is ComboBoxItem effect1TargetTypeItem)
                _currentEditingSkill.Effect1TargetType = effect1TargetTypeItem.Tag?.ToString() ?? "";

            if (int.TryParse(Effect1Remain1TextBox.Text, out int effect1Remain1))
                _currentEditingSkill.Effect1Remain1 = effect1Remain1;
            if (int.TryParse(Effect1Remain2TextBox.Text, out int effect1Remain2))
                _currentEditingSkill.Effect1Remain2 = effect1Remain2;
            if (int.TryParse(Effect1EffectIdTextBox.Text, out int effect1EffectId))
                _currentEditingSkill.Effect1EffectId = effect1EffectId;
            if (int.TryParse(Effect1Reserved2TextBox.Text, out int effect1Reserved2))
                _currentEditingSkill.Effect1Reserved2 = effect1Reserved2;
            if (int.TryParse(Effect1Reserved4TextBox.Text, out int effect1Reserved4))
                _currentEditingSkill.Effect1Reserved4 = effect1Reserved4;
            _currentEditingSkill.Effect1Reserved13 = Effect1Reserved13TextBox.Text;
            _currentEditingSkill.Effect1Reserved14 = Effect1Reserved14TextBox.Text;

            // 高级属性
            _currentEditingSkill.SkillCategory = SkillCategoryTextBox.Text;
            if (int.TryParse(DelayIdTextBox.Text, out int delayId))
                _currentEditingSkill.DelayId = delayId;
            if (int.TryParse(ChainCategoryLevelTextBox.Text, out int chainCategoryLevel))
                _currentEditingSkill.ChainCategoryLevel = chainCategoryLevel;
            _currentEditingSkill.SkillGroupName = SkillGroupNameTextBox.Text;
            _currentEditingSkill.DescAbnormal = DescAbnormalTextBox.Text;
            _currentEditingSkill.DescPrechainCategoryName = DescPrechainCategoryNameTextBox.Text;

            // 刷新显示
            _filteredSkills.View.Refresh();

            MessageBox.Show("技能修改已保存！", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
            _logger.LogInformation("技能 {SkillId} - {SkillName} 修改已保存", _currentEditingSkill.Id, _currentEditingSkill.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存技能修改时出错");
            MessageBox.Show($"保存技能修改时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 取消编辑
    /// </summary>
    private void CancelEditButton_Click(object sender, RoutedEventArgs e)
    {
        if (_currentEditingSkill != null)
        {
            // 重新加载原始数据
            LoadSkillToEditor(_currentEditingSkill);
            MessageBox.Show("已取消修改，恢复到原始数据。", "取消编辑", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    #endregion

    #region 筛选和搜索功能

    /// <summary>
    /// 搜索文本变化事件
    /// </summary>
    private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    /// <summary>
    /// 类型筛选变化事件
    /// </summary>
    private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    /// <summary>
    /// 激活类型筛选变化事件
    /// </summary>
    private void ActivationFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    /// <summary>
    /// 清除筛选按钮点击事件
    /// </summary>
    private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Text = "";
        TypeFilterComboBox.SelectedIndex = 0;
        ActivationFilterComboBox.SelectedIndex = 0;
        ApplyFilters();
    }

    /// <summary>
    /// 应用筛选条件
    /// </summary>
    private void ApplyFilters()
    {
        if (_filteredSkills?.View == null) return;

        _filteredSkills.View.Filter = skill =>
        {
            if (skill is not SkillBaseClient skillItem) return false;

            // 搜索文本筛选
            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            if (!string.IsNullOrEmpty(searchText))
            {
                var matchesSearch = skillItem.Id.ToString().Contains(searchText) ||
                                  skillItem.Name.ToLower().Contains(searchText) ||
                                  skillItem.Desc.ToLower().Contains(searchText);
                if (!matchesSearch) return false;
            }

            // 类型筛选
            if (TypeFilterComboBox?.SelectedItem is ComboBoxItem typeFilter &&
                !string.IsNullOrEmpty(typeFilter.Tag?.ToString()))
            {
                if (skillItem.Type != typeFilter.Tag.ToString()) return false;
            }

            // 激活类型筛选
            if (ActivationFilterComboBox?.SelectedItem is ComboBoxItem activationFilter &&
                !string.IsNullOrEmpty(activationFilter.Tag?.ToString()))
            {
                if (skillItem.ActivationAttribute != activationFilter.Tag.ToString()) return false;
            }

            return true;
        };

        UpdateSkillCounts();
    }

    #endregion
}
