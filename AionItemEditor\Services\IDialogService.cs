namespace AionItemEditor.Services;

/// <summary>
/// 对话框服务接口
/// 提供各种对话框功能
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示信息对话框
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    Task ShowInfoAsync(string message, string title = "信息");

    /// <summary>
    /// 显示警告对话框
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    Task ShowWarningAsync(string message, string title = "警告");

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    Task ShowErrorAsync(string message, string title = "错误");

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <returns>用户是否确认</returns>
    Task<bool> ShowConfirmAsync(string message, string title = "确认");

    /// <summary>
    /// 显示文件选择对话框
    /// </summary>
    /// <param name="filter">文件过滤器</param>
    /// <param name="title">标题</param>
    /// <returns>选择的文件路径，如果取消则返回null</returns>
    Task<string?> ShowOpenFileDialogAsync(string filter = "XML文件|*.xml|所有文件|*.*", string title = "选择文件");

    /// <summary>
    /// 显示文件保存对话框
    /// </summary>
    /// <param name="filter">文件过滤器</param>
    /// <param name="title">标题</param>
    /// <param name="defaultFileName">默认文件名</param>
    /// <returns>保存的文件路径，如果取消则返回null</returns>
    Task<string?> ShowSaveFileDialogAsync(string filter = "XML文件|*.xml|所有文件|*.*", string title = "保存文件", string defaultFileName = "");

    /// <summary>
    /// 显示文件夹选择对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <returns>选择的文件夹路径，如果取消则返回null</returns>
    Task<string?> ShowFolderDialogAsync(string title = "选择文件夹");

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="message">提示消息</param>
    /// <param name="title">标题</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>用户输入的值，如果取消则返回null</returns>
    Task<string?> ShowInputDialogAsync(string message, string title = "输入", string defaultValue = "");
}
