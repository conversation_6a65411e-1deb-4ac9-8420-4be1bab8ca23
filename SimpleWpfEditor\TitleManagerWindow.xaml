<Window x:Class="SimpleWpfEditor.TitleManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="称号管理器" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="🏆" FontSize="32" VerticalAlignment="Center" Margin="0,0,16,0"/>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="称号管理器" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="管理游戏中的所有称号和奖励属性" FontSize="14" Foreground="#7F8C8D" Margin="0,4,0,0"/>
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="OpenFileButton" Content="📁 打开文件" Padding="12,8" Margin="0,0,8,0" Background="#3498DB" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveFileButton" Content="💾 保存文件" Padding="12,8" Margin="0,0,8,0" Background="#27AE60" Foreground="White" BorderThickness="0"/>
                    <Button x:Name="SaveWithServerConversionButton" Content="🔄 保存并转换服务端" Padding="12,8" Background="#FF5722" Foreground="White" BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏和统计信息 -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 搜索和筛选 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBox x:Name="SearchTextBox" Width="200" Padding="8,4" Margin="0,0,16,0" ToolTip="搜索称号ID、名称或描述"/>
                    
                    <TextBlock Text="种族:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox x:Name="RaceFilterComboBox" Width="100" Margin="0,0,16,0">
                        <ComboBoxItem Content="全部" Tag="-1"/>
                        <ComboBoxItem Content="天族" Tag="0"/>
                        <ComboBoxItem Content="魔族" Tag="1"/>
                        <ComboBoxItem Content="通用" Tag="2"/>
                    </ComboBox>
                    
                    <Button x:Name="ClearFilterButton" Content="🗑️ 清除筛选" Padding="8,4" Background="#95A5A6" Foreground="White" BorderThickness="0"/>
                </StackPanel>
                
                <!-- 统计信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#3498DB" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="TotalCountTextBlock" Text="总计: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#E67E22" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="LightCountTextBlock" Text="天族: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#9B59B6" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock x:Name="DarkCountTextBlock" Text="魔族: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                    <Border Background="#27AE60" Padding="8,4">
                        <TextBlock x:Name="CommonCountTextBlock" Text="通用: 0" Foreground="White" FontWeight="Medium"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主数据网格 -->
        <Border Grid.Row="2" Background="White" Margin="20,10,20,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            
            <DataGrid x:Name="TitlesDataGrid" 
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False" 
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F8F9FA"
                      RowBackground="White"
                      BorderThickness="0">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="内部名称" Binding="{Binding Name}" Width="150" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="显示名称键" Binding="{Binding Desc}" Width="200" IsReadOnly="True"/>
                    
                    <DataGridTextColumn Header="本地化名称" Binding="{Binding DisplayName}" Width="200" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="Foreground" Value="#2C3E50"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="种族" Binding="{Binding RaceDisplayName}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="优先级" Binding="{Binding TitlePriority}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <DataGridTextColumn Header="奖励属性" Binding="{Binding BonusAttrsSummary}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextWrapping" Value="Wrap"/>
                                <Setter Property="Foreground" Value="#7F8C8D"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
                
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Height" Value="35"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Border>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#34495E" Padding="20,12" Margin="20,10,20,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="准备就绪" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" x:Name="FilePathTextBlock" Text="未加载文件" Foreground="#BDC3C7" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
