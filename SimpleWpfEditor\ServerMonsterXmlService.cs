using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor
{
    /// <summary>
    /// 服务端怪物XML数据服务
    /// </summary>
    public class ServerMonsterXmlService
    {
        private readonly ILogger<ServerMonsterXmlService> _logger;

        public ServerMonsterXmlService(ILogger<ServerMonsterXmlService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 从XML文件加载服务端怪物数据
        /// </summary>
        public async Task<List<ServerMonster>> LoadMonstersFromXmlAsync(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始加载服务端怪物文件: {filePath}");

                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var monsters = new List<ServerMonster>();
                var doc = new XmlDocument();
                doc.Load(filePath);

                var npcNodes = doc.SelectNodes("//npc");
                if (npcNodes == null)
                {
                    _logger.LogWarning("未找到npc节点");
                    return monsters;
                }

                foreach (XmlNode npcNode in npcNodes)
                {
                    try
                    {
                        var monster = ParseMonsterFromXml(npcNode);
                        if (monster != null)
                        {
                            monsters.Add(monster);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"解析怪物数据时出错，节点: {npcNode.OuterXml.Substring(0, Math.Min(100, npcNode.OuterXml.Length))}");
                    }
                }

                _logger.LogInformation($"成功加载 {monsters.Count} 个服务端怪物");
                return monsters;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载服务端怪物文件失败: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// 保存服务端怪物数据到XML文件
        /// </summary>
        public async Task SaveMonstersToXmlAsync(string filePath, List<ServerMonster> monsters)
        {
            try
            {
                _logger.LogInformation($"开始保存服务端怪物文件: {filePath}");

                var doc = new XmlDocument();
                var declaration = doc.CreateXmlDeclaration("1.0", "UTF-8", null);
                doc.AppendChild(declaration);

                var root = doc.CreateElement("npcs");
                doc.AppendChild(root);

                foreach (var monster in monsters)
                {
                    var monsterElement = CreateMonsterXmlElement(doc, monster);
                    root.AppendChild(monsterElement);
                }

                // 格式化保存
                var settings = new XmlWriterSettings
                {
                    Indent = true,
                    IndentChars = "\t",
                    NewLineChars = "\n"
                };

                using (var writer = XmlWriter.Create(filePath, settings))
                {
                    doc.Save(writer);
                }

                _logger.LogInformation($"成功保存 {monsters.Count} 个服务端怪物到文件: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存服务端怪物文件失败: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// 从XML节点解析怪物数据
        /// </summary>
        private ServerMonster? ParseMonsterFromXml(XmlNode npcNode)
        {
            try
            {
                var monster = new ServerMonster();

                // 基础信息
                monster.Id = GetIntValue(npcNode, "id");
                monster.Name = GetStringValue(npcNode, "name");
                monster.Desc = GetStringValue(npcNode, "desc");
                monster.SpawnZoneName = GetStringValue(npcNode, "__spawn_zonename__");
                monster.NpcTitle = GetStringValue(npcNode, "npc_title");
                monster.DiskType = GetStringValue(npcNode, "disk_type");

                // 外观
                ParseAppearance(npcNode, monster);
                ParseVisibleEquipments(npcNode, monster);
                ParseBoundRadius(npcNode, monster);

                monster.Scale = GetIntValue(npcNode, "scale", 100);
                monster.WeaponScale = GetIntValue(npcNode, "weapon_scale", 100);

                // 移动属性
                monster.MoveSpeedNormalWalk = GetFloatValue(npcNode, "move_speed_normal_walk", 1.457f);
                monster.MoveSpeedNormalRun = GetFloatValue(npcNode, "move_speed_normal_run", 6.0f);
                monster.MoveSpeedCombatRun = GetFloatValue(npcNode, "move_speed_combat_run", 7.0f);
                monster.PushedRange = GetFloatValue(npcNode, "pushed_range", 2.0f);
                monster.DesignReference = GetStringValue(npcNode, "__design_reference__");

                // 基础属性
                monster.Level = GetIntValue(npcNode, "level", 1);
                monster.Exp = GetIntValue(npcNode, "exp");
                monster.Dp = GetIntValue(npcNode, "dp", 100);
                monster.MaxHp = GetIntValue(npcNode, "max_hp", 100);
                monster.HpgaugeLevel = GetIntValue(npcNode, "hpgauge_level", 1);
                monster.HpRegen = GetIntValue(npcNode, "hp_regen");

                // 战斗属性
                monster.PhysicalAttack = GetIntValue(npcNode, "physical_attack");
                monster.PhysicalDefend = GetIntValue(npcNode, "physical_defend");
                monster.MagicalAttack = GetIntValue(npcNode, "magical_attack");
                monster.MagicalDefend = GetIntValue(npcNode, "magical_defend");
                monster.MagicalResist = GetIntValue(npcNode, "magical_resist");
                monster.MinDamage = GetIntValue(npcNode, "min_damage");
                monster.MaxDamage = GetIntValue(npcNode, "max_damage");
                monster.MagicalSkillBoost = GetIntValue(npcNode, "magical_skill_boost");
                monster.MagicalSkillBoostResist = GetIntValue(npcNode, "magical_skill_boost_resist");
                monster.HitCount = GetIntValue(npcNode, "hit_count", 1);
                monster.AttackDelay = GetIntValue(npcNode, "attack_delay", 2000);
                monster.HitAccuracy = GetIntValue(npcNode, "hit_accuracy");
                monster.MagicalHitAccuracy = GetIntValue(npcNode, "magical_hit_accuracy");

                // 暴击属性
                monster.Critical = GetIntValue(npcNode, "critical");
                monster.PhysicalCriticalReduceRate = GetIntValue(npcNode, "physical_critical_reduce_rate");
                monster.PhysicalCriticalDamageReduce = GetIntValue(npcNode, "physical_critical_damage_reduce");
                monster.MagicalCritical = GetIntValue(npcNode, "magical_critical");
                monster.MagicalCriticalReduceRate = GetIntValue(npcNode, "magical_critical_reduce_rate");
                monster.MagicalCriticalDamageReduce = GetIntValue(npcNode, "magical_critical_damage_reduce");

                // 防御属性
                monster.Concentration = GetIntValue(npcNode, "concentration");
                monster.Dodge = GetIntValue(npcNode, "dodge");
                monster.Parry = GetIntValue(npcNode, "parry");
                monster.Block = GetIntValue(npcNode, "block");
                monster.DpReduce = GetIntValue(npcNode, "dp_reduce");

                // 深渊属性
                monster.AbyssPoint = GetIntValue(npcNode, "abyss_point");
                monster.AbyssRank = GetIntValue(npcNode, "abyss_rank", 1);
                monster.CancelLevel = GetIntValue(npcNode, "cancel_level", 50);

                // 元素防御
                ParseFirstLevelAttr(npcNode, monster);

                // 异常状态抗性
                ParseAbnormalResists(npcNode, monster);
                ParseAbnormalPenetrations(npcNode, monster);

                // 伤害修正
                monster.PhysicalDamageTrim = GetIntValue(npcNode, "physical_damage_trim");
                monster.MagicalDamageTrim = GetIntValue(npcNode, "magical_damage_trim");
                monster.NoreducespellImmune = GetIntValue(npcNode, "noreducespell_immune");
                monster.FirstAttackSplitRate = GetStringValue(npcNode, "first_attack_split_rate", "Split_Default");

                // AI和行为
                monster.GameLang = GetStringValue(npcNode, "game_lang");
                monster.NoLootingDespawnTime = GetIntValue(npcNode, "no_looting_despawn_time", 5000);
                monster.AiName = GetStringValue(npcNode, "ai_name");
                monster.QuestAiName = GetStringValue(npcNode, "quest_ai_name");
                monster.IdleName = GetStringValue(npcNode, "idle_name", "NoMove");
                monster.Tribe = GetStringValue(npcNode, "tribe");
                monster.RaceType = GetStringValue(npcNode, "race_type");
                monster.PetAiName = GetStringValue(npcNode, "pet_ai_name", "Pet");

                // 感知和追击
                monster.MaxEnemyCount = GetIntValue(npcNode, "max_enemy_count", 10);
                monster.SensoryRange = GetFloatValue(npcNode, "sensory_range", 8.0f);
                monster.SensoryRangeShort = GetFloatValue(npcNode, "sensory_range_short", 4.0f);
                monster.SensoryAngle = GetIntValue(npcNode, "sensory_angle", 240);
                monster.MaxChaseTime = GetIntValue(npcNode, "max_chase_time", 8);
                monster.ChaseFlying = GetIntValue(npcNode, "chase_flying");
                monster.ReactToPathfindFail = GetStringValue(npcNode, "react_to_pathfind_fail", "return_to_sp");

                // 移动行为
                monster.MoveSpeedFlee = GetIntValue(npcNode, "move_speed_flee", 70);
                monster.MoveTypeReturn = GetStringValue(npcNode, "move_type_return", "run");
                monster.MoveSpeedReturn = GetIntValue(npcNode, "move_speed_return", 200);
                monster.DecreaseSensoryRangeReturn = GetIntValue(npcNode, "decrease_sensory_range_return", 40);

                // 仇恨系统
                monster.HateDecreaseA = GetIntValue(npcNode, "hate_decrease_a", 364);

                // 特殊能力
                monster.CanSeeInvisible = GetIntValue(npcNode, "can_see_invisible");
                monster.CanSeePolymorph = GetStringValue(npcNode, "can_see_polymorph", "FALSE");

                // 攻击和技能
                ParseAttacks(npcNode, monster);
                ParseSkills(npcNode, monster);

                // 掉落系统 - 重点！
                ParseItemsInfo(npcNode, monster);

                return monster;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析怪物XML节点时出错");
                return null;
            }
        }

        /// <summary>
        /// 解析掉落信息 - 重点方法！
        /// </summary>
        private void ParseItemsInfo(XmlNode npcNode, ServerMonster monster)
        {
            var itemsInfoNode = npcNode.SelectSingleNode("items_info");
            if (itemsInfoNode == null)
            {
                // 如果没有items_info节点，创建一个空的
                monster.ItemsInfo = new MonsterItemsInfo();
                return;
            }

            monster.ItemsInfo = new MonsterItemsInfo();

            // 金币掉落
            monster.ItemsInfo.MinCashAmount = GetIntValue(itemsInfoNode, "min_cash_amount");
            monster.ItemsInfo.MaxCashAmount = GetIntValue(itemsInfoNode, "max_cash_amount");
            monster.ItemsInfo.CashDropProb = GetIntValue(itemsInfoNode, "cash_drop_prob");

            // 掉落组
            var dropGroupsNode = itemsInfoNode.SelectSingleNode("drop_groups");
            if (dropGroupsNode != null)
            {
                var dropGroupNodes = dropGroupsNode.SelectNodes("data");
                if (dropGroupNodes != null)
                {
                    foreach (XmlNode dropGroupNode in dropGroupNodes)
                    {
                        var dropGroup = new MonsterDropGroup
                        {
                            DropGroupMin = GetIntValue(dropGroupNode, "drop_group_min"),
                            DropGroupMax = GetIntValue(dropGroupNode, "drop_group_max", 1)
                        };
                        monster.ItemsInfo.DropGroups.Add(dropGroup);
                    }
                }
            }

            // 掉落物品
            var itemsNode = itemsInfoNode.SelectSingleNode("items");
            if (itemsNode != null)
            {
                var itemNodes = itemsNode.SelectNodes("data");
                if (itemNodes != null)
                {
                    foreach (XmlNode itemNode in itemNodes)
                    {
                        var dropItem = new MonsterDropItem
                        {
                            Prob = GetIntValue(itemNode, "prob", -1),
                            Item = GetStringValue(itemNode, "item"),
                            ItemId = GetIntValue(itemNode, "item_id"),
                            ItemCount = GetIntValue(itemNode, "item_count", 1),
                            MinCount = GetIntValue(itemNode, "min_count", 1),
                            MaxCount = GetIntValue(itemNode, "max_count", 1),
                            DropAttribute = GetStringValue(itemNode, "drop_attribute", "common"),
                            DropGroupIdx = GetIntValue(itemNode, "drop_group_idx")
                        };
                        monster.ItemsInfo.Items.Add(dropItem);
                    }
                }
            }

            // 通用掉落
            var commonDropsNode = itemsInfoNode.SelectSingleNode("common_drops");
            if (commonDropsNode != null)
            {
                var commonDropNodes = commonDropsNode.SelectNodes("data");
                if (commonDropNodes != null)
                {
                    foreach (XmlNode commonDropNode in commonDropNodes)
                    {
                        var commonDrop = new MonsterCommonDrop
                        {
                            CommonDrop = GetStringValue(commonDropNode, "common_drop"),
                            CommonDropAdjustment = GetIntValue(commonDropNode, "common_drop_adjustment", -1),
                            CommonDropGroupIdx = GetIntValue(commonDropNode, "common_drop_group_idx")
                        };
                        monster.ItemsInfo.CommonDrops.Add(commonDrop);
                    }
                }
            }

            // 调试日志
            _logger.LogInformation($"解析怪物 {monster.Name} (ID: {monster.Id}) 的掉落信息: " +
                                 $"金币({monster.ItemsInfo.MinCashAmount}-{monster.ItemsInfo.MaxCashAmount}, {monster.ItemsInfo.CashDropProb}%), " +
                                 $"掉落组({monster.ItemsInfo.DropGroups.Count}), " +
                                 $"掉落物品({monster.ItemsInfo.Items.Count}), " +
                                 $"通用掉落({monster.ItemsInfo.CommonDrops.Count})");
        }

        /// <summary>
        /// 解析外观信息
        /// </summary>
        private void ParseAppearance(XmlNode npcNode, ServerMonster monster)
        {
            var appearanceNode = npcNode.SelectSingleNode("appearance");
            if (appearanceNode != null)
            {
                monster.Appearance.PcType = GetStringValue(appearanceNode, "pc_type");
                monster.Appearance.FaceType = GetIntValue(appearanceNode, "face_type");
                monster.Appearance.HairType = GetIntValue(appearanceNode, "hair_type");
            }
        }

        /// <summary>
        /// 解析可见装备
        /// </summary>
        private void ParseVisibleEquipments(XmlNode npcNode, ServerMonster monster)
        {
            var equipNode = npcNode.SelectSingleNode("visible_equipments");
            if (equipNode != null)
            {
                monster.VisibleEquipments.Head = GetStringValue(equipNode, "head");
                monster.VisibleEquipments.Torso = GetStringValue(equipNode, "torso");
                monster.VisibleEquipments.Leg = GetStringValue(equipNode, "leg");
                monster.VisibleEquipments.Foot = GetStringValue(equipNode, "foot");
                monster.VisibleEquipments.Shoulder = GetStringValue(equipNode, "shoulder");
                monster.VisibleEquipments.Glove = GetStringValue(equipNode, "glove");
                monster.VisibleEquipments.Main = GetStringValue(equipNode, "main");
                monster.VisibleEquipments.Sub = GetStringValue(equipNode, "sub");
            }
        }

        /// <summary>
        /// 解析边界半径
        /// </summary>
        private void ParseBoundRadius(XmlNode npcNode, ServerMonster monster)
        {
            var boundNode = npcNode.SelectSingleNode("bound_radius");
            if (boundNode != null)
            {
                monster.BoundRadius.Front = GetFloatValue(boundNode, "front", 0.5f);
                monster.BoundRadius.Side = GetFloatValue(boundNode, "side", 0.7f);
                monster.BoundRadius.Upper = GetFloatValue(boundNode, "upper", 2.0f);
            }
        }

        /// <summary>
        /// 解析元素防御
        /// </summary>
        private void ParseFirstLevelAttr(XmlNode npcNode, ServerMonster monster)
        {
            var attrNode = npcNode.SelectSingleNode("first_level_attr");
            if (attrNode != null)
            {
                monster.FirstLevelAttr.ElementalDefendLight = GetIntValue(attrNode, "elemental_defend_light");
                monster.FirstLevelAttr.ElementalDefendDark = GetIntValue(attrNode, "elemental_defend_dark");
                monster.FirstLevelAttr.ElementalDefendEarth = GetIntValue(attrNode, "elemental_defend_earth");
                monster.FirstLevelAttr.ElementalDefendAir = GetIntValue(attrNode, "elemental_defend_air");
                monster.FirstLevelAttr.ElementalDefendWater = GetIntValue(attrNode, "elemental_defend_water");
                monster.FirstLevelAttr.ElementalDefendFire = GetIntValue(attrNode, "elemental_defend_fire");
            }
        }

        /// <summary>
        /// 解析异常状态抗性
        /// </summary>
        private void ParseAbnormalResists(XmlNode npcNode, ServerMonster monster)
        {
            var resistsNode = npcNode.SelectSingleNode("abnormal_resists");
            if (resistsNode != null)
            {
                var resistNodes = resistsNode.SelectNodes("data");
                if (resistNodes != null)
                {
                    foreach (XmlNode resistNode in resistNodes)
                    {
                        var resist = new MonsterAbnormalResist
                        {
                            AbnormalResistName = GetStringValue(resistNode, "abnormal_resist_name"),
                            AbnormalResistValue = GetIntValue(resistNode, "abnormal_resist_value")
                        };
                        monster.AbnormalResists.Add(resist);
                    }
                }
            }
        }

        /// <summary>
        /// 解析异常状态穿透
        /// </summary>
        private void ParseAbnormalPenetrations(XmlNode npcNode, ServerMonster monster)
        {
            var penetrationsNode = npcNode.SelectSingleNode("abnormal_penetrations");
            if (penetrationsNode != null)
            {
                var penetrationNodes = penetrationsNode.SelectNodes("data");
                if (penetrationNodes != null)
                {
                    foreach (XmlNode penetrationNode in penetrationNodes)
                    {
                        var penetration = new MonsterAbnormalPenetration
                        {
                            AbnormalPenetrationValue = GetIntValue(penetrationNode, "abnormal_penetration_value")
                        };
                        monster.AbnormalPenetrations.Add(penetration);
                    }
                }
            }
        }

        /// <summary>
        /// 解析攻击信息
        /// </summary>
        private void ParseAttacks(XmlNode npcNode, ServerMonster monster)
        {
            var attacksNode = npcNode.SelectSingleNode("attacks");
            if (attacksNode != null)
            {
                var attackNodes = attacksNode.SelectNodes("data");
                if (attackNodes != null)
                {
                    foreach (XmlNode attackNode in attackNodes)
                    {
                        var attack = new MonsterAttack
                        {
                            AttackMod = GetIntValue(attackNode, "attack_mod", 100),
                            AttackRange = GetFloatValue(attackNode, "attack_range", 2.0f),
                            AttackRate = GetIntValue(attackNode, "attack_rate", 1000),
                            AttackType = GetStringValue(attackNode, "attack_type", "physical")
                        };
                        monster.Attacks.Add(attack);
                    }
                }
            }
        }

        /// <summary>
        /// 解析技能信息
        /// </summary>
        private void ParseSkills(XmlNode npcNode, ServerMonster monster)
        {
            var skillsNode = npcNode.SelectSingleNode("skills");
            if (skillsNode != null)
            {
                var skillNodes = skillsNode.SelectNodes("data");
                if (skillNodes != null)
                {
                    foreach (XmlNode skillNode in skillNodes)
                    {
                        var skill = new MonsterSkill
                        {
                            SkillName = GetStringValue(skillNode, "skill_name"),
                            SkillLevel = GetIntValue(skillNode, "skill_level", 1),
                            SkillRate = GetIntValue(skillNode, "skill_rate")
                        };
                        monster.Skills.Add(skill);
                    }
                }
            }
        }

        /// <summary>
        /// 创建怪物XML元素
        /// </summary>
        private XmlElement CreateMonsterXmlElement(XmlDocument doc, ServerMonster monster)
        {
            var npcElement = doc.CreateElement("npc");

            // 基础信息
            AppendElement(doc, npcElement, "id", monster.Id.ToString());
            AppendElement(doc, npcElement, "name", monster.Name);
            AppendElement(doc, npcElement, "desc", monster.Desc);
            if (!string.IsNullOrEmpty(monster.SpawnZoneName))
                AppendElement(doc, npcElement, "__spawn_zonename__", monster.SpawnZoneName);
            if (!string.IsNullOrEmpty(monster.NpcTitle))
                AppendElement(doc, npcElement, "npc_title", monster.NpcTitle);
            if (!string.IsNullOrEmpty(monster.DiskType))
                AppendElement(doc, npcElement, "disk_type", monster.DiskType);

            // 外观
            CreateAppearanceElement(doc, npcElement, monster);
            CreateVisibleEquipmentsElement(doc, npcElement, monster);
            CreateBoundRadiusElement(doc, npcElement, monster);

            AppendElement(doc, npcElement, "scale", monster.Scale.ToString());
            AppendElement(doc, npcElement, "weapon_scale", monster.WeaponScale.ToString());

            // 移动属性
            AppendElement(doc, npcElement, "move_speed_normal_walk", monster.MoveSpeedNormalWalk.ToString("F6"));
            AppendElement(doc, npcElement, "move_speed_normal_run", monster.MoveSpeedNormalRun.ToString("F6"));
            AppendElement(doc, npcElement, "move_speed_combat_run", monster.MoveSpeedCombatRun.ToString("F6"));
            AppendElement(doc, npcElement, "pushed_range", monster.PushedRange.ToString("F6"));
            if (!string.IsNullOrEmpty(monster.DesignReference))
                AppendElement(doc, npcElement, "__design_reference__", monster.DesignReference);

            // 基础属性
            AppendElement(doc, npcElement, "level", monster.Level.ToString());
            AppendElement(doc, npcElement, "exp", monster.Exp.ToString());
            AppendElement(doc, npcElement, "dp", monster.Dp.ToString());
            AppendElement(doc, npcElement, "max_hp", monster.MaxHp.ToString());
            AppendElement(doc, npcElement, "hpgauge_level", monster.HpgaugeLevel.ToString());
            AppendElement(doc, npcElement, "hp_regen", monster.HpRegen.ToString());

            // 战斗属性
            AppendElement(doc, npcElement, "physical_attack", monster.PhysicalAttack.ToString());
            AppendElement(doc, npcElement, "physical_defend", monster.PhysicalDefend.ToString());
            AppendElement(doc, npcElement, "magical_attack", monster.MagicalAttack.ToString());
            AppendElement(doc, npcElement, "magical_defend", monster.MagicalDefend.ToString());
            AppendElement(doc, npcElement, "magical_resist", monster.MagicalResist.ToString());
            AppendElement(doc, npcElement, "min_damage", monster.MinDamage.ToString());
            AppendElement(doc, npcElement, "max_damage", monster.MaxDamage.ToString());

            // 掉落系统 - 重点！
            CreateItemsInfoElement(doc, npcElement, monster);

            return npcElement;
        }

        /// <summary>
        /// 创建掉落信息XML元素 - 重点方法！
        /// </summary>
        private void CreateItemsInfoElement(XmlDocument doc, XmlElement parent, ServerMonster monster)
        {
            var itemsInfoElement = doc.CreateElement("items_info");

            // 金币掉落
            AppendElement(doc, itemsInfoElement, "min_cash_amount", monster.ItemsInfo.MinCashAmount.ToString());
            AppendElement(doc, itemsInfoElement, "max_cash_amount", monster.ItemsInfo.MaxCashAmount.ToString());
            AppendElement(doc, itemsInfoElement, "cash_drop_prob", monster.ItemsInfo.CashDropProb.ToString());

            // 掉落组
            if (monster.ItemsInfo.DropGroups.Count > 0)
            {
                var dropGroupsElement = doc.CreateElement("drop_groups");
                foreach (var dropGroup in monster.ItemsInfo.DropGroups)
                {
                    var dataElement = doc.CreateElement("data");
                    AppendElement(doc, dataElement, "drop_group_min", dropGroup.DropGroupMin.ToString());
                    AppendElement(doc, dataElement, "drop_group_max", dropGroup.DropGroupMax.ToString());
                    dropGroupsElement.AppendChild(dataElement);
                }
                itemsInfoElement.AppendChild(dropGroupsElement);
            }

            // 掉落物品
            if (monster.ItemsInfo.Items.Count > 0)
            {
                var itemsElement = doc.CreateElement("items");
                foreach (var item in monster.ItemsInfo.Items)
                {
                    var dataElement = doc.CreateElement("data");

                    // 物品名称（优先使用）
                    if (!string.IsNullOrEmpty(item.Item))
                        AppendElement(doc, dataElement, "item", item.Item);

                    // 物品ID（如果有的话）
                    if (item.ItemId > 0)
                        AppendElement(doc, dataElement, "item_id", item.ItemId.ToString());

                    // 掉落概率
                    if (item.Prob > 0)
                        AppendElement(doc, dataElement, "prob", item.Prob.ToString());

                    // 物品数量（单个数量）
                    if (item.ItemCount > 0)
                        AppendElement(doc, dataElement, "item_count", item.ItemCount.ToString());

                    // 数量范围
                    if (item.MinCount > 0)
                        AppendElement(doc, dataElement, "min_count", item.MinCount.ToString());
                    if (item.MaxCount > 0)
                        AppendElement(doc, dataElement, "max_count", item.MaxCount.ToString());

                    // 掉落属性
                    if (!string.IsNullOrEmpty(item.DropAttribute))
                        AppendElement(doc, dataElement, "drop_attribute", item.DropAttribute);

                    // 掉落组索引
                    if (item.DropGroupIdx >= 0)
                        AppendElement(doc, dataElement, "drop_group_idx", item.DropGroupIdx.ToString());

                    itemsElement.AppendChild(dataElement);
                }
                itemsInfoElement.AppendChild(itemsElement);
            }

            // 通用掉落
            if (monster.ItemsInfo.CommonDrops.Count > 0)
            {
                var commonDropsElement = doc.CreateElement("common_drops");
                foreach (var commonDrop in monster.ItemsInfo.CommonDrops)
                {
                    var dataElement = doc.CreateElement("data");

                    // 通用掉落名称
                    if (!string.IsNullOrEmpty(commonDrop.CommonDrop))
                        AppendElement(doc, dataElement, "common_drop", commonDrop.CommonDrop);

                    // 通用掉落调整
                    if (commonDrop.CommonDropAdjustment >= 0)
                        AppendElement(doc, dataElement, "common_drop_adjustment", commonDrop.CommonDropAdjustment.ToString());

                    // 通用掉落组索引
                    if (commonDrop.CommonDropGroupIdx >= 0)
                        AppendElement(doc, dataElement, "common_drop_group_idx", commonDrop.CommonDropGroupIdx.ToString());

                    commonDropsElement.AppendChild(dataElement);
                }
                itemsInfoElement.AppendChild(commonDropsElement);
            }

            parent.AppendChild(itemsInfoElement);
        }

        // 辅助方法
        private void AppendElement(XmlDocument doc, XmlElement parent, string name, string value)
        {
            var element = doc.CreateElement(name);
            element.InnerText = value;
            parent.AppendChild(element);
        }

        private void CreateAppearanceElement(XmlDocument doc, XmlElement parent, ServerMonster monster)
        {
            if (!string.IsNullOrEmpty(monster.Appearance.PcType) ||
                monster.Appearance.FaceType > 0 ||
                monster.Appearance.HairType > 0)
            {
                var appearanceElement = doc.CreateElement("appearance");
                if (!string.IsNullOrEmpty(monster.Appearance.PcType))
                    AppendElement(doc, appearanceElement, "pc_type", monster.Appearance.PcType);
                if (monster.Appearance.FaceType > 0)
                    AppendElement(doc, appearanceElement, "face_type", monster.Appearance.FaceType.ToString());
                if (monster.Appearance.HairType > 0)
                    AppendElement(doc, appearanceElement, "hair_type", monster.Appearance.HairType.ToString());
                parent.AppendChild(appearanceElement);
            }
        }

        private void CreateVisibleEquipmentsElement(XmlDocument doc, XmlElement parent, ServerMonster monster)
        {
            var hasEquipment = !string.IsNullOrEmpty(monster.VisibleEquipments.Head) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Torso) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Leg) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Foot) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Shoulder) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Glove) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Main) ||
                              !string.IsNullOrEmpty(monster.VisibleEquipments.Sub);

            if (hasEquipment)
            {
                var equipElement = doc.CreateElement("visible_equipments");
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Head))
                    AppendElement(doc, equipElement, "head", monster.VisibleEquipments.Head);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Torso))
                    AppendElement(doc, equipElement, "torso", monster.VisibleEquipments.Torso);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Leg))
                    AppendElement(doc, equipElement, "leg", monster.VisibleEquipments.Leg);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Foot))
                    AppendElement(doc, equipElement, "foot", monster.VisibleEquipments.Foot);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Shoulder))
                    AppendElement(doc, equipElement, "shoulder", monster.VisibleEquipments.Shoulder);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Glove))
                    AppendElement(doc, equipElement, "glove", monster.VisibleEquipments.Glove);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Main))
                    AppendElement(doc, equipElement, "main", monster.VisibleEquipments.Main);
                if (!string.IsNullOrEmpty(monster.VisibleEquipments.Sub))
                    AppendElement(doc, equipElement, "sub", monster.VisibleEquipments.Sub);
                parent.AppendChild(equipElement);
            }
        }

        private void CreateBoundRadiusElement(XmlDocument doc, XmlElement parent, ServerMonster monster)
        {
            var boundElement = doc.CreateElement("bound_radius");
            AppendElement(doc, boundElement, "front", monster.BoundRadius.Front.ToString("F6"));
            AppendElement(doc, boundElement, "side", monster.BoundRadius.Side.ToString("F6"));
            AppendElement(doc, boundElement, "upper", monster.BoundRadius.Upper.ToString("F6"));
            parent.AppendChild(boundElement);
        }

        private string GetStringValue(XmlNode node, string elementName, string defaultValue = "")
        {
            var element = node.SelectSingleNode(elementName);
            return element?.InnerText ?? defaultValue;
        }

        private int GetIntValue(XmlNode node, string elementName, int defaultValue = 0)
        {
            var element = node.SelectSingleNode(elementName);
            if (element != null && int.TryParse(element.InnerText, out int value))
                return value;
            return defaultValue;
        }

        private float GetFloatValue(XmlNode node, string elementName, float defaultValue = 0.0f)
        {
            var element = node.SelectSingleNode(elementName);
            if (element != null && float.TryParse(element.InnerText, out float value))
                return value;
            return defaultValue;
        }
    }
}
