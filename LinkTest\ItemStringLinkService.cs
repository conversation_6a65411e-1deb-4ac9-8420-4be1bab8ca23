using System.Collections.Concurrent;
using System.IO;
using Microsoft.Extensions.Logging;

namespace LinkTest;

/// <summary>
/// 物品字符串关联服务
/// 自动建立物品ID、内部名称与本地化字符串的关联
/// </summary>
public class ItemStringLinkService
{
    private readonly ILogger<ItemStringLinkService> _logger;
    private readonly StringService _stringService;
    private readonly XmlService _xmlService;
    
    // 物品ID到本地化名称的映射
    private readonly ConcurrentDictionary<int, string> _itemIdToLocalizedName;
    
    // 内部名称到本地化名称的映射
    private readonly ConcurrentDictionary<string, string> _internalNameToLocalizedName;
    
    // 物品ID到描述键的映射
    private readonly ConcurrentDictionary<int, string> _itemIdToDescKey;
    
    // 所有已加载的物品
    private readonly ConcurrentDictionary<int, ClientItem> _allItems;

    public ItemStringLinkService(
        StringService stringService, 
        XmlService xmlService, 
        ILogger<ItemStringLinkService> logger)
    {
        _stringService = stringService;
        _xmlService = xmlService;
        _logger = logger;
        
        _itemIdToLocalizedName = new ConcurrentDictionary<int, string>();
        _internalNameToLocalizedName = new ConcurrentDictionary<string, string>();
        _itemIdToDescKey = new ConcurrentDictionary<int, string>();
        _allItems = new ConcurrentDictionary<int, ClientItem>();
    }

    /// <summary>
    /// 加载所有客户端XML文件并建立关联
    /// </summary>
    public async Task LoadAllItemsAndBuildLinksAsync(string clientDirectory = "客户端")
    {
        try
        {
            _logger.LogInformation("开始加载所有客户端XML文件并建立字符串关联...");

            if (!Directory.Exists(clientDirectory))
            {
                _logger.LogWarning("客户端目录不存在: {Directory}", clientDirectory);
                return;
            }

            // 获取所有客户端XML文件
            var xmlFiles = new[]
            {
                "client_items_armor.xml",
                "client_items_etc.xml", 
                "client_items_misc.xml",
                "client_npc_goodslist.xml",
                "client_npc_purchase_list.xml",
                "client_npc_trade_in_list.xml",
                "client_npcs.xml",
                "client_skill_prohibit.xml",
                "client_skills.xml",
                "client_titles.xml"
            };

            var totalItems = 0;
            var linkedItems = 0;

            foreach (var xmlFile in xmlFiles)
            {
                var filePath = Path.Combine(clientDirectory, xmlFile);
                if (File.Exists(filePath))
                {
                    var (itemCount, linkCount) = await ProcessXmlFileAsync(filePath);
                    totalItems += itemCount;
                    linkedItems += linkCount;
                    
                    _logger.LogInformation("处理文件 {FileName}: {ItemCount} 个物品, {LinkCount} 个关联", 
                        xmlFile, itemCount, linkCount);
                }
                else
                {
                    _logger.LogWarning("文件不存在: {FilePath}", filePath);
                }
            }

            _logger.LogInformation("关联建立完成 - 总物品: {TotalItems}, 成功关联: {LinkedItems}", 
                totalItems, linkedItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载物品文件并建立关联时出错");
        }
    }

    /// <summary>
    /// 处理单个XML文件
    /// </summary>
    private async Task<(int ItemCount, int LinkCount)> ProcessXmlFileAsync(string filePath)
    {
        try
        {
            var items = await _xmlService.LoadItemsAsync(filePath);
            var linkCount = 0;

            foreach (var item in items)
            {
                // 存储物品
                _allItems.TryAdd(item.Id, item);

                // 建立ID到描述键的关联
                if (!string.IsNullOrEmpty(item.Desc))
                {
                    _itemIdToDescKey.TryAdd(item.Id, item.Desc);

                    // 获取本地化文本
                    var localizedText = _stringService.GetString(item.Desc);
                    if (!string.IsNullOrEmpty(localizedText) && localizedText != item.Desc)
                    {
                        // 建立ID到本地化名称的关联
                        _itemIdToLocalizedName.TryAdd(item.Id, localizedText);
                        
                        // 建立内部名称到本地化名称的关联
                        if (!string.IsNullOrEmpty(item.Name))
                        {
                            _internalNameToLocalizedName.TryAdd(item.Name, localizedText);
                        }
                        
                        linkCount++;
                    }
                }
            }

            return (items.Count, linkCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理XML文件时出错: {FilePath}", filePath);
            return (0, 0);
        }
    }

    /// <summary>
    /// 根据物品ID获取本地化名称
    /// </summary>
    public string? GetLocalizedNameById(int itemId)
    {
        return _itemIdToLocalizedName.TryGetValue(itemId, out var name) ? name : null;
    }

    /// <summary>
    /// 根据内部名称获取本地化名称
    /// </summary>
    public string? GetLocalizedNameByInternalName(string internalName)
    {
        if (string.IsNullOrEmpty(internalName))
            return null;
            
        return _internalNameToLocalizedName.TryGetValue(internalName, out var name) ? name : null;
    }

    /// <summary>
    /// 根据物品ID获取描述键
    /// </summary>
    public string? GetDescKeyById(int itemId)
    {
        return _itemIdToDescKey.TryGetValue(itemId, out var descKey) ? descKey : null;
    }

    /// <summary>
    /// 根据物品ID获取完整物品信息
    /// </summary>
    public ClientItem? GetItemById(int itemId)
    {
        return _allItems.TryGetValue(itemId, out var item) ? item : null;
    }

    /// <summary>
    /// 搜索物品（支持ID、内部名称、本地化名称）
    /// </summary>
    public List<ItemSearchResult> SearchItems(string searchText, int maxResults = 100)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return new List<ItemSearchResult>();

        var results = new List<ItemSearchResult>();
        var searchLower = searchText.ToLowerInvariant();

        foreach (var item in _allItems.Values)
        {
            if (results.Count >= maxResults)
                break;

            var localizedName = GetLocalizedNameById(item.Id);
            var matches = false;
            var matchType = "";

            // 检查ID匹配
            if (item.Id.ToString().Contains(searchLower))
            {
                matches = true;
                matchType = "ID";
            }
            // 检查内部名称匹配
            else if (item.Name.ToLowerInvariant().Contains(searchLower))
            {
                matches = true;
                matchType = "内部名称";
            }
            // 检查本地化名称匹配
            else if (!string.IsNullOrEmpty(localizedName) && 
                     localizedName.ToLowerInvariant().Contains(searchLower))
            {
                matches = true;
                matchType = "本地化名称";
            }

            if (matches)
            {
                results.Add(new ItemSearchResult
                {
                    Item = item,
                    LocalizedName = localizedName ?? "未找到本地化文本",
                    MatchType = matchType
                });
            }
        }

        return results.OrderBy(r => r.Item.Id).ToList();
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public (int TotalItems, int LinkedItems, int UnlinkedItems) GetStatistics()
    {
        var totalItems = _allItems.Count;
        var linkedItems = _itemIdToLocalizedName.Count;
        var unlinkedItems = totalItems - linkedItems;
        
        return (totalItems, linkedItems, unlinkedItems);
    }

    /// <summary>
    /// 获取所有未关联的物品
    /// </summary>
    public List<ClientItem> GetUnlinkedItems()
    {
        return _allItems.Values
            .Where(item => !_itemIdToLocalizedName.ContainsKey(item.Id))
            .OrderBy(item => item.Id)
            .ToList();
    }

    /// <summary>
    /// 清空所有数据
    /// </summary>
    public void Clear()
    {
        _itemIdToLocalizedName.Clear();
        _internalNameToLocalizedName.Clear();
        _itemIdToDescKey.Clear();
        _allItems.Clear();
    }
}

/// <summary>
/// 物品搜索结果
/// </summary>
public class ItemSearchResult
{
    public ClientItem Item { get; set; } = new();
    public string LocalizedName { get; set; } = string.Empty;
    public string MatchType { get; set; } = string.Empty;
}
