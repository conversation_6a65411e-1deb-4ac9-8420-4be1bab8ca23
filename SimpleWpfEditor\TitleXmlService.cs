using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Logging;

namespace SimpleWpfEditor;

/// <summary>
/// 称号XML文件服务
/// </summary>
public class TitleXmlService
{
    private readonly ILogger<TitleXmlService> _logger;

    public TitleXmlService(ILogger<TitleXmlService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加载称号文件
    /// </summary>
    public async Task<List<ClientTitle>> LoadTitlesAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("开始加载称号文件: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"称号文件不存在: {filePath}");
            }

            var titles = new List<ClientTitle>();
            var xmlDoc = new XmlDocument();
            
            // 读取XML文件
            var xmlContent = await File.ReadAllTextAsync(filePath);
            xmlDoc.LoadXml(xmlContent);

            var titleNodes = xmlDoc.SelectNodes("//client_title");
            if (titleNodes == null)
            {
                _logger.LogWarning("未找到称号节点");
                return titles;
            }

            foreach (XmlNode titleNode in titleNodes)
            {
                try
                {
                    var title = ParseClientTitle(titleNode);
                    titles.Add(title);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析称号时出错: {TitleNode}", titleNode?.OuterXml);
                }
            }

            _logger.LogInformation("成功加载 {Count} 个称号", titles.Count);
            return titles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载称号文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 解析称号节点
    /// </summary>
    private ClientTitle ParseClientTitle(XmlNode titleNode)
    {
        var title = new ClientTitle
        {
            Id = GetIntValue(titleNode, "id", 0),
            Name = GetStringValue(titleNode, "name"),
            Desc = GetStringValue(titleNode, "desc"),
            TitleDesc = GetStringValue(titleNode, "title_desc"),
            TitleRace = GetIntValue(titleNode, "title_race", 2),
            TitleLocation = GetStringValue(titleNode, "title_location"),
            TitlePriority = GetIntValue(titleNode, "title_priority", 1),
            TitleType = GetIntValue(titleNode, "title_type", 0)
        };

        // 解析奖励属性
        var bonusAttrsNode = titleNode.SelectSingleNode("bonus_attrs");
        if (bonusAttrsNode != null)
        {
            var dataNodes = bonusAttrsNode.SelectNodes("data");
            if (dataNodes != null)
            {
                foreach (XmlNode dataNode in dataNodes)
                {
                    var bonusAttrNode = dataNode.SelectSingleNode("bonus_attr");
                    if (bonusAttrNode != null && !string.IsNullOrEmpty(bonusAttrNode.InnerText))
                    {
                        title.BonusAttrs.Add(new TitleBonusData
                        {
                            BonusAttr = bonusAttrNode.InnerText.Trim()
                        });
                    }
                }
            }
        }

        return title;
    }

    /// <summary>
    /// 获取字符串值
    /// </summary>
    private string GetStringValue(XmlNode parentNode, string elementName, string defaultValue = "")
    {
        var node = parentNode.SelectSingleNode(elementName);
        return node?.InnerText?.Trim() ?? defaultValue;
    }

    /// <summary>
    /// 获取整数值
    /// </summary>
    private int GetIntValue(XmlNode parentNode, string elementName, int defaultValue = 0)
    {
        var node = parentNode.SelectSingleNode(elementName);
        if (node != null && int.TryParse(node.InnerText?.Trim(), out var value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 保存称号到XML文件
    /// </summary>
    public async Task SaveTitlesAsync(string filePath, List<ClientTitle> titles)
    {
        try
        {
            _logger.LogInformation("开始保存称号文件: {FilePath}", filePath);

            var xmlDoc = new XmlDocument();
            
            // 创建根节点
            var rootElement = xmlDoc.CreateElement("client_titles");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有称号
            foreach (var title in titles)
            {
                var titleElement = CreateTitleXmlElement(xmlDoc, title);
                rootElement.AppendChild(titleElement);
            }

            // 保存到文件
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = System.Text.Encoding.Unicode // 使用UTF-16编码，与原文件一致
            };

            using var writer = XmlWriter.Create(filePath, settings);
            xmlDoc.Save(writer);

            _logger.LogInformation("成功保存 {Count} 个称号到文件", titles.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存称号文件时出错: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 创建称号的XML元素
    /// </summary>
    private XmlElement CreateTitleXmlElement(XmlDocument xmlDoc, ClientTitle title)
    {
        var titleElement = xmlDoc.CreateElement("client_title");

        // 添加基础属性
        AddXmlElement(xmlDoc, titleElement, "id", title.Id.ToString());
        AddXmlElement(xmlDoc, titleElement, "name", title.Name);
        AddXmlElement(xmlDoc, titleElement, "desc", title.Desc);
        AddXmlElement(xmlDoc, titleElement, "title_desc", title.TitleDesc);
        AddXmlElement(xmlDoc, titleElement, "title_race", title.TitleRace.ToString());

        // 添加奖励属性
        if (title.BonusAttrs != null && title.BonusAttrs.Count > 0)
        {
            var bonusAttrsElement = xmlDoc.CreateElement("bonus_attrs");
            
            foreach (var bonusData in title.BonusAttrs)
            {
                if (!string.IsNullOrEmpty(bonusData.BonusAttr))
                {
                    var dataElement = xmlDoc.CreateElement("data");
                    AddXmlElement(xmlDoc, dataElement, "bonus_attr", bonusData.BonusAttr);
                    bonusAttrsElement.AppendChild(dataElement);
                }
            }
            
            titleElement.AppendChild(bonusAttrsElement);
        }

        AddXmlElement(xmlDoc, titleElement, "title_location", title.TitleLocation);
        AddXmlElement(xmlDoc, titleElement, "title_priority", title.TitlePriority.ToString());
        AddXmlElement(xmlDoc, titleElement, "title_type", title.TitleType.ToString());

        return titleElement;
    }

    /// <summary>
    /// 添加XML子元素
    /// </summary>
    private void AddXmlElement(XmlDocument xmlDoc, XmlElement parentElement, string elementName, string value)
    {
        var element = xmlDoc.CreateElement(elementName);
        element.InnerText = value ?? string.Empty;
        parentElement.AppendChild(element);
    }

    /// <summary>
    /// 获取称号统计信息
    /// </summary>
    public TitleStatistics GetStatistics(List<ClientTitle> titles)
    {
        var stats = new TitleStatistics
        {
            TotalTitles = titles.Count,
            LightTitles = titles.Count(t => t.TitleRace == 0),
            DarkTitles = titles.Count(t => t.TitleRace == 1),
            CommonTitles = titles.Count(t => t.TitleRace == 2)
        };

        // 统计奖励属性类型
        foreach (var title in titles)
        {
            foreach (var bonus in title.BonusAttrs)
            {
                if (!string.IsNullOrEmpty(bonus.BonusAttr))
                {
                    var attrType = bonus.BonusAttr.Split(' ')[0]; // 获取属性类型
                    if (stats.BonusAttrTypes.ContainsKey(attrType))
                        stats.BonusAttrTypes[attrType]++;
                    else
                        stats.BonusAttrTypes[attrType] = 1;
                }
            }
        }

        // 统计地点分布
        foreach (var title in titles)
        {
            if (!string.IsNullOrEmpty(title.TitleLocation))
            {
                if (stats.LocationCounts.ContainsKey(title.TitleLocation))
                    stats.LocationCounts[title.TitleLocation]++;
                else
                    stats.LocationCounts[title.TitleLocation] = 1;
            }
        }

        return stats;
    }

    /// <summary>
    /// 保存称号数据并转换为服务端格式
    /// </summary>
    public async Task<bool> SaveWithServerConversionAsync(string clientFilePath, List<ClientTitle> titles, string serverBasePath)
    {
        try
        {
            _logger.LogInformation($"开始转换 {titles.Count} 个称号到服务端格式");

            // 确保服务端目录存在
            if (!Directory.Exists(serverBasePath))
            {
                Directory.CreateDirectory(serverBasePath);
            }

            // 创建服务端XML文档
            var xmlDoc = new XmlDocument();
            xmlDoc.AppendChild(xmlDoc.CreateXmlDeclaration("1.0", "UTF-8", null));

            // 使用服务端根标签
            var rootElement = xmlDoc.CreateElement("titles");
            rootElement.SetAttribute("generated_time", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
            xmlDoc.AppendChild(rootElement);

            // 添加所有称号（按ID排序）
            var sortedTitles = titles.OrderBy(t => t.Id).ToList();
            foreach (var title in sortedTitles)
            {
                var titleElement = CreateTitleXmlElement(xmlDoc, title);
                rootElement.AppendChild(titleElement);
            }

            // 保存服务端文件
            var serverFilePath = Path.Combine(serverBasePath, "titles.xml");
            xmlDoc.Save(serverFilePath);

            _logger.LogInformation($"服务端称号文件已保存: {serverFilePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换称号到服务端格式时出错");
            return false;
        }
    }
}
