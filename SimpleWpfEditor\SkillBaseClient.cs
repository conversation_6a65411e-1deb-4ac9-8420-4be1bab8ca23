namespace SimpleWpfEditor;

/// <summary>
/// 完整的技能基础数据模型 - 基于真实的skill_base_client XML结构
/// </summary>
public class SkillBaseClient
{
    // 基础信息
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Desc { get; set; } = string.Empty;
    public string DescLong { get; set; } = string.Empty;
    public string DescAbnormal { get; set; } = string.Empty;
    public string DescPrechainCategoryName { get; set; } = string.Empty;
    public string Type { get; set; } = "Physical";
    public string SubType { get; set; } = "Attack";
    public string SkillCategory { get; set; } = "SKILLCTG_NONE";
    public int UltraSkill { get; set; }
    public int UltraTransfer { get; set; }
    public int ChainCategoryLevel { get; set; }
    public string SkillGroupName { get; set; } = string.Empty;
    public string SkillIconName { get; set; } = string.Empty;
    public int DelayId { get; set; }
    public string ActivationAttribute { get; set; } = "Active";

    // 消耗相关
    public string CostParameter { get; set; } = "MP";
    public int CostEnd { get; set; }
    public int CostChargeWeapon { get; set; }
    public int CostChargeArmor { get; set; }
    public int PolishChargeWeapon { get; set; }
    public int PenaltyNoCastingTimeSucc { get; set; }

    // 驱散相关
    public string DispelCategory { get; set; } = string.Empty;
    public int RequiredDispelLevel { get; set; }

    // 目标相关
    public string TargetSlot { get; set; } = "none";
    public int TargetStop { get; set; }
    public string HostileType { get; set; } = "none";
    public int DelayType { get; set; }
    public int DelayTime { get; set; }
    public int TargetMaxcount { get; set; } = 4;
    public string FirstTarget { get; set; } = "Me";
    public int FirstTargetValidDistance { get; set; } = 6;
    public string TargetRange { get; set; } = string.Empty;
    public string TargetRangeAreaType { get; set; } = "FireBall";
    public int TargetRangeOpt1 { get; set; } = 8;
    public int TargetRangeOpt3 { get; set; } = 4;
    public string TargetSpeciesRestriction { get; set; } = "All";
    public string TargetRelationRestriction { get; set; } = "Enemy";
    public int TargetMarkerRadius { get; set; }

    // 行为控制
    public int CancelRate { get; set; }
    public int Obstacle { get; set; } = 4;
    public int AddWpnRange { get; set; }
    public int CastingDelay { get; set; }
    public int MoveCasting { get; set; } = 1;
    public int InstantSkill { get; set; }
    public string AutoAttack { get; set; } = "stop";
    public int PeaceSkill { get; set; }
    public int ShowWeapon { get; set; }

    // 动作和动画
    public string MotionName { get; set; } = string.Empty;
    public int MotionPlaySpeed { get; set; } = 50;

    // 特效相关
    public string CastFx { get; set; } = string.Empty;
    public string CastFxBone { get; set; } = string.Empty;
    public string CastcancelFx { get; set; } = string.Empty;
    public string CastcancelFxBone { get; set; } = string.Empty;
    public string FireFx { get; set; } = string.Empty;
    public string FireFxBone { get; set; } = string.Empty;
    public string HitFx { get; set; } = string.Empty;
    public string HitFxEx1 { get; set; } = string.Empty;
    public string HitFxBone { get; set; } = string.Empty;
    public string StatusFx { get; set; } = string.Empty;
    public string StatusFxBone { get; set; } = string.Empty;
    public string CastFxc { get; set; } = string.Empty;
    public string FireFxc { get; set; } = string.Empty;
    public string HitFxc { get; set; } = string.Empty;

    // 效果1 - 完整属性
    public string Effect1Type { get; set; } = string.Empty;
    public int Effect1NoResist { get; set; }
    public int Effect1Remain1 { get; set; }
    public int Effect1Remain2 { get; set; }
    public int Effect1EffectId { get; set; }
    public int Effect1HideMsg { get; set; }
    public string Effect1TargetType { get; set; } = string.Empty;
    public int Effect1Reserved1 { get; set; }
    public int Effect1Reserved2 { get; set; }
    public int Effect1Reserved3 { get; set; }
    public int Effect1Reserved4 { get; set; }
    public int Effect1Reserved6 { get; set; }
    public int Effect1Reserved7 { get; set; }
    public int Effect1Reserved9 { get; set; }
    public string Effect1Reserved10 { get; set; } = string.Empty;
    public int Effect1Reserved11 { get; set; }
    public string Effect1Reserved13 { get; set; } = string.Empty;
    public string Effect1Reserved14 { get; set; } = string.Empty;
    public int Effect1CriticalProbMod1 { get; set; } = 100;

    // 效果2 - 完整属性
    public string Effect2Type { get; set; } = "DispelBuff";
    public int Effect2NoResist { get; set; } = 1;
    public string Effect2TargetType { get; set; } = "Target_Only";
    public string Effect2CondPreeffect { get; set; } = "e1";
    public int Effect2Reserved1 { get; set; }
    public int Effect2Reserved2 { get; set; } = 3;
    public string Effect2Reserved10 { get; set; } = "earth";
    public int Effect2Reserved11 { get; set; } = 100;
    public int Effect2Reserved12 { get; set; } = 120;
    public int Effect2Reserved15 { get; set; }
    public int Effect2Reserved16 { get; set; } = 10;
    public int Effect2Reserved17 { get; set; }
    public int Effect2Reserved18 { get; set; } = 10;
    public int Effect2CriticalProbMod2 { get; set; } = 100;

    public string TypeDisplay => $"{Type} - {SubType}";
    public string ActivationDisplay => ActivationAttribute;
    public string DisplayName => string.IsNullOrEmpty(Name) ? $"技能_{Id}" : Name;
    public string EffectSummary => !string.IsNullOrEmpty(Effect1Type) ? Effect1Type : "无效果";
}